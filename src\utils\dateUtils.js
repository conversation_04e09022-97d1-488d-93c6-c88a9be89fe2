/**
 * Utility functions for date formatting
 */

/**
 * Format date to DD/MM/YYYY format
 * @param {string|Date} dateStr - Date string or Date object
 * @returns {string} Formatted date string in DD/MM/YYYY format
 */
export const formatDate = (dateStr) => {
  if (!dateStr) return '';
  
  // If it's already in DD/MM/YYYY format, return as is
  if (typeof dateStr === 'string' && /^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
    return dateStr;
  }
  
  const d = new Date(dateStr);
  if (isNaN(d)) return dateStr;
  
  // Format to DD/MM/YYYY
  const day = d.getDate();
  const month = d.getMonth() + 1;
  const year = d.getFullYear();
  
  return `${day}/${month}/${year}`;
};

/**
 * Format date to DD/MM/YYYY format with fallback text
 * @param {string|Date} dateStr - Date string or Date object
 * @param {string} fallback - Fallback text when date is invalid
 * @returns {string} Formatted date string or fallback
 */
export const formatDateWithFallback = (dateStr, fallback = 'N/A') => {
  if (!dateStr) return fallback;
  
  const formatted = formatDate(dateStr);
  return formatted || fallback;
}; 