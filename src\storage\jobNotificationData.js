// <PERSON><PERSON> liệu thông báo công việc
export const jobNotificationData = [
  {
    id: 1,
    title: "Công việc mới được giao",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "assignment",
    priority: "high",
    projectName: "Dự án Website",
    assignedBy: "<EMAIL>"
  },
  {
    id: 2,
    title: "Deadline sắp đến",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "deadline",
    priority: "high",
    projectName: "Dự án Mobile App",
    assignedBy: "<EMAIL>",
    dueDate: "2025-01-07"
  },
  {
    id: 3,
    title: "Công việc đã hoàn thành",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: true,
    type: "completed",
    priority: "medium",
    projectName: "Dự án ERP",
    assignedBy: "<EMAIL>"
  },
  {
    id: 4,
    title: "<PERSON><PERSON><PERSON> c<PERSON>u chỉnh sửa",
    message: "Đã hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "revision",
    priority: "medium",
    projectName: "Dự án Branding",
    assignedBy: "<EMAIL>"
  },
  {
    id: 5,
    title: "Công việc bị trì hoãn",
    message: "Đã hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: true,
    type: "delayed",
    priority: "low",
    projectName: "Dự án E-commerce",
    assignedBy: "<EMAIL>"
  },
  {
    id: 6,
    title: "Công việc được cập nhật",
    message: "Mô tả công việc 'Kiểm thử hệ thống' đã được cập nhật",
    time: "2 ngày trước",
    isRead: true,
    type: "updated",
    priority: "medium",
    projectName: "Dự án Testing",
    updatedBy: "Hoàng Văn E"
  }
];

// Hàm lọc thông báo theo trạng thái
export const filterJobNotifications = (notifications, filter) => {
  switch (filter) {
    case 'unread':
      return notifications.filter(notif => !notif.isRead);
    case 'read':
      return notifications.filter(notif => notif.isRead);
    case 'all':
    default:
      return notifications;
  }
};

// Hàm đánh dấu đã đọc
export const markJobNotificationAsRead = (notificationId) => {
  const notification = jobNotificationData.find(notif => notif.id === notificationId);
  if (notification) {
    notification.isRead = true;
  }
};

// Hàm đánh dấu tất cả đã đọc
export const markAllJobNotificationsAsRead = () => {
  jobNotificationData.forEach(notification => {
    notification.isRead = true;
  });
};

// Hàm đánh dấu tất cả chưa đọc
export const markAllJobNotificationsAsUnread = () => {
  jobNotificationData.forEach(notification => {
    notification.isRead = false;
  });
};
