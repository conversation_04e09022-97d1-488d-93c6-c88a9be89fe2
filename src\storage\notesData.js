// Mock data cho ghi chú cá nhân
export const mockNotesData = [
  {
    id: 1,
    title: 'Tiêu đề',
    content: 'Làm phần header rồi đến thanh sidebar. Đảm bảo responsive',
    date: new Date().toISOString(),
  },
  {
    id: 2,
    title: '<PERSON>hi chú về dự án',
    content: 'Cần hoàn thành phần notification system với popup dropdown và tab layout',
    date: new Date(Date.now() - 86400000).toISOString(), // 1 ngày trước
  },
  {
    id: 3,
    title: 'Meeting notes',
    content: 'Thảo luận về UI/UX improvements cho documents page và members page',
    date: new Date(Date.now() - 172800000).toISOString(), // 2 ngày trước
  }
];

// Utility functions để làm việc với localStorage
export const NOTES_STORAGE_KEY = 'personal_notes';

export const getNotesFromStorage = () => {
  try {
    const storedNotes = localStorage.getItem(NOTES_STORAGE_KEY);
    if (storedNotes) {
      return JSON.parse(storedNotes);
    }
    // Nếu chưa có data trong localStorage, trả về mock data
    return mockNotesData;
  } catch (error) {
    console.error('Error loading notes from storage:', error);
    return mockNotesData;
  }
};

export const saveNotesToStorage = (notes) => {
  try {
    localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(notes));
  } catch (error) {
    console.error('Error saving notes to storage:', error);
  }
};

export const clearNotesStorage = () => {
  try {
    localStorage.removeItem(NOTES_STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing notes storage:', error);
  }
};
