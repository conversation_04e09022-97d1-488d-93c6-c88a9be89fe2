import React from 'react';
import '../styles/LogoutConfirmation.css';


const LogoutConfirmation = ({ 
  show, 
  onConfirm, 
  onClose,
  message = "Phiên đăng nhập của bạn đã bị vô hiệu hóa do có người khác đăng nhập từ thiết bị khác.",
  title = "Thông báo đăng xuất"
}) => {
  if (!show) return null;

  const handleConfirm = () => {
    // Xóa token và chuyển hướng về trang đăng nhập
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');
    localStorage.removeItem('user');
    onConfirm();
    window.location.href = '/login';
  };

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('logout-confirmation-overlay')) {
      if (onClose) onClose();
    }
  };

  return (
    <div className="logout-confirmation-overlay" onClick={handleOverlayClick}>
      <div className="logout-confirmation-modal">
        <div className="logout-confirmation-header">
          <h3>{title}</h3>
        </div>
        <div className="logout-confirmation-body">
          <p>{message}</p>
        </div>
        <div className="logout-confirmation-actions">
          <button 
            className="btn-confirm"
            onClick={handleConfirm}
          >
            Đăng xuất
          </button>
        </div>
      </div>
    </div>
  );
};

export default LogoutConfirmation; 