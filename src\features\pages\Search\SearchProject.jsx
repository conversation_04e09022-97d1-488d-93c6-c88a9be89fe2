import React, { useEffect, useState } from "react";
import { searchAll } from "../../../api/search";
import DeploymentIcon from "../../../assets/deployment.svg";

const badgeStyle = (bg, color) => ({
  background: bg,
  color,
  borderRadius: 12,
  fontWeight: 500,
  fontSize: 15,
  padding: "4px 16px",
  display: "inline-block",
  minWidth: 70,
  textAlign: "center",
});

const labelStyle = {
  fontWeight: 600,
  fontSize: 17,
  marginBottom: 2,
};

const subLabelStyle = {
  color: "#888",
  fontSize: 14,
  marginBottom: 2,
};

const cellTitle = {
  fontWeight: 500,
  fontSize: 15,
  marginBottom: 2,
};

const cellValue = {
  color: "#888",
  fontSize: 14,
};

const statusToVietnamese = (status) => {
  switch (status) {
    case 'completed': return '<PERSON>àn thành';
    case 'in-progress':
    case 'in_progress': return 'Đang triển khai';
    case 'waiting':
    case 'pending': return 'Đang chờ';
    case 'overdue': return 'Quá hạn';
    case 'review':
    case 'consider': return 'Đang xem xét';
    default: return status;
  }
};

const Project = ({ searchValue, onCountChange }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!searchValue) return;
    setLoading(true);
    searchAll(searchValue)
      .then((res) => {
        const group = (res.data?.groups || []).find(g => g.type === "project");
        setResults(group ? group.items : []);
      })
      .catch(() => setResults([]))
      .finally(() => setLoading(false));
  }, [searchValue]);

  useEffect(() => {
    if (onCountChange) onCountChange(results.length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [results]);

  if (loading) return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Đang tìm kiếm...</div>;
  if (!results.length) {
    return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Không tìm thấy dự án cho "{searchValue}"</div>;
  }
  return (
    <div style={{ background: "#fff", padding: 0, borderRadius: 12 }}>
      {results.map((project, idx) => (
        <div
          key={project.id || idx}
          style={{
            border: "1px solid #f3f3f3",
            borderRadius: 12,
            marginBottom: 12,
            padding: 0,
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "stretch",
              padding: "20px 24px",
              gap: 0,
            }}
          >
            <div style={{ flex: 2 }}>
              <div style={labelStyle}>{project.name}</div>
              <div style={subLabelStyle}>Mã dự án: {project.projectCode || project.code || project.id}</div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Thời gian</div>
              <div style={cellValue}>{project.time ? `${new Date(project.time.start).toLocaleDateString()} - ${new Date(project.time.end).toLocaleDateString()}` : ""}</div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Trưởng nhóm</div>
              <div style={cellValue}>{project.leader?.fullName || "-"}</div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Trạng thái</div>
              <div
                style={{
                  color: "#4a90e2",
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <img
                  src={DeploymentIcon}
                  alt="Đang tiến hành"
                  style={{ width: 18, height: 18, marginRight: 4 }}
                />
                {statusToVietnamese(project.status) || "Đang triển khai"}
              </div>
            </div>
            <div
              style={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
              }}
            >
              <span style={badgeStyle("#eaf3ff", "#4a90e2")}>Dự án</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Project;