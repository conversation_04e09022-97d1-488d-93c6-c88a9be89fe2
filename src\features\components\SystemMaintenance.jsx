import React, { useState, useEffect } from 'react';
import '../../styles/SystemMaintenance.css';
import '../../styles/HRLayout.css';
import { showSuccess, showError } from '../../utils/toastUtils';
import { getMaintenanceStatus, toggleMaintenanceMode } from '../../api/admin';
import MaintenanceIcon from '../../assets/deployment.svg';
import RefreshIcon from '../../assets/refresh-ccw.svg';

const SystemMaintenance = () => {
  const [maintenanceStatus, setMaintenanceStatus] = useState({
    enabled: false,
    message: '',
    enabledAt: null,
    enabledBy: null,
    estimatedDowntime: ''
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [customMessage, setCustomMessage] = useState('');
  const [estimatedDowntime, setEstimatedDowntime] = useState('');



  // L<PERSON>y trạng thái bảo trì hiện tại
  const fetchMaintenanceStatus = async () => {
    try {
      setLoading(true);
      const result = await getMaintenanceStatus();
      
      if (result.success) {
        const maintenance = result.data?.maintenance || {};
        setMaintenanceStatus({
          enabled: Boolean(maintenance.enabled),
          message: maintenance.message || '',
          enabledAt: maintenance.enabledAt || null,
          enabledBy: maintenance.enabledBy || null,
          estimatedDowntime: maintenance.estimatedDowntime || ''
        });
        setCustomMessage(maintenance.message || '');
        setEstimatedDowntime(maintenance.estimatedDowntime || '');
      } else {
        showError(result.message || 'Không thể lấy trạng thái bảo trì');
      }
    } catch (error) {
      console.error('Error fetching maintenance status:', error);
      showError('Lỗi kết nối khi lấy trạng thái bảo trì');
    } finally {
      setLoading(false);
    }
  };

  // Bật/tắt chế độ bảo trì
  const toggleMaintenance = async () => {
    try {
      setUpdating(true);
      
      const result = await toggleMaintenanceMode({
        enabled: !maintenanceStatus.enabled,
        message: customMessage.trim() || 'Hệ thống đang được bảo trì. Vui lòng quay lại sau.',
        estimatedDowntime: estimatedDowntime.trim() || null
      });
      
      if (result.success) {
        const maintenance = result.data?.maintenance || {};
        setMaintenanceStatus({
          enabled: Boolean(maintenance.enabled),
          message: maintenance.message || '',
          enabledAt: maintenance.enabledAt || null,
          enabledBy: maintenance.enabledBy || null,
          estimatedDowntime: maintenance.estimatedDowntime || ''
        });
        showSuccess(
          `${maintenance.enabled ? 'Bật' : 'Tắt'} chế độ bảo trì thành công`
        );
      } else {
        showError(result.message || 'Có lỗi xảy ra khi thay đổi trạng thái bảo trì');
      }
    } catch (error) {
      console.error('Error toggling maintenance:', error);
      showError('Lỗi kết nối khi thay đổi trạng thái bảo trì');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    fetchMaintenanceStatus();
  }, []);

  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Ho_Chi_Minh'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  if (loading) {
    return (
      <div className="hr-layout-container">
        <div className="hr-layout-title-row">
          <img src={MaintenanceIcon} alt="icon" style={{ width: 24, height: 24, color: '#2d5be3' }} />
          <div>
            <div className="hr-layout-title">Bảo trì hệ thống</div>
            <div className="hr-layout-desc">Đang tải trạng thái bảo trì...</div>
          </div>
        </div>
        <div className="system-maintenance-loading">
          <div className="spinner"></div>
          <p>Đang tải trạng thái bảo trì...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header Layout giống HRLayout */}
      <div className="hr-layout-container">
        <div className="hr-layout-title-row">
          <img src={MaintenanceIcon} alt="icon" style={{ width: 24, height: 24, color: '#2d5be3' }} />
          <div>
            <div className="hr-layout-title">Bảo trì hệ thống</div>
            <div className="hr-layout-desc">Kiểm soát chế độ bảo trì và thông báo cho người dùng</div>
          </div>
        </div>

        {/* Toolbar Actions */}
        <div className="hr-toolbar-row">
          <div className="hr-toolbar-left">
            <div className={`status-indicator ${maintenanceStatus.enabled ? 'active' : 'inactive'}`}>
              <div className="status-dot"></div>
              <span className="status-text">
                {maintenanceStatus.enabled ? 'Đang bảo trì' : 'Hoạt động bình thường'}
              </span>
            </div>
          </div>
          
          <div className="hr-toolbar-actions">
            <button 
              className="hr-toolbar-btn"
              onClick={fetchMaintenanceStatus}
              disabled={updating || loading}
            >
              <img src={RefreshIcon} alt="refresh" style={{ width: 18, height: 18 }} />
              {loading ? 'Đang tải...' : 'Làm mới'}
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="system-maintenance-content">
        {maintenanceStatus.enabled && (
          <div className="maintenance-info-card">
            <h3>Thông tin bảo trì hiện tại</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Thời gian bắt đầu:</label>
                <span>{formatDateTime(maintenanceStatus.enabledAt)}</span>
              </div>
              {maintenanceStatus.estimatedDowntime && (
                <div className="info-item">
                  <label>Thời gian dự kiến:</label>
                  <span>{maintenanceStatus.estimatedDowntime}</span>
                </div>
              )}
              <div className="info-item">
                <label>Thông báo:</label>
                <span>{maintenanceStatus.message}</span>
              </div>
            </div>
          </div>
        )}

        <div className="maintenance-controls-card">
          <h3>Cấu hình bảo trì</h3>
          
          <div className="control-group">
            <label htmlFor="maintenance-message">Thông báo bảo trì:</label>
            <textarea
              id="maintenance-message"
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Hệ thống đang được bảo trì. Vui lòng quay lại sau."
              rows={3}
              disabled={updating}
            />
          </div>

          <div className="control-group">
            <label htmlFor="estimated-downtime">Thời gian dự kiến:</label>
            <input
              type="text"
              id="estimated-downtime"
              value={estimatedDowntime}
              onChange={(e) => setEstimatedDowntime(e.target.value)}
              placeholder="Ví dụ: 30 phút, 2 giờ, v.v."
              disabled={updating}
            />
          </div>

          <div className="maintenance-actions">
            <button
              className={`maintenance-toggle-btn ${maintenanceStatus.enabled ? 'enabled' : 'disabled'}`}
              onClick={toggleMaintenance}
              disabled={updating}
            >
              {updating ? (
                <>
                  <div className="button-spinner"></div>
                  Đang xử lý...
                </>
              ) : (
                <>
                  {maintenanceStatus.enabled ? 'Tắt bảo trì' : 'Bật bảo trì'}
                </>
              )}
            </button>
          </div>
        </div>

        <div className="maintenance-warning-card">
          <div className="warning-icon">⚠️</div>
          <div className="warning-content">
            <strong>Lưu ý quan trọng:</strong>
            <ul>
              <li>Khi bật chế độ bảo trì, tất cả người dùng (trừ admin) sẽ không thể truy cập hệ thống</li>
              <li>Chỉ admin mới có thể truy cập và quản lý hệ thống trong thời gian bảo trì</li>
              <li>Hãy đảm bảo hoàn thành công việc bảo trì trước khi tắt chế độ này</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemMaintenance; 