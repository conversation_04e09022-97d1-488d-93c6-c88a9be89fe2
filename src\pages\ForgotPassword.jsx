// Import các thư viện và component cần thiết
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { forgotPassword } from '../api/auth.js'; // Hàm gọi API quên mật khẩu
import '../styles/Forgot.css';
import '../index.css';
import { showSuccess, showError } from '../components/Toastify'; // Thông báo toast
import { validateForgotPasswordForm } from '../utils/validation'; // Hàm validate form
import bgLogin from '../assets/login/bgLogin.jpg'; // Ảnh nền
import logo from '../assets/login/dau.svg'; // Logo
import arrowLeft from '../assets/arrowleft.svg'; // Icon mũi tên trái
import arrowRight from '../assets/arrowright.svg'; // Icon mũi tên phải

const ForgotPassword = () => {
  // State lưu giá trị email nhập vào
  const [email, setEmail] = useState('');
  // State lưu thông báo thành công
  const [message, setMessage] = useState('');
  // State lưu thông báo lỗi
  const [error, setError] = useState('');
  // State loading khi gửi form
  const [loading, setLoading] = useState(false);
  // State lưu lỗi validate
  const [errors, setErrors] = useState({});
  // Hook điều hướng
  const navigate = useNavigate();

  // Hàm xử lý thay đổi email, kiểm tra hợp lệ realtime
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    const validationErrors = validateForgotPasswordForm({ email: e.target.value });
    setErrors((prev) => ({ ...prev, email: validationErrors.email }));
  };

  // Hàm xử lý submit form quên mật khẩu
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');
    const validationErrors = validateForgotPasswordForm({ email });
    setErrors(validationErrors);
    if (validationErrors.email) {
      return;
    }
    setLoading(true);
    try {
      await forgotPassword(email); // Gọi API gửi email quên mật khẩu
      setMessage('Đã gửi email đặt lại mật khẩu!'); // Hiện thông báo thành công trên form
      setError('');
      showSuccess('Đã gửi email đặt lại mật khẩu!'); // Hiện toast thành công
    } catch (err) {
      setError(err.message || 'Gửi email thất bại.'); // Hiện lỗi trên form
      setMessage('');
      showError(err.message || 'Gửi email thất bại.'); // Hiện toast lỗi
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="forgot-container">
      {/* Bên trái: Ảnh minh hoạ */}
      <div className="forgot-left">
        <img 
          src={bgLogin} 
          alt="Forgot Illustration" 
          className="forgot-illustration"
        />
      </div>
      {/* Bên phải: Form quên mật khẩu */}
      <div className="forgot-right">
        <div className="forgot-form-wrapper">
          <div className="forgot-back-row">
            {/* Nút quay lại trang đăng nhập */}
            <a href="#" className="forgot-back-link"
            onClick={e => { e.preventDefault();
            navigate('/login'); }}>
            <img src={arrowLeft} alt="Arrow Left" style={{width: 14, height: 14, marginRight: 8, verticalAlign: 'middle'}} />
            Quay lại
            </a>
          </div>
          {/* Logo */}
          <img src={logo} alt="Logo" className="forgot-logo" style={{width:60, height:60, marginBottom:28}} />
          <h2 className="forgot-title">Quên mật khẩu</h2>
          <p className="forgot-desc">Vui lòng nhập email để đặt lại mật khẩu</p>
          <form className="forgot-form" onSubmit={handleSubmit}>
            <label className="forgot-label">Email</label>
            <div className="forgot-input-validation-wrapper">
              <input
                type="email"
                className={`forgot-input${errors.email ? ' forgot-input-error' : ''}`}
                placeholder="Nhập email của bạn"
                value={email}
                onChange={handleEmailChange}
                autoComplete="off"
                onInvalid={e => e.preventDefault()}
              />
              {/* Hiển thị lỗi validate email nếu có */}
              {errors.email && <div className="forgot-validation-error-message">{errors.email}</div>}
            </div>
            {/* Hiển thị thông báo thành công nếu có */}
            {message && <div className="forgot-success">{message}</div>}
            <button type="submit" className="forgot-btn" disabled={loading}>
              {loading ? 'Đang gửi...' : (
                <>
                  Gửi liên kết đặt lại
                  <img src={arrowRight} alt="Arrow Right" style={{width: 14, height: 14, marginLeft: 8, verticalAlign: 'middle', filter: 'brightness(0) invert(1)'}} />
                </>
              )}
            </button>
          </form>
          
        </div>
      </div>
      {/* Toast được hiển thị thông qua ToastContainer trong App.jsx */}
    </div>
  );
};

export default ForgotPassword; // Export component để sử dụng ở nơi khác
