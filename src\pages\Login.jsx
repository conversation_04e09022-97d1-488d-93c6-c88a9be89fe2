// Import các thư viện và component cần thiết
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/Login.css';
import '../index.css'

import { showSuccess, showError } from '../components/Toastify'; // Thông báo toast
import { validateLoginForm } from '../utils/validation'; // Hàm kiểm tra hợp lệ form
import bgLogin from '../assets/login/bgLogin.jpg'; // Ảnh nền
import logo from '../assets/login/dau.svg'; // Logo
import { login as loginApi } from '../api/auth'; // Import hàm login API

const Login = () => {
  // State lưu giá trị email nhập vào
  const [email, setEmail] = useState('');
  // State lưu giá trị mật khẩu nhập vào
  const [password, setPassword] = useState('');
  // State hiển thị/ẩn mật khẩu
  const [showPassword, setShowPassword] = useState(false);
  // State lưu trạng thái checkbox "Ghi nhớ"
  const [remember, setRemember] = useState(false);
  // State loading khi gửi form
  const [loading, setLoading] = useState(false);
  // State lưu lỗi validate
  const [errors, setErrors] = useState({});
  // Hook điều hướng
  const navigate = useNavigate();

  // Hàm kiểm tra hợp lệ email khi nhập
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    const validationErrors = validateLoginForm({ email: e.target.value, password });
    setErrors((prev) => ({ ...prev, email: validationErrors.email }));
  };
  // Hàm kiểm tra hợp lệ mật khẩu khi nhập
  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    const validationErrors = validateLoginForm({ email, password: e.target.value });
    setErrors((prev) => ({ ...prev, password: validationErrors.password }));
  };

  return (
    <div className="forgot-container">
      {/* Bên trái: Ảnh minh hoạ */}
      <div className="forgot-left">
        <img 
          src={bgLogin} 
          alt="Login Illustration" 
          className="forgot-illustration"
        />
      </div>
      {/* Bên phải: Form đăng nhập */}
      <div className="forgot-right">
        <div className="forgot-form-wrapper">
          {/* Logo */}
          <img src={logo} alt="Logo" className="forgot-logo" style={{width:60, height:60, marginBottom:28}} />
          <h2 className="forgot-title">ỨNG DỤNG QUẢN LÍ DỰ ÁN</h2>
          <p className="forgot-desc">Đăng nhập để tiếp tục quản lí dự án</p>
          <form className="forgot-form" 
            onSubmit={async (e) => {
              e.preventDefault();
              setLoading(true);
              const validationErrors = validateLoginForm({ email, password });
              setErrors(validationErrors);
              if (validationErrors.email || validationErrors.password) {
                setLoading(false);
                return;
              }
              try {
                // Gọi API login thực tế
                const res = await loginApi(email, password);
                if (res.success) {
                  localStorage.setItem('token', res.data.token);
                  localStorage.setItem('user', JSON.stringify(res.data.user));
                  showSuccess('Đăng nhập thành công!');
                  setTimeout(() => navigate('/'), 500);
                } else {
                  showError(res.message || 'Sai tài khoản hoặc mật khẩu!');
                }
              } catch (err) {
                showError(err.message || 'Sai tài khoản hoặc mật khẩu!');
              } finally {
                setLoading(false);
              }
            }}>
            <label className="forgot-label">Email</label>
            <div className="forgot-input-validation-wrapper">
              <input type="email" 
                placeholder="Nhập email của bạn" 
                value={email} 
                onChange={handleEmailChange} 
                autoComplete="off"
                onInvalid={e => e.preventDefault()}
                className={`forgot-input${errors.email ? ' forgot-input-error' : ''}`}
              />
              {/* Hiển thị lỗi validate email nếu có */}
              {errors.email && <div className="forgot-validation-error-message">{errors.email}</div>}
            </div>
            <label className="forgot-label">Mật khẩu</label>
            <div className="forgot-input-validation-wrapper" style={{ position: 'relative', width: '100%' }}>
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Nhập mật khẩu của bạn"
                value={password}
                onChange={handlePasswordChange}
                className={`forgot-input${errors.password ? ' forgot-input-error' : ''}`}
                style={{
                  paddingRight: 38,
                  boxSizing: 'border-box',
                  fontSize: '1rem'
                }}
              />
              {/* Hiển thị lỗi validate mật khẩu nếu có */}
              {errors.password && <div className="forgot-validation-error-message">{errors.password}</div>}
              {/* Nút hiện/ẩn mật khẩu */}
              <button
                type="button"
                className="pm-password-toggle"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}
                tabIndex={0}
                style={{
                  position: 'absolute',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  padding: 0,
                  cursor: 'pointer',
                  height: 24,
                  width: 24,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <span className="material-icons" style={{fontSize:20, color:'#bdbdbd'}}>
                  {showPassword ? 'visibility_off' : 'visibility'}
                </span>
              </button>
            </div>
            {/* Hàng ghi nhớ và quên mật khẩu */}
            <div className="pm-remember-row" style={{marginBottom:16}}>
              <label style={{display:'flex',alignItems:'center',fontWeight:400,fontSize:'0.98rem',color:'#222'}}>
                <input type="checkbox" checked={remember} onChange={e => setRemember(e.target.checked)} style={{marginRight:6}} />
                Ghi nhớ
              </label>
              <div style={{flex:1}}></div>
              {/* Link quên mật khẩu */}
              <a href="#" className="pm-forgot-link" 
                onClick={e => {e.preventDefault(); 
                navigate('/forgotpassword')}}>Quên mật khẩu ?</a>
            </div>
            <button type="submit" className="forgot-btn" disabled={loading}>
              {loading ? 'Đang đăng nhập...' : 'Đăng nhập'}
            </button>
          </form>
          {/* <div className="pm-register-link">
            Bạn chưa có tài khoản? <a href="#" 
              onClick={e => {e.preventDefault(); 
              navigate('/register')}}>Đăng kí ngay</a>
          </div> */}
        </div>
      </div>
      {/* Toast được hiển thị thông qua ToastContainer trong App.jsx */}
    </div>
  );
};

export default Login; // Export component để sử dụng ở nơi khác