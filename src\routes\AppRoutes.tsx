// Import các thư viện và component cần thiết
import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useOutletContext, useSearchParams, useNavigate } from 'react-router-dom';
import Login from '../pages/Login.jsx'; // Trang đăng nhập

import ForgotPassword from '../pages/ForgotPassword.jsx'; // Trang quên mật khẩu
import ResetPassword from '../pages/ResetPassword.jsx'; // Trang đặt lại mật khẩu
import Home from '../pages/Home.jsx'; // Trang dashboard chính

// Các trang thuộc ProjectCompany
import WorkContent from '../features/components/WorkContent.jsx'; // Component chứa header/navigation công việc

// Các view cho công việc (component)
import Lists from '../features/pages/ProjectCompany/ListJobBig.jsx'; // <PERSON><PERSON> sách công việc
import Kanban from '../features/pages/ProjectCompany/Kanban.jsx'; // Bảng Kanban
import Timeline from '../features/pages/ProjectCompany/Timeline.jsx'; // Dòng thời gian
import Charts from '../features/pages/ProjectCompany/Charts.jsx'; // Biểu đồ
import Documents from '../features/pages/ProjectCompany/Documents.jsx'; // Tài liệu dự án
import Procedure from '../features/pages/Statistical/Procedure';
import StatisticsPlaceholder from '../features/pages/Statistical/StatisticsPlaceholder';

// Import DashboardLayout
import DashboardLayout from '../features/components/DashboardLayout.jsx';

import PersonalNotes from '../features/pages/Workspace/PersonalNotes.jsx'; // Trang ghi chú cá nhân
import MyJob from '../features/pages/Workspace/MyJob.jsx'; // Trang công việc của tôi
import TrashPage from '../pages/TrashPage.jsx'; // Trang thùng rác
import HRPage from '../pages/HRPage'; // Trang quản lý nhân sự
import Log from '../features/pages/ActivityLog/Log.jsx'; // Trang nhật ký hoạt động
import ProtectedRoute from '../components/ProtectedRoute'; // Component bảo vệ route
import MembersPage from '../pages/MembersPage'; // Trang thành viên
import DepartmentList from '../features/pages/HR/DepartmentList';
import DepartmentDetail from '../features/pages/HR/DepartmentDetail';
import SystemMaintenance from '../features/components/SystemMaintenance.jsx'; // Component quản lý bảo trì


// Wrapper để truyền props cho Lists từ context
function ListsWithContext() {
    // Lấy context từ Outlet (ví dụ: sortBy, filters)
    const context = useOutletContext<{ sortBy: any; filters: any }>();
    // Trả về component Lists với các props được truyền từ context
    return <Lists sortOption={context?.sortBy} filterOptions={context?.filters} />;
}

// Component để check token và redirect đến ResetPassword nếu cần
function HomeWithTokenCheck() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();

    useEffect(() => {
        const token = searchParams.get('token');
        if (token) {
            // Nếu có token trong query params, redirect đến reset password
            navigate(`/reset-password/${token}`, { replace: true });
        }
    }, [searchParams, navigate]);

    return <Home />;
}

const AppRoutes = () => {
    return (
        <Routes>
            {/* Trang dashboard chính - yêu cầu đăng nhập */}
            <Route path="/" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<HomeWithTokenCheck />} />
            </Route>
            {/* Trang đăng nhập - không cần bảo vệ */}
            <Route path="/login" element={<Login />} />
            {/* Trang quên mật khẩu - không cần bảo vệ */}
            <Route path="/forgotpassword" element={<ForgotPassword />} />
            {/* Trang đặt lại mật khẩu - không cần bảo vệ */}
            <Route path="/resetpassword" element={<ResetPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />

            {/* Route cho bảo trì hệ thống - chỉ admin */}
            <Route path="/system-maintenance" element={
              <ProtectedRoute requiredRoles={['admin']} redirectTo="/">
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<SystemMaintenance />} />
            </Route>

            {/* Route chính cho công việc của tôi - yêu cầu đăng nhập */}
            <Route path="/myjob" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<MyJob />} />
            </Route>

            <Route path="/team/tasks-old" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
                {/* Mặc định vào sẽ là trang công việc */}
                <Route index element={<Navigate to="list" replace />} />
                <Route path="" element={<WorkContent />}>
                    <Route path="list" element={<ListsWithContext />} />
                    <Route path="kanban" element={<Kanban />} />
                    <Route path="timeline" element={<Timeline />} />
                    <Route path="chart" element={<Charts />} />
                </Route>
            </Route>

            {/* <Route path="/kanban" element={<Kanban />} /> */}

            {/* Route cho từng dự án cụ thể - yêu cầu đăng nhập */}
            <Route path="/projects/:projectId/work" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
                {/* Mặc định vào sẽ là trang công việc */}
                <Route index element={<Navigate to="list" replace />} />
                <Route path="" element={<WorkContent />}>
                    <Route path="list" element={<ListsWithContext />} />
                    <Route path="kanban" element={<Kanban />} />
                    <Route path="timeline" element={<Timeline />} />
                    <Route path="chart" element={<Charts />} />
                </Route>
            </Route>

            {/* Route cho tài liệu dự án - yêu cầu đăng nhập */}
            <Route path="/projects/:projectId/documents" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Documents />} />
            </Route>
            <Route path="/projects/chopchat/documents" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Documents />} />
            </Route>

            {/* Route cho thành viên trong dự án - yêu cầu đăng nhập */}
            <Route path="/projects/:projectId/team/members" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<MembersPage />} />
            </Route>
            <Route path="/members" element={<Navigate to="/" replace />} />

            {/* Route cho staff - yêu cầu đăng nhập */}
            <Route path="/staff/tasks" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
                <Route index element={<Navigate to="list" replace />} />
                <Route path="" element={<WorkContent />}>
                    <Route path="list" element={<ListsWithContext />} />
                    <Route path="kanban" element={<Kanban />} />
                    <Route path="timeline" element={<Timeline />} />
                    <Route path="chart" element={<Charts />} />
                </Route>
            </Route>
            {/* Route cho leader - yêu cầu đăng nhập */}
            <Route path="/leader/tasks" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
                <Route index element={<Navigate to="list" replace />} />
                <Route path="" element={<WorkContent />}>
                    <Route path="list" element={<ListsWithContext />} />
                    <Route path="kanban" element={<Kanban />} />
                    <Route path="timeline" element={<Timeline />} />
                    <Route path="chart" element={<Charts />} />
                </Route>
            </Route>
            {/* Route cho báo cáo & thống kê - admin, CEO, HR, Leader, departmentHead */}
            <Route path="/statistical" element={
              <ProtectedRoute requiredRoles={['admin', 'ceo', 'hr', 'leader', 'departmenthead']} redirectTo="/">
                <DashboardLayout />
              </ProtectedRoute>
            }>
                <Route index element={<Navigate to="reports" replace />} />
                <Route path="reports" element={<Procedure />} />
                <Route path="list" element={<StatisticsPlaceholder />} />
            </Route>
            {/* Thêm route cho ghi chú cá nhân - yêu cầu đăng nhập */}
            <Route path="/notes" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<PersonalNotes />} />
            </Route>
            {/* Trang thùng rác - yêu cầu đăng nhập */}
            <Route path="/trash" element={
              <ProtectedRoute>
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<TrashPage />} />
            </Route>
            {/* Route cho quản lý nhân sự - admin, CEO, HR, departmentHead */}
            <Route path="/hr" element={
              <ProtectedRoute requiredRoles={['admin', 'ceo', 'hr', 'departmenthead']} redirectTo="/">
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<HRPage />} />
            </Route>
            {/* Route cho nhật ký hoạt động - admin, CEO */}
            <Route path="/activity-log" element={
              <ProtectedRoute requiredRoles={['admin', 'ceo']} redirectTo="/">
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Log />} />
            </Route>
            {/* Route cho quản lý phòng ban - admin, CEO, HR, departmentHead */}
            <Route path="/departments" element={
              <ProtectedRoute requiredRoles={['admin', 'ceo', 'hr', 'departmenthead']} redirectTo="/">
                <DashboardLayout />
              </ProtectedRoute>
            }>
              <Route index element={<DepartmentList />} />
              <Route path=":id" element={<DepartmentDetail />} />
            </Route>
        </Routes>
    );
};

export default AppRoutes; // Export component định nghĩa các route cho ứng dụng