import FileIcon from "../assets/icon-sidebar/tailieu.svg";
import TeamIcon from "../assets/icon-sidebar/doingu.svg";
import TaskIcon from "../assets/icon-sidebar/congviec.svg";
import { getEndpointsByRole, getCurrentUserRole } from "../api/endpoints";

// Hàm lấy dữ liệu dự án từ API thật (KHÔNG dùng localStorage, không có dữ liệu giả)
export const fetchProjects = async () => {
  const role = getCurrentUserRole();
  const endpoints = getEndpointsByRole(role);
  const apiUrl = endpoints.ALL_PROJECTS || endpoints.PROJECTS;

  if (!apiUrl) return [];

  try {
    const token = localStorage.getItem('token') || localStorage.getItem('accessToken');
    const res = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token.replace(/['"]+/g, '')}` } : {})
      }
    });
    if (!res.ok) throw new Error('API error');
    const data = await res.json();
    // Map API data to Sidebar format
    return (Array.isArray(data.data) ? data.data : data).map((project) => ({
      id: project._id || project.id,
      name: project.name,
      description: project.description,
      type: 'project',
      hasSubmenu: true,
      isOpen: false,
      icon: 'hashtag',
      children: [
        {
          id: `${project._id || project.id}-documents`,
          name: 'Tài liệu',
          path: `/projects/${project._id || project.id}/documents`,
          icon: FileIcon,
          type: 'page'
        },
        {
          id: `${project._id || project.id}-team`,
          name: 'Đội ngũ',
          type: 'dropdown',
          icon: TeamIcon,
          isOpen: false,
          children: [
            {
              id: `${project._id || project.id}-team-members`,
              name: 'Thành viên',
              path: `/projects/${project._id || project.id}/team/members`,
              icon: TeamIcon,
              type: 'page'
            },
            {
              id: `${project._id || project.id}-team-work`,
              name: 'Công việc',
              path: `/projects/${project._id || project.id}/work/tasks`,
              icon: TaskIcon,
              type: 'page'
            }
          ]
        }
      ]
    }));
  } catch (err) {
    return [];
  }
};