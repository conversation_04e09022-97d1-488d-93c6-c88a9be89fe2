import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import "../../../styles/Chart.css";
import userAvatar from "../../../assets/user1.png";
import namechartIcon from "../../../assets/namechart.svg";
import chartPieIcon from "../../../assets/chart-pie.svg";
import clockIcon from "../../../assets/clock.svg";
import playIcon from "../../../assets/play.svg";
import tichIcon from "../../../assets/tich.svg";
import eyeIcon from "../../../assets/eye.svg";
import { getChartData } from "../../../storage/chartsData";
import {
  getTaskStatusStats,
  getProjectInfo,
} from "../../../api/taskManagement";

// Đăng ký các components Chart.js cần thiết
ChartJS.register(ArcElement, Tooltip, Legend);

const Charts = () => {
  const { projectId } = useParams();
  const [hoveredIdx, setHoveredIdx] = useState(null);
  const [tooltipPos, setTooltipPos] = useState({ x: 0, y: 0 });
  const [statusData, setStatusData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [projectInfo, setProjectInfo] = useState(null);
  const [members, setMembers] = useState([]);

  // Disable page scroll for this component
  useEffect(() => {
    const dashboardMain = document.querySelector('.dashboard-main');
    if (dashboardMain) {
      dashboardMain.classList.add('no-page-scroll');
    }

    return () => {
      if (dashboardMain) {
        dashboardMain.classList.remove('no-page-scroll');
      }
    };
  }, []);

  // Fetch real-time data
  useEffect(() => {
    const fetchData = async () => {
      // Check authentication
      const token =
        localStorage.getItem("token") || localStorage.getItem("authToken");
      const user = JSON.parse(localStorage.getItem("user") || "{}");

      if (!projectId) {
        // No projectId provided
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const [statsResponse, infoResponse] = await Promise.all([
            getTaskStatusStats(projectId),
            getProjectInfo(projectId),
          ]);

        const stats = statsResponse.data || statsResponse;
        const { project, members: projectMembers } = infoResponse;

        // Define status configuration
        const statusConfig = [
          { label: "Đang chờ", color: "#e0e0e0", key: "pending" },
          { label: "Đang tiến hành", color: "#00c2e0", key: "in_progress" },
          { label: "Hoàn thành", color: "#0074b7", key: "completed" },
          { label: "Quá hạn", color: "#ff4d4f", key: "overdue" },
          { label: "Xem xét", color: "#00004d", key: "review" },
        ];

        // Map real data to status configuration
        const updatedStatusData = statusConfig.map((status) => {
          const count = stats[status.key] || 0;
          return {
            ...status,
            value: count,
            taskCount: count,
          };
        });

          setStatusData(updatedStatusData);
        setProjectInfo(project);
        setMembers(projectMembers || []);
      } catch (error) {
        console.error("❌ Charts Error:", error);
        
        if (error.message.includes("không có quyền") || error.message.includes("403")) {
          setError("Bạn không có quyền xem dữ liệu của dự án này. Vui lòng liên hệ quản trị viên để được thêm vào dự án.");
        } else {
          setError("Không thể tải dữ liệu biểu đồ. Vui lòng thử lại sau.");
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // Get chart data from storage
  const chartData = getChartData(statusData);

  // Chart.js options
  const chartOptions = {
    responsive: true,
    cutout: "50%",
    maintainAspectRatio: true,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    elements: {
      arc: {
        borderWidth: 0,
        hoverOffset: 0,
        hoverBorderWidth: 0,
      },
    },
    onHover: (event, elements) => {
      if (elements && elements.length > 0) {
        const idx = elements[0].index;
        setHoveredIdx(idx);

        if (event && event.native) {
          const canvas = event.chart.canvas;
          const rect = canvas.getBoundingClientRect();
          setTooltipPos({
            x: event.native.clientX - rect.left,
            y: event.native.clientY - rect.top,
          });
        }
      } else {
        setHoveredIdx(null);
      }
    },
  };

  return (
    <div className="chart-container">
      <div className="chart-content">
        {/* Combined Card with Header, Chart, and Legend */}
        <div className="chart-card">
          {/* Header */}
          <div className="chart-header">
            <img
              src={namechartIcon}
              alt="chart"
              className="chart-header-icon"
            />
            Biểu đồ dự án{" "}
            <span className="chart-header-title">
              {projectInfo?.name || projectInfo?.projectCode || "SDTC"}
            </span>
          </div>

          {/* Chart Container */}
          <div className="chart-wrapper">
            <div className="chart-canvas">
              {loading ? (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: 200,
                  }}
                >
                  Đang tải dữ liệu...
                </div>
              ) : error ? (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    height: 200,
                    textAlign: "center",
                    padding: "20px",
                  }}
                >
                  <div
                    style={{
                      color: "#ef4444",
                      fontSize: "18px",
                      marginBottom: "8px",
                    }}
                  >
                    ⚠️ Lỗi truy cập
                  </div>
                  <div style={{ color: "#6b7280", fontSize: "14px" }}>
                    {error}
                  </div>
                </div>
              ) : statusData.length === 0 ? (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: 200,
                    color: "#6b7280",
                  }}
                >
                  Không có dữ liệu để hiển thị
                </div>
              ) : (
                <div>
                  <Doughnut data={chartData} options={chartOptions} />
                </div>
              )}
            </div>

            {/* Custom Tooltip */}
            {hoveredIdx !== null && (
              <div
                className="chart-tooltip"
                style={{
                  left: tooltipPos.x + 16,
                  top: tooltipPos.y + 16,
                }}
              >
                <div className="tooltip-header">
                  <span className="tooltip-title">Trạng thái:</span>
                  <span
                    className="tooltip-color-dot"
                    style={{ background: statusData[hoveredIdx].color }}
                  />
                  <span className="tooltip-status-label">
                    {statusData[hoveredIdx].label}
                  </span>
                </div>

                <div className="tooltip-members">
                  <span className="tooltip-title">Người thực hiện:</span>
                  <div className="tooltip-avatars">
                    <img
                      src={userAvatar}
                      alt="user"
                      className="tooltip-avatar"
                    />
                    <img
                      src={userAvatar}
                      alt="user"
                      className="tooltip-avatar"
                    />
                  </div>
                </div>

                <div className="tooltip-tasks">
                  <span className="tooltip-title">Số lượng công việc:</span>
                  <span className="tooltip-task-count">
                    {statusData[hoveredIdx].taskCount}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Legend */}
          <div className="chart-legend">
            <div className="legend-header">
              <img
                src={chartPieIcon}
                alt="chart-pie"
                className="legend-header-icon"
              />
              Trạng thái của dự án{" "}
              <span className="legend-header-title">
                {projectInfo?.name || projectInfo?.projectCode || "SDTC"}
              </span>
            </div>
            {statusData.map((s) => (
              <div key={s.key} className="legend-item">
                <span
                  className="legend-color-dot"
                  style={{ background: s.color }}
                />
                <span className="legend-label">{s.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Project Members Table */}
        <div className="member-table-card">
          <div className="member-table-container">
            <table className="member-table">
              <thead>
                <tr>
                  <th>Nhân viên</th>
                  <th>Chức vụ</th>
                  <th>Phòng ban</th>
                  <th>Hiệu suất</th>
                  <th>Tổng cv</th>
                </tr>
              </thead>
              <tbody>
                {members.map((member) => (
                  <tr key={member.id || member._id}>
                    <td className="member-info">
                      <div className="member-avatar">
                        <img
                          src={member.avatar || userAvatar}
                          alt={member.name || member.fullName}
                        />
                      </div>
                      <div className="member-details">
                        <div className="member-name">
                          {member.name || member.fullName}
                        </div>
                        <div className="member-email">{member.email}</div>
                      </div>
                    </td>
                    <td className="member-role">
                      {member.position || member.role || "Nhân viên"}
                    </td>
                    <td className="member-department">
                      <span className="department-badge">
                        {member.department || member.departmentId?.name || "IT"}
                      </span>
                    </td>
                    <td className="member-efficiency">
                      <div className="efficiency-container">
                        <div className="efficiency-bar">
                          <div
                            className="efficiency-fill"
                            style={{ width: `${member.efficiency || 0}%` }}
                          ></div>
                        </div>
                        <span className="efficiency-text">
                          {member.efficiency || 0}%
                        </span>
                      </div>
                    </td>
                    <td className="member-total">
                      <div className="total-tasks">
                        <span className="total-number">
                          {member.totalTasks || 0}
                        </span>
                        <span className="total-label">công việc</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Charts;
