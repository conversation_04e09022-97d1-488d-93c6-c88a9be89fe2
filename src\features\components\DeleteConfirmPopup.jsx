import React from 'react';
import '../../styles/DeleteConfirmPopup.css';

const DeleteConfirmPopup = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  documentName,
  title = "Xóa tài liệu",
  message = "Bạn có chắc chắn muốn xóa tài liệu này không? Hành động này không thể hoàn tác."
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <div className="delete-confirm-backdrop" onClick={handleBackdropClick}>
      <div className="delete-confirm-popup">
        <div className="delete-confirm-header">
          <h3 className="delete-confirm-title">{title}</h3>
        </div>
        
        <div className="delete-confirm-content">
          <p className="delete-confirm-message">
            {documentName ? 
              `Bạn có chắc chắn muốn xóa tài liệu "${documentName}" không? Hành động này không thể hoàn tác.` 
              : message
            }
          </p>
        </div>
        
        <div className="delete-confirm-actions">
          <button 
            className="delete-confirm-cancel-btn"
            onClick={onClose}
          >
            Hủy
          </button>
          <button 
            className="delete-confirm-delete-btn"
            onClick={handleConfirm}
          >
            Xóa tài liệu
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmPopup;
