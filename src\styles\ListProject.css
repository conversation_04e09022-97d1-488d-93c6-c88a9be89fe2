@import url("../index.css");
/* Lists Component Styles */
.lists-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 139px); /* Fixed height to prevent page scroll */
  width: 100%;
  overflow: hidden;
}

.lists-content-wrapper {
  position: relative;
  height: 100%; /* Use full height of parent */
  width: 100%;
  overflow: hidden;
}

.task-view-wrapper {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.task-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
}

.icon-plus {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='16' height='16' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 5v14M5 12h14'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-count {
  font-size: 14px;
  color: #666;
}

.btn-bulk-delete,
.btn-bulk-move {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-bulk-delete:hover,
.btn-bulk-move:hover {
  background-color: #e5e7eb;
}

.btn-bulk-delete {
  color: #ef4444;
}

.tasks-table-container {
  flex: 1;
  overflow: auto;
  /* Áp dụng custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: all 0.3s ease;
}

/* Custom scrollbar cho tasks-table-container */
.tasks-table-container::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  opacity: 0;
}

.tasks-table-container::-webkit-scrollbar-track {
  background: transparent;
  margin: 3px;
}

.tasks-table-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.tasks-table-container:hover {
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
}

.tasks-table-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.12);
}

.tasks-table-container:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.tasks-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.tasks-table th {
  background-color: #f9fafb;
  text-align: left;
  padding: 12px 16px;
  font-weight: 600;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tasks-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  color: #1f2937;
}

.checkbox-column {
  width: 40px;
}

.task-name {
  font-weight: 500;
}

.selected-row {
  background-color: rgba(59, 130, 246, 0.05);
}

.selected-row:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.tasks-table tr:hover {
  background-color: #f9fafb;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-complete {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-in-progress {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-not-started {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Priority badge styles */
.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.priority-high {
  background-color: #fee2e2;
  color: #dc2626;
}

.priority-medium {
  background-color: #fef3c7;
  color: #d97706;
}

.priority-low {
  background-color: #e0f2fe;
  color: #0284c7;
}

.assignee {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Progress bar styles */
.progress-container {
  width: 100%;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  position: relative;
}

.progress-bar {
  height: 100%;
  border-radius: 4px;
}

.progress-bar-complete {
  background-color: #10b981;
}

.progress-bar-halfway {
  background-color: #3b82f6;
}

.progress-bar-started {
  background-color: #6b7280;
}

.progress-text {
  position: absolute;
  right: -30px;
  top: -4px;
  font-size: 12px;
  color: #4b5563;
}

.task-filters {
  margin-bottom: 16px;
}

.task-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.task-view {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 100%;
  overflow: hidden; /* Ngăn không cho task-view scroll */
  padding-bottom: 60px; /* Space for action bar */
  width: 100%;
  display: flex;
  flex-direction: column;
}



.task-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

/* Ensure the table header remains fixed at the top */
.task-table thead {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: #fafbfc;
}

/* Add a subtle shadow to the fixed header for better visual separation */
.task-table thead::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}

.task-table th {
  text-align: left;
  padding: 12px 10px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.task-table th.task-name-col {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-table th.task-name-col > input[type="checkbox"] {
  margin-right: 6px;
}

.task-table th.task-name-col > span {
  display: inline-block;
  vertical-align: middle;
}

.task-table th.task-status-col,
.task-table th.task-priority-col,
.task-table th.task-assignee-col {
  text-align: left;
}

.task-table td {
  padding: 12px 10px;
  font-size: 14px;
  border-bottom: 1px solid #e0e0e0;
  color: #7c7c7c;
}

.task-status-cell,
.task-priority-cell,
.task-assignee-cell {
  text-align: left;
}

.task-table tbody tr:hover {
  background-color: #f5f7fa;
}

.task-table tr.selected {
  background-color: #e6f0ff;
}

.task-table tbody {
  position: relative;
}

.task-name-col {
  width: 60%;
}

.task-status-col {
  width: 15%;
  text-align: left;
}

.task-priority-col {
  width: 12%;
  text-align: left;
}

.task-assignee-col {
  width: 13%;
  text-align: left;
}

.task-id-name {
  display: flex;
  align-items: center;
}

.task-id {
  margin-right: 8px;
  font-weight: 500;
  min-width: 70px;
}

.task-comments {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  font-size: 13px;
  color: #666;
}

/* Task Status */
.task-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  justify-content: flex-start;
  gap: 6px;
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.task-status.pending .status-text {
  color: #8c8c8c;
}

/* .task-status.in-progress .status-text {
  color: #1890ff;
} */

/* .task-status.completed .status-text {
  color: #52c41a;
} */

.task-status.overdue .status-text {
  font-weight: 500;
}

/* .task-status.review .status-text {
  color: #722ed1;
} */

/* Priority */
.task-priority {
  display: flex;
  align-items: center;
  font-size: 13px;
  justify-content: flex-start;
  gap: 6px;
}

.priority-icon {
  width: 16px;
  height: 16px;
}

/* Assignee */
.assignee-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
  margin-right: -8px;
}

.assignee-avatar:last-child {
  margin-right: 0;
}

.assignee-avatar:hover {
  transform: scale(1.1);
  z-index: 10;
}

/* Status Popup Styles */
.status-popup {
  position: fixed !important;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 200px;
  z-index: 1001 !important;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
  animation: fadeIn 0.15s ease-in-out;
  transform-origin: top left;
  pointer-events: auto !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-popup-header {
  padding: 12px 16px;
  font-weight: 500;
  font-size: 14px;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f5f5f5;
}

.status-popup-options {
  max-height: 200px;
  overflow-y: auto;
}

.status-option {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-option:hover {
  background-color: #f9fafb;
}

.status-option.active {
  background-color: #f9fafb;
}

.status-option img {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.status-option span {
  font-size: 14px;
  color: #4b5563;
}

/* Status change animation */
@keyframes statusChange {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Priority Popup Styles */
.priority-popup {
  position: fixed;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 200px;
  z-index: 1000;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
  animation: fadeIn 0.15s ease-in-out;
  transform-origin: top left;
}

.priority-popup-header {
  padding: 12px 16px;
  font-weight: 500;
  font-size: 14px;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f5f5f5;
}

.priority-popup-options {
  max-height: 200px;
  overflow-y: auto;
}

.priority-option {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.priority-option:hover {
  background-color: #f9fafb;
}

.priority-option.active {
  background-color: #f9fafb;
}

.priority-option img {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.priority-option span {
  font-size: 14px;
  color: #4b5563;
}

.task-status {
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.task-status:hover {
  background-color: #f3f4f6;
  border-radius: 4px;
}

.task-status.status-changing {
  animation: statusChange 0.5s ease;
}

/* Status colors with transitions */
.task-status {
  transition: background-color 0.3s ease, color 0.3s ease;
}

.task-priority {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  width: fit-content;
}

.priority-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.priority-text {
  font-size: 14px;
}

/* Assignee tooltip styles */
.assignee-tooltip {
  position: fixed;
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  transform: translateX(-50%);
  white-space: nowrap;
  animation: tooltipFadeIn 0.15s ease-in-out;
}

.assignee-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Custom scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: all 0.3s ease;
}

.assignee-avatar:hover {
  transform: scale(1.1);
  z-index: 10;
}

/* Checkbox hình tròn cho bảng công việc */
.task-table input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #bbb;
  border-radius: 50%;
  background: #fff;
  outline: none;
  cursor: pointer;
  position: relative;
  transition: border-color 0.2s;
}
.task-table input[type="checkbox"]:checked {
  border-color: #1976d2;
  background: #1976d2;
}
.task-table input[type="checkbox"]:checked::after {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
}
.task-table input[type="checkbox"]:hover {
  border-color: #1976d2;
}
.task-action-bar {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 32px;
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
.task-cancel-btn {
  background: #fff;
  color: #1976d2;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 16px;
  margin-right: 16px;
  cursor: pointer;
  transition: background 0.2s;
}
.task-cancel-btn:hover {
  background: #f0f0f0;
}
.delete-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 32px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
  cursor: pointer;
  transition: background 0.2s;
}
.delete-btn:hover {
  background: #1251a3;
}
.statistical-list-container {
  background: #f8f9fb;
  min-height: 100vh;
}
.title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: #222;
}
.statistical-cards {
  display: flex;
  gap: 24px;
}
.stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  padding: 24px 32px 16px 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 180px;
}
.stat-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
}
.stat-title.stat-title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}
.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #222;
}
.stat-desc {
  font-size: 13px;
  color: #bdbdbd;
  margin-top: 4px;
}
.statistical-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 18px;
  justify-content: flex-end;
}
/* .btn-filter, .btn-export {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.2s, border 0.2s;
} */
/* .btn-filter:hover, .btn-export:hover {
  background: #f0f4fa;
  border: 1px solid #1976d2;
} */
.project-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.project-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.project-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 32px;
}
.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-bottom: 2px;
}
.project-code {
  font-size: 13px;
  color: #888;
  margin-bottom: 8px;
}
.project-time {
  font-size: 14px;
  color: #444;
  margin-bottom: 0;
}
.project-members {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  min-width: 80px;
}
.avatars {
  display: flex;
  gap: 2px;
}
.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #e3eafc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 2px solid #fff;
  margin-right: -6px;
}
.project-status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  gap: 4px;
}
.status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #7b61ff;
  margin-right: 6px;
  animation: status-blink 1.2s infinite alternate;
}
@keyframes status-blink {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
.status-text {
  color: #7c7c7c;
  font-size: 15px;
  font-weight: 500;
}
.project-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}
.btn-view,
.btn-edit,
.btn-delete {
  background: #f5f6fa;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-view:hover {
  background: #e3eafc;
}
.btn-edit:hover {
  background: #fffbe6;
}
.btn-delete:hover {
  background: #ffeaea;
}
.project-progress {
  margin-top: 8px;
}
.progress-label {
  font-size: 14px;
  color: #1976d2;
  font-weight: 500;
  margin-bottom: 4px;
}
.progress-bar {
  width: 100%;
  height: 7px;
  background: #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 4px;
}
.progress-bar-inner {
  height: 100%;
  background: #1976d2;
  border-radius: 6px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.progress-desc {
  font-size: 13px;
  color: #888;
}

/* Tabs UI & button style cho phần đầu trang thống kê */
/* .tabs-list {
  background: #eceef080;
  border-radius: 8px;
  display: flex;
  gap: 4px;
}
.tabs-trigger {
  background: transparent;
  border: none;
  outline: none;
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 15px;
  color: #222;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.15s, color 0.15s;
  height: 36px;
  line-height: 20px;
  display: flex;
  align-items: center;
}
.tabs-trigger.active {
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
  font-weight: 600;
  color: #222;
  border: 1.5px solid #e5e7eb;
  padding: 4px 12px;
  border-radius: 10px;
}
.tabs-trigger:not(.active):hover {
  background: #f0f0f0;
} */
/* .btn-filter,
.btn-export {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  padding: 6px 16px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  transition: background 0.15s, border 0.15s;
  height: 36px;
  margin: 0;
} */
/* .btn-filter:hover,
.btn-export:hover {
  background: #f7f8fa;
  border-color: #bfc5ce;
} */
.tabs .flex.justify-between.items-center {
  flex-wrap: nowrap !important;
  display: flex;
  margin: 24px 0;
}
.tabs .flex.space-x-2 {
  flex-direction: row !important;
  gap: 8px !important;
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
}
.add-task-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #7c7c7c;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 0 4px 14px;
  margin: 2px 0 2px 0;
  transition: color 0.18s;
  opacity: 0.5;
}
.add-task-btn:hover {
  color: #7c7c7c;
  opacity: 1;
}
.add-task-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  filter: invert(38%) sepia(0%) saturate(0%) hue-rotate(180deg) brightness(90%)
    contrast(90%);
  opacity: 1;
  transition: filter 0.18s;
}
.add-task-btn:hover .add-task-icon {
  filter: invert(38%) sepia(0%) saturate(0%) hue-rotate(180deg) brightness(60%)
    contrast(120%);
}
.custom-scrollbar:hover {
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
}

/* For Webkit browsers (Chrome, Safari) */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  opacity: 0;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  margin: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.12);
}

.custom-scrollbar:hover::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.detail-job-panel {
  width: 400px;
  margin-left: 24px;
  flex-shrink: 0;
}

/* Khi không có task được chọn, bảng sẽ chiếm toàn bộ chiều rộng */
.task-view-wrapper:only-child {
  width: 100%;
}

/* Animation for the task status change */
@keyframes statusChange {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.task-status.status-changing {
  animation: statusChange 0.5s ease;
}

/* Small Tasks Dropdown Styles */
.small-tasks-dropdown {
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  padding: 20px;
  transition: all 0.3s ease;
}

.small-tasks-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.small-tasks-grid::-webkit-scrollbar {
  width: 6px;
}

.small-tasks-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.small-tasks-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.small-tasks-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.small-tasks-empty {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

/* Parent task indicator */
.parent-task-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #3b82f6;
}

.parent-task-indicator::before {
  content: "📋";
  font-size: 16px;
}
