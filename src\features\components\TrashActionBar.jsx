import React from "react";
import "../../styles/TrashActionBar.css";
import trashIcon from "../../assets/trash.svg";
import refreshIcon from "../../assets/refresh-ccw.svg";

const TrashActionBar = ({ selectedCount = 1, onRestore, onDelete, onClearSelection }) => {
  return (
    <div className="trash-action-bar">
      <div className="selection-group">
        <span>Đ<PERSON> chọn {selectedCount} mục</span>
        <button className="clear-selection" onClick={onClearSelection}>
          Bỏ chọn tất cả
        </button>
      </div>
      <div className="action-group">
        <button className="restore-btn" onClick={onRestore}>
          <img src={refreshIcon} alt="restore" className="icon-restore-img" /> <PERSON><PERSON><PERSON><PERSON> phục ({selectedCount})
        </button>
        <button className="delete-btn" onClick={onDelete}>
          <img src={trashIcon} alt="delete" className="icon-delete-img" /> <PERSON><PERSON><PERSON> vĩ<PERSON> viễn ({selectedCount})
        </button>
      </div>
    </div>
  );
};

export default TrashActionBar;
