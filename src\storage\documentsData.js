import userIcon from '../assets/user1.png';

// D<PERSON> liệu mặc định
const defaultDocumentsData = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON> liệu hướng dẫn sử dụng dự án",
    type: "pdf",
    size: "2.5 MB",
    creator: "<PERSON>uy<PERSON><PERSON>ăn <PERSON>",
    createdAt: "25/6/2025",
    downloadUrl: "/documents/huong-dan-su-dung-du-an.pdf",
    fileName: "huong-dan-su-dung-du-an.pdf",
    icon: "file-text",
    description: "Tài liệu này sử dụng với mục đích hướng dẫn người dùng có thể thao tác và sử dụng tốt ứng dụng trong dự án",
    project: "SDTC",
    attachments: [
      {
        name: "<PERSON><PERSON><PERSON> c<PERSON>u khách hàng.xlsx",
        size: "1.2 MB",
        type: "xlsx",
        date: "Hôm nay",
        url: "/attachments/yeu-cau-khach-hang.xlsx"
      },
      {
        name: "SRS.docx",
        size: "2.1 MB",
        type: "docx",
        date: "Hôm qua",
        url: "/attachments/srs.docx"
      }
    ],
    activities: [
      {
        user: 'Gia Bảo',
        avatar: userIcon,
        content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan',
        timestamp: new Date().toLocaleString('vi-VN')
      },
      {
        user: 'Tấn Sanh',
        avatar: userIcon,
        content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan',
        timestamp: new Date().toLocaleString('vi-VN')
      },
      {
        user: 'Tấn Sanh',
        avatar: userIcon,
        content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan',
        timestamp: new Date().toLocaleString('vi-VN')
      }
    ]
  },
  {
    id: 2,
    name: "Tài liệu hướng dẫn xây dựng cấu trúc dự án",
    type: "docx",
    size: "1.8 MB",
    creator: "Trần Thị B",
    createdAt: "25/6/2025",
    downloadUrl: "/documents/huong-dan-xay-dung-cau-truc.docx",
    fileName: "huong-dan-xay-dung-cau-truc.docx",
    icon: "file-text"
  },
  {
    id: 3,
    name: "Tài liệu về quy trình dự án",
    type: "pdf",
    size: "3.2 MB",
    creator: "Lê Văn C",
    createdAt: "25/6/2025",
    downloadUrl: "/documents/quy-trinh-du-an.pdf",
    fileName: "quy-trinh-du-an.pdf",
    icon: "file-text"
  },
  {
    id: 4,
    name: "Tài liệu phân công công việc của thành viên",
    type: "xlsx",
    size: "1.5 MB",
    creator: "Phạm Thị D",
    createdAt: "25/6/2025",
    downloadUrl: "/documents/phan-cong-cong-viec.xlsx",
    fileName: "phan-cong-cong-viec.xlsx",
    icon: "file-text"
  },
  {
    id: 5,
    name: "Tài liệu hướng dẫn vận hành dự án",
    type: "pdf",
    size: "4.1 MB",
    creator: "Hoàng Văn E",
    createdAt: "25/6/2025",
    downloadUrl: "/documents/huong-dan-van-hanh.pdf",
    fileName: "huong-dan-van-hanh.pdf",
    icon: "file-text"
  },
  {
    id: 6,
    name: "Biên bản họp khởi động dự án",
    type: "docx",
    size: "1.2 MB",
    creator: "Nguyễn Văn A",
    createdAt: "20/6/2025",
    downloadUrl: "/documents/bien-ban-hop-khoi-dong.docx",
    fileName: "bien-ban-hop-khoi-dong.docx",
    icon: "file-text"
  },
  {
    id: 7,
    name: "Báo cáo tiến độ tháng 6",
    type: "pptx",
    size: "5.7 MB",
    creator: "Trần Thị B",
    createdAt: "18/6/2025",
    downloadUrl: "/documents/bao-cao-tien-do-thang-6.pptx",
    fileName: "bao-cao-tien-do-thang-6.pptx",
    icon: "file-text"
  },
  {
    id: 8,
    name: "Kế hoạch phát triển Q3/2025",
    type: "pdf",
    size: "2.9 MB",
    creator: "Lê Văn C",
    createdAt: "15/6/2025",
    downloadUrl: "/documents/ke-hoach-phat-trien-q3.pdf",
    fileName: "ke-hoach-phat-trien-q3.pdf",
    icon: "file-text"
  },
  {
    id: 9,
    name: "Tài liệu đào tạo nhân viên mới",
    type: "pdf",
    size: "8.3 MB",
    creator: "Phạm Thị D",
    createdAt: "10/6/2025",
    downloadUrl: "/documents/dao-tao-nhan-vien-moi.pdf",
    fileName: "dao-tao-nhan-vien-moi.pdf",
    icon: "file-text"
  },
  {
    id: 10,
    name: "Hướng dẫn sử dụng API",
    type: "pdf",
    size: "1.7 MB",
    creator: "Hoàng Văn E",
    createdAt: "05/6/2025",
    downloadUrl: "/documents/huong-dan-su-dung-api.pdf",
    fileName: "huong-dan-su-dung-api.pdf",
    icon: "file-text"
  }
];

// Key để lưu trong localStorage
const STORAGE_KEY = 'documents_data';

// Hàm lấy dữ liệu từ localStorage hoặc dữ liệu mặc định
const getDocumentsFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Lỗi khi đọc dữ liệu từ localStorage:', error);
  }
  return [...defaultDocumentsData];
};

// Hàm lưu dữ liệu vào localStorage
const saveDocumentsToStorage = (documents) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(documents));
  } catch (error) {
    console.error('Lỗi khi lưu dữ liệu vào localStorage:', error);
  }
};

// Khởi tạo dữ liệu từ localStorage
let documentsData = getDocumentsFromStorage();

export const fetchDocuments = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Luôn lấy dữ liệu mới nhất từ localStorage
      documentsData = getDocumentsFromStorage();
      resolve([...documentsData]);
    }, 500);
  });
};

// Hàm thêm tài liệu mới
export const addDocument = (documentData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Lấy dữ liệu mới nhất từ localStorage
      documentsData = getDocumentsFromStorage();

      // Tạo ID mới (lấy ID lớn nhất + 1)
      const newId = documentsData.length > 0 ? Math.max(...documentsData.map(doc => doc.id)) + 1 : 1;

      // Tạo object tài liệu mới với cấu trúc đầy đủ
      const newDocument = {
        id: newId,
        name: documentData.name,
        type: documentData.type || 'pdf',
        size: documentData.size || '0 KB',
        creator: documentData.creator || 'Người dùng',
        createdAt: documentData.createdAt || new Date().toLocaleDateString('vi-VN'),
        downloadUrl: documentData.downloadUrl || '',
        fileName: documentData.fileName || `${documentData.name}.${documentData.type}`,
        icon: 'file-text',
        description: documentData.description || '',
        project: documentData.project || 'SDTC',
        attachments: documentData.attachments || [],
        activities: documentData.activities || []
      };

      // Thêm vào đầu mảng
      documentsData.unshift(newDocument);

      // Lưu vào localStorage
      saveDocumentsToStorage(documentsData);

      resolve(newDocument);
    }, 300);
  });
};

// Hàm xóa tài liệu
export const deleteDocument = (documentId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Lấy dữ liệu mới nhất từ localStorage
      documentsData = getDocumentsFromStorage();

      const index = documentsData.findIndex(doc => doc.id === documentId);
      if (index !== -1) {
        const deletedDocument = documentsData.splice(index, 1)[0];

        // Lưu vào localStorage
        saveDocumentsToStorage(documentsData);

        resolve(deletedDocument);
      } else {
        resolve(null);
      }
    }, 300);
  });
};

// Hàm cập nhật tài liệu
export const updateDocument = (documentId, updatedData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Lấy dữ liệu mới nhất từ localStorage
      documentsData = getDocumentsFromStorage();

      const index = documentsData.findIndex(doc => doc.id === documentId);
      if (index !== -1) {
        documentsData[index] = { ...documentsData[index], ...updatedData };

        // Lưu vào localStorage
        saveDocumentsToStorage(documentsData);

        resolve(documentsData[index]);
      } else {
        resolve(null);
      }
    }, 300);
  });
};

// Hàm reset dữ liệu về mặc định (để test)
export const resetDocuments = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      documentsData = [...defaultDocumentsData];
      saveDocumentsToStorage(documentsData);
      resolve(documentsData);
    }, 300);
  });
};

export default defaultDocumentsData;