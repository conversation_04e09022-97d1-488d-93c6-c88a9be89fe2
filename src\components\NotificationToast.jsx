import React, { useEffect } from 'react';
import '../styles/NotificationToast.css';

const NotificationToast = ({ 
  show, 
  message, 
  type = "success", // success, error, warning, info
  duration = 3000,
  onClose 
}) => {
  useEffect(() => {
    if (show && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [show, duration, onClose]);

  if (!show) return null;

  return (
    <div className={`notification-toast ${type} show`}>
      <div className="toast-content">
        <div className={`toast-icon ${type}`}>
          {type === 'success' && '✅'}
          {type === 'error' && '❌'}
          {type === 'warning' && '⚠️'}
          {type === 'info' && 'ℹ️'}
        </div>
        <span className="toast-message">{message}</span>
        <button className="toast-close" onClick={onClose}>
          ×
        </button>
      </div>
    </div>
  );
};

export default NotificationToast;