// D<PERSON> liệu thông báo bình luận
export const commentNotificationData = [
  {
    id: 1,
    title: "<PERSON>ình luận mới",
    message: "<PERSON><PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "new-comment",
    priority: "medium",
    author: {
      name: "<EMAIL>",
      avatar: "/src/assets/user1.png",
      role: "Designer"
    },
    taskName: "Thiết kế giao diện",
    projectName: "Dự án Website",
    commentContent: "Giao diện này cần điều chỉnh màu sắc cho phù hợp với brand guidelines.",
    taskId: "TASK-001"
  },
  {
    id: 2,
    title: "Phản hồi bình luận",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "reply",
    priority: "medium",
    author: {
      name: "g<PERSON><PERSON>@gmail.com",
      avatar: "/src/assets/user1.png",
      role: "Developer"
    },
    taskName: "Phát triển API",
    projectName: "Dự án Mobile App",
    commentContent: "Tôi đồng ý với ý kiến của bạn, chúng ta nên implement theo cách này.",
    taskId: "TASK-002",
    replyTo: "Bình luận của bạn về cấu trúc API"
  },
  {
    id: 3,
    title: "Được mention",
    message: "Lê Văn C đã nhắc đến bạn trong bình luận",
    time: "1 giờ trước",
    isRead: true,
    type: "mention",
    priority: "high",
    author: {
      name: "Lê Văn C",
      avatar: "/src/assets/user1.png",
      role: "Project Manager"
    },
    taskName: "Kiểm thử hệ thống",
    projectName: "Dự án Testing",
    commentContent: "@you Bạn có thể review phần test case này được không?",
    taskId: "TASK-003",
    mentionContext: "Review test case"
  },
  {
    id: 4,
    title: "Bình luận được like",
    message: "Phạm Thị D đã thích bình luận của bạn",
    time: "2 giờ trước",
    isRead: true,
    type: "like",
    priority: "low",
    author: {
      name: "Phạm Thị D",
      avatar: "/src/assets/user1.png",
      role: "QA Tester"
    },
    taskName: "Viết tài liệu",
    projectName: "Dự án Documentation",
    commentContent: "Tài liệu này rất chi tiết và dễ hiểu!",
    taskId: "TASK-004",
    likedComment: "Cảm ơn team đã support tốt trong quá trình làm tài liệu"
  },
  {
    id: 5,
    title: "Bình luận khẩn cấp",
    message: "Hoàng Văn E đã đánh dấu bình luận là khẩn cấp",
    time: "3 giờ trước",
    isRead: true,
    type: "urgent",
    priority: "high",
    author: {
      name: "Hoàng Văn E",
      avatar: "/src/assets/user1.png",
      role: "Tech Lead"
    },
    taskName: "Fix bug critical",
    projectName: "Dự án Production",
    commentContent: "Bug này đang ảnh hưởng đến user production, cần fix ngay!",
    taskId: "TASK-005",
    urgencyLevel: "critical"
  },
  {
    id: 6,
    title: "Bình luận được resolve",
    message: "Vũ Thị F đã đánh dấu bình luận của bạn là đã giải quyết",
    time: "5 giờ trước",
    isRead: true,
    type: "resolved",
    priority: "low",
    author: {
      name: "Vũ Thị F",
      avatar: "/src/assets/user1.png",
      role: "Business Analyst"
    },
    taskName: "Phân tích yêu cầu",
    projectName: "Dự án Analysis",
    commentContent: "Đã clarify xong requirement này với client.",
    taskId: "TASK-006",
    resolvedIssue: "Làm rõ yêu cầu về tính năng thanh toán"
  }
];

// Hàm lọc thông báo bình luận
export const filterCommentNotifications = (notifications, filter) => {
  switch (filter) {
    case 'unread':
      return notifications.filter(notif => !notif.isRead);
    case 'read':
      return notifications.filter(notif => notif.isRead);
    case 'all':
    default:
      return notifications;
  }
};

// Hàm đánh dấu đã đọc
export const markCommentNotificationAsRead = (notificationId) => {
  const notification = commentNotificationData.find(notif => notif.id === notificationId);
  if (notification) {
    notification.isRead = true;
  }
};

// Hàm đánh dấu tất cả đã đọc
export const markAllCommentNotificationsAsRead = () => {
  commentNotificationData.forEach(notification => {
    notification.isRead = true;
  });
};

// Hàm đánh dấu tất cả chưa đọc
export const markAllCommentNotificationsAsUnread = () => {
  commentNotificationData.forEach(notification => {
    notification.isRead = false;
  });
};
