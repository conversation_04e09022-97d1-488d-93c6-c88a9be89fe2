import React, { useState, useRef, useEffect } from "react";
import { useParams } from "react-router-dom";
import "../../../styles/Members.css";
import searchIcon from '../../../assets/search.svg';
// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import TeamIcon from "../../../assets/icon-sidebar/doingu.svg";
import { getProjectMembers, transformUserData } from '../../../api/userManagement';

// Thêm cache và preload ở đầu file
let membersCache = {};
let membersCacheTimestamp = {};
const MEMBERS_CACHE_DURATION = 10000; // 10s
let membersPreloadPromise = {};

const preloadMembers = async (projectId) => {
  if (!projectId) return [];
  if (membersPreloadPromise[projectId]) return membersPreloadPromise[projectId];
  membersPreloadPromise[projectId] = (async () => {
    try {
      const res = await getProjectMembers(projectId);
      const rawUsers = res.data || res || [];
      const users = rawUsers.map(m => {
        const user = m.user || m;
        const createdAt = m.joinedAt || user.joinedAt || user.createdAt || null;
        let department = user.departmentInfo?.name
          || user.department?.name
          || user.departmentName
          || (typeof user.department === 'string' ? user.department : '')
          || 'Chưa phân công';
        return {
          ...transformUserData(user),
          department,
          createdAt: createdAt ? new Date(createdAt).toLocaleDateString('vi-VN') : 'N/A'
        };
      });
      membersCache[projectId] = users;
      membersCacheTimestamp[projectId] = Date.now();
      return users;
    } catch {
      return [];
    }
  })();
  return membersPreloadPromise[projectId];
};

const Members = () => {
  const { projectId } = useParams();
  const [users, setUsers] = useState(projectId && membersCache[projectId] ? membersCache[projectId] : []);
  const [loading, setLoading] = useState(!(projectId && membersCache[projectId]));
  const [search, setSearch] = useState("");
  const [openFilter, setOpenFilter] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState("Tất cả");
  const [selectedRole, setSelectedRole] = useState("Tất cả");
  const [selectedStatus, setSelectedStatus] = useState("Tất cả");
  const filterRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setOpenFilter(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (!projectId) {
      setUsers([]);
      setLoading(false);
      return;
    }
    let ignore = false;
    const fetchMembers = async () => {
      const now = Date.now();
      if (membersCache[projectId] && (now - membersCacheTimestamp[projectId]) < MEMBERS_CACHE_DURATION) {
        setUsers(membersCache[projectId]);
        setLoading(false);
        preloadMembers(projectId).then(newUsers => {
          if (!ignore && newUsers) {
            setUsers(newUsers);
            setLoading(false);
          }
        });
        return;
      }
      setLoading(!(projectId && membersCache[projectId]));
      try {
        const newUsers = await preloadMembers(projectId);
        if (!ignore && newUsers) {
          setUsers(newUsers);
          setLoading(false);
        }
      } catch {
        if (!ignore) {
          setUsers([]);
          setLoading(false);
        }
      }
    };
    fetchMembers();
    return () => { ignore = true; };
  }, [projectId]);

  useEffect(() => { if (projectId) preloadMembers(projectId); }, [projectId]);

  if (!projectId) {
    return (
      <div className="members-container">
        <div style={{ padding: 32, textAlign: 'center', color: 'red' }}>
          Không xác định được dự án. Vui lòng truy cập từ trang dự án cụ thể.
        </div>
      </div>
    );
  }

  const filteredUsers = users.filter((user) => {
    const matchSearch = (user.name?.toLowerCase() || "").includes(search.toLowerCase()) || 
                        (user.email?.toLowerCase() || "").includes(search.toLowerCase());
    const matchDepartment = selectedDepartment === "Tất cả" || user.department === selectedDepartment;
    const matchRole = selectedRole === "Tất cả" || user.role === selectedRole;
    const matchStatus = selectedStatus === "Tất cả" || 
                       (selectedStatus === "Hoạt động" ? user.status === "active" : user.status === "locked");
    return matchSearch && matchDepartment && matchRole && matchStatus;
  });

  const departments = ["Tất cả", ...new Set(users.map(user => user.department))];
  const roles = ["Tất cả", "Trưởng phòng", "Leader", "Staff", "HCNS", "CEO", "Admin Sys"];
  const statuses = ["Tất cả", "Hoạt động", "Đã khoá"];

  return (
    <div className="members-container">
      <div className="members-header">
        <div className="members-title">
          <div className="members-title-row">
            <img src={TeamIcon} alt="Đội ngũ" className="members-title-icon" />
            <h1>Thành viên trong dự án</h1>
          </div>
          <div className="members-tabs">
            <div className="tab active">
              Thành viên <span className="tab-count">{filteredUsers.length}</span>
            </div>
          </div>
        </div>
        <div className="members-search">
          <div className="search-container">
            <img src={searchIcon} alt="Search" className="search-ion" />
            <input
              type="text"
              placeholder="Tìm kiếm thành viên"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="member-search-input"
            />
          </div>
        </div>
      </div>

      <div className="members-table-container">
        {loading ? (
          <div>
            <table className="members-table">
              <thead>
                <tr>
                  <th>Nhân viên</th>
                  <th>Phòng ban</th>
                  <th>Chức vụ</th>
                  <th>Trạng thái</th>
                  <th>Vị trí</th>
                  <th>Ngày thêm vào</th>
                </tr>
              </thead>
              <tbody>
                {Array.from({ length: 6 }).map((_, idx) => (
                  <tr key={idx} style={{ opacity: 0.7 }}>
                    <td><div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    <td><div style={{ width: 80, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    <td><div style={{ width: 60, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    <td><div style={{ width: 60, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    <td><div style={{ width: 60, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                    <td><div style={{ width: 90, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* <div style={{ padding: 32, textAlign: 'center' }}>Đang tải danh sách thành viên...</div> */}
          </div>
        ) : (
        <table className="members-table">
          <thead>
            <tr>
              <th>Nhân viên</th>
              <th>Phòng ban</th>
              <th>Chức vụ</th>
              <th>Trạng thái</th>
              <th>Vị trí</th>
              <th>Ngày thêm vào</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map((user, idx) => (
              <tr key={idx}>
                <td className="member-info">
                  <img src={user.avatar} alt={user.name} className="member-avatar" />
                  <div>
                    <div className="member-name">{user.name}</div>
                    <div className="member-email">{user.email}</div>
                    <div className="member-id">{user.id}</div>
                  </div>
                </td>
                <td>
                  <span className="department-badge">{user.department}</span>
                </td>
                <td>{user.role}</td>
                <td>
                  <span className={`status-badges ${user.status === "active" ? "active" : "locked"}`}>
                    {user.status === "active" ? "Hoạt động" : "Đã khoá"}
                  </span>
                </td>
                <td>{user.position || "UX/UI"}</td>
                <td>{user.createdAt}</td>
              </tr>
            ))}
          </tbody>
        </table>
        )}
      </div>
    </div>
  );
};

export default Members;