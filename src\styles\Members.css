@import url('../index.css');

.members-container {
  background-color: #f7fafd;
  height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 10px 32px 16px 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.03);
  flex-shrink: 0;
}

.members-title-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 22px;
}

.members-title-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.members-title h1 {
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  margin: 0;
}

.members-tabs {
  display: flex;
  gap: 0;
  margin-top: 0;
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 7px 18px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  border: none;
}

.tab.active {
  background: #007BFF1A;
  color: #5B5B5B;
}

.tab-count {
  background: #007BFF;
  color: #FFFFFF;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 4px;
}

.members-search {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}
.member-search-input {
    padding: 8px 35px 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 16px !important;
    width: 100%;
    box-sizing: border-box;
    outline: none;
    position: relative;
    z-index: 1;
    transition: border-color 0.2s ease; 
}

.search-input:focus {
  outline: none;
  border-color: #007BFF;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-input::placeholder {
  color: #9CA3AF;
  font-size: 14px;
}

.search-ion {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  z-index: 2;
  opacity: 0.6;
}

/* .filter-container {
  position: relative;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.filter-button img {
  width: 16px;
  height: 16px;
}

.filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 110px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 15px;
  z-index: 10;
  margin-top: 5px;
  animation: fadeIn 0.15s;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-section h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-option {
  padding: 5px 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-option:hover {
  background-color: #e9ecef;
}

.filter-option.selected {
  background-color: #2d5be3;
  color: white;
} */

.members-table-container {
  background: #fff;
  border-radius: 12px;
  margin: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.members-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 15px;
}

.members-table thead tr {
  background: #eaf3fb;
  color: #222;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.members-table th {
  padding: 12px 8px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: #5B5B5B;
}

/* Căn giữa các cột phòng ban, chức vụ, trạng thái, vị trí, ngày thêm vào */
.members-table th:nth-child(2),
.members-table th:nth-child(3),
.members-table th:nth-child(4),
.members-table th:nth-child(5),
.members-table th:nth-child(6) {
  text-align: center;
}

.members-table th:first-child {
  border-radius: 8px 0 0 0;
}

.members-table th:last-child {
  border-radius: 0 8px 0 0;
}

.members-table tbody tr {
  border-bottom: 1px solid #f0f0f0;
}

.members-table td {
  padding: 12px 8px;
  font-size: 14px;
  color: #333;
}

/* Căn giữa nội dung các cột phòng ban, chức vụ, trạng thái, vị trí, ngày thêm vào */
.members-table td:nth-child(2),
.members-table td:nth-child(3),
.members-table td:nth-child(4),
.members-table td:nth-child(5),
.members-table td:nth-child(6) {
  text-align: center;
}

.member-info {
  flex-direction: row;
  align-items: center;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #eaf3fb;
}

.member-name {
  font-weight: 500;
}

.member-email {
  color: #888;
  font-size: 13px;
}

.member-id {
  color: #bdbdbd;
  font-size: 12px;
}

.department-badge {
  background: #A3BFFA;
  color: #000000;
  border-radius: 12px;
  padding: 3px 12px;
  font-size: 12px;
  font-weight: 400;
}

.status-badges {
  display: inline-block;
  padding: 3px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 400;
}

.status-badges.active {
  background: #C6E8D3;
  color: #006400;
}

.status-badges.locked {
  background: #F8B4B4;
  color: #8B0000;
}

/* Scrollbar styling - thin and hidden by default, appear on hover */
.members-table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.members-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.members-table-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.2s;
}

.members-table-container:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

.members-table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Firefox scrollbar */
.members-table-container {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.members-table-container:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px); }
  to { opacity: 1; transform: translateY(0); }
}