import { useState } from 'react';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import closeIcon from '../../assets/closeLoc.svg';
import userIcon from '../../assets/user-round.svg';
import prjIcon from '../../assets/folder.svg';
import folderIcon from '../../assets/folder.svg';
import rateIcon from '../../assets/clipboard-check.svg';
import taskIcon from '../../assets/briefcase.svg';
import calendarIcon from '../../assets/today.svg';
import '../../styles/EmployeeStatisticsPopup.css';

// Đăng ký các components Chart.js cần thiết
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend
);

const EmployeeStatisticsPopup = ({ employee, onClose }) => {
  const [activeTab, setActiveTab] = useState('task-analysis');

  // Mock data cho biểu đồ pie chart
  const pieChartData = {
    labels: ['<PERSON><PERSON>n thành muộn', '<PERSON><PERSON><PERSON> thành sớm', '<PERSON><PERSON>n thành đúng hạn', 'Đang chờ', 'Đang tiến hành', 'Xem xét', 'Quá hạn'],
    data: [2, 9, 2, 2, 2, 2, 2],
    colors: ['#FFB74D', '#66BB6A', '#26C6DA', '#B0BEC5', '#42A5F5', '#BA68C8', '#EF5350']
  };

  const chartData = {
    labels: pieChartData.labels,
    datasets: [
      {
        data: pieChartData.data,
        backgroundColor: pieChartData.colors,
        borderWidth: 0,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    rotation: 160,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: '#fff',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            return `${context.parsed}`;
          }
        }
      },
    },
    elements: {
      arc: {
        borderWidth: 1,
      }
    }
  };

  // Legend Component
  const StatusLegend = () => {
    // Sắp xếp legend theo 3 cột
    const column1 = [
      { label: 'Hoàn thành sớm', data: 9, color: '#66BB6A' },
      { label: 'Hoàn thành muộn', data: 2, color: '#FFB74D' },
      { label: 'Hoàn thành đúng hạn', data: 2, color: '#26C6DA' }
    ];
    
    const column2 = [
      { label: 'Đang chờ', data: 2, color: '#B0BEC5' },
      { label: 'Đang tiến hành', data: 2, color: '#42A5F5' }
    ];
    
    const column3 = [
      { label: 'Xem xét', data: 2, color: '#BA68C8' },
      { label: 'Quá hạn', data: 2, color: '#EF5350' }
    ];

    const renderLegendColumn = (items) => (
      <div className="employee-statistics-legend-column">
        {items.map((item, index) => (
          <div key={index} className="employee-statistics-legend-item">
            <div
              className="employee-statistics-legend-color"
              style={{ backgroundColor: item.color }}
            >
              {item.data}
            </div>
            <span className="employee-statistics-legend-label">
              {item.label}
            </span>
          </div>
        ))}
      </div>
    );

    return (
      <div className="employee-statistics-legend">
        <div className="employee-statistics-legend-title">Tổng quan</div>
        <div className="employee-statistics-legend-grid">
          {renderLegendColumn(column1)}
          {renderLegendColumn(column2)}
          {renderLegendColumn(column3)}
        </div>
      </div>
    );
  };

  return (
    <div className="employee-statistics-overlay" onClick={onClose}>
      <div className="employee-statistics-popup" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="employee-statistics-header">
          <div className="employee-statistics-avatar-section">
            <div className="employee-statistics-avatar">
              <img src={employee?.avatar || "https://randomuser.me/api/portraits/men/1.jpg"} alt="avatar" />
            </div>
            <div className="employee-statistics-info">
              <h2 className="employee-statistics-name">{employee?.name || 'Nguyễn Hoài Gia Bảo'}</h2>
              <p className="employee-statistics-position">{employee?.position || 'BA phòng IT'}</p>
            </div>
          </div>
          <button className="employee-statistics-close-btn" onClick={onClose}>
            <img src={closeIcon} alt="close" />
          </button>
        </div>

                 {/* Key Metrics */}
         <div className="employee-statistics-metrics">
           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={prjIcon} alt="projects" />
               </div>
               <div className="employee-statistics-metric-label">Tổng số dự án</div>
             </div>
             <div className="employee-statistics-metric-value">{employee?.totalProjects || 12}</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={taskIcon} alt="tasks" />
               </div>
               <div className="employee-statistics-metric-label">Tổng số công việc</div>
             </div>
             <div className="employee-statistics-metric-value">{employee?.totalTasks || 50}</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={rateIcon} alt="completion" />
               </div>
               <div className="employee-statistics-metric-label">Tỉ lệ hoàn thành</div>
             </div>
             <div className="employee-statistics-metric-value">{employee?.completionRate || 60}%</div>
           </div>

           <div className="employee-statistics-metric-card">
             <div className="employee-statistics-metric-header">
               <div className="employee-statistics-metric-icon">
                 <img src={calendarIcon} alt="join date" />
               </div>
               <div className="employee-statistics-metric-label">Ngày tham gia</div>
             </div>
             <div className="employee-statistics-metric-value">20/7/2025</div>
           </div>
         </div>

        {/* Tabs */}
        <div className="employee-statistics-tabs">
          <button
            className={`employee-statistics-tab ${activeTab === 'task-analysis' ? 'active' : ''}`}
            onClick={() => setActiveTab('task-analysis')}
          >
            Phân tích công việc
          </button>
          <button
            className={`employee-statistics-tab ${activeTab === 'projects' ? 'active' : ''}`}
            onClick={() => setActiveTab('projects')}
          >
            Dự án
          </button>
        </div>

        {/* Content */}
        <div className="employee-statistics-content">
          {activeTab === 'task-analysis' && (
            <div className="employee-statistics-task-analysis">
              <div className="employee-statistics-chart-section">
                <div className="employee-statistics-section-title">
                  Phân bổ trạng thái công việc
                  <div className="employee-statistics-section-subtitle">
                    Phân bổ trạng thái công việc
                  </div>
                </div>
                
                <div className="employee-statistics-chart-content">
                  <div className="employee-statistics-pie-chart">
                    <Pie data={chartData} options={chartOptions} />
                  </div>
                  <div className="employee-statistics-legend-container">
                    <StatusLegend />
                  </div>
                </div>
              </div>
            </div>
          )}

                     {activeTab === 'projects' && (
             <div className="employee-statistics-projects">
               <div className="employee-statistics-section-title">
                 Chi tiết dự án
               </div>
               <div className="employee-statistics-projects-list">
                 <div className="employee-statistics-project-item">
                   <div className="employee-statistics-project-icon">
                     <img src={folderIcon} alt="project" />
                   </div>
                   <div className="employee-statistics-project-name">ABc</div>
                 </div>
                 <div className="employee-statistics-project-item">
                   <div className="employee-statistics-project-icon">
                     <img src={folderIcon} alt="project" />
                   </div>
                   <div className="employee-statistics-project-name">4jaksdas</div>
                 </div>
                 <div className="employee-statistics-project-item">
                   <div className="employee-statistics-project-icon">
                     <img src={folderIcon} alt="project" />
                   </div>
                   <div className="employee-statistics-project-name">hjkasdhdakjsdas</div>
                 </div>
               </div>
             </div>
           )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeStatisticsPopup; 