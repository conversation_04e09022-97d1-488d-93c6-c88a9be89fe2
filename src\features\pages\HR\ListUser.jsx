import React, { useState, useRef, useEffect } from "react";
import '../../../styles/ListUser.css';
import editIcon from '../../../assets/edit.svg';
import lockIcon from '../../../assets/lock.svg';
import UserUpdate from '../../components/UserUpdate';
import UserSessionManagement from '../../components/UserSessionManagement';
import { updateUser, toggleUserStatus } from '../../../api/userManagement';
import { showSuccess, showError } from '../../../utils/toastUtils';
import defaultAvatar from '../../../assets/user1.png';

// XÓA cache, preload, fetchUsersApi

const PAGE_SIZE = 10;

const ListUser = ({ users, searchTerm, onUpdateUser, loading: loadingProp }) => {
  const [userList, setUserList] = useState(Array.isArray(users) ? users : []);
  const [openMenuIdx, setOpenMenuIdx] = useState(null);
  const [editUserIdx, setEditUserIdx] = useState(null);
  const menuRef = useRef(null);
  const [lockingIdx, setLockingIdx] = useState(null);
  const [updateError, setUpdateError] = useState('');
  const [updateLoading, setUpdateLoading] = useState(false);
  const [showSessionManagement, setShowSessionManagement] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  const loadingValue = typeof loadingProp === 'boolean' ? loadingProp : false;
  // Phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(userList.length / PAGE_SIZE);

  // Luôn đồng bộ userList với props.users
  useEffect(() => {
    setUserList(Array.isArray(users) ? users : []);
    setCurrentPage(1); // Reset về trang 1 khi users thay đổi
  }, [users]);

  // Hàm trợ giúp để làm nổi bật các thuật ngữ tìm kiếm
  const highlightText = (text, searchTerm) => {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} style={{ backgroundColor: '#fff3cd', fontWeight: 'bold' }}>
          {part}
        </span>
      ) : part
    );
  };

  // Hàm chuyển role sang tiếng Việt
  const getRoleText = (role) => {
    if (!role) return '';
    const map = {
      'admin': 'Quản trị viên',
      'ceo': 'Giám đốc',
      'hr': 'Nhân sự',
      'departmenthead': 'Trưởng phòng',
      'leader': 'Leader',
      'staff': 'Nhân viên',
      'hcns': 'Hành chính nhân sự',
      'admin sys': 'Quản trị hệ thống',
      // fallback
    };
    const lower = role.toLowerCase();
    return map[lower] || role;
  };

  // Lấy thông tin user hiện tại để kiểm tra quyền
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();

  // Kiểm tra quyền sửa user - admin, CEO, HR và departmentHead có quyền
  const canEditUsers = ['admin', 'ceo', 'hr', 'departmenthead'].includes(userRole);
  // Kiểm tra quyền khóa user - admin, CEO và HR có quyền
  const canLockUsers = ['admin', 'ceo', 'hr'].includes(userRole);

  // Kiểm tra xem departmentHead có thể sửa user cụ thể này không
  const canEditSpecificUser = (user) => {
    if (userRole !== 'departmenthead') return canEditUsers; // Admin, CEO, HR có thể sửa tất cả
    return canEditUsers && ['leader', 'staff'].includes(user.role?.toLowerCase());
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setOpenMenuIdx(null);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Sửa user
  const handleEditClick = (idx) => {
    setEditUserIdx(idx);
    setOpenMenuIdx(null);
  };
  const handleCloseUpdate = () => {
    setEditUserIdx(null);
    setUpdateError('');
    setUpdateLoading(false);
  };
  const handleSubmitUpdate = async (data, userId) => {
    if (editUserIdx !== null) {
      setUpdateLoading(true);
      setUpdateError('');
      try {
        // Gọi callback lên HRPage để cập nhật user và refresh danh sách
        if (typeof onUpdateUser === 'function') {
          await onUpdateUser(editUserIdx, data);
        }
        // Không đóng form ở đây nữa - để UserUpdate tự đóng sau khi thành công
      } catch (err) {
        setUpdateError(err.message || 'Có lỗi xảy ra');
        throw err; // Throw lại để UserUpdate có thể catch và hiển thị lỗi
      } finally {
        setUpdateLoading(false);
      }
    }
  };
  // Khoá/mở khoá user
  const handleLockAccount = async (idx) => {
    setLockingIdx(idx);
    const user = userList[idx];
    const isCurrentlyLocked = user.status === 'locked';
    try {
      await toggleUserStatus(user.id, !isCurrentlyLocked);
      showSuccess(isCurrentlyLocked ? 'Đã mở khoá tài khoản!' : 'Đã khoá tài khoản thành công!');
      setUserList(prev => {
        const updated = [...prev];
        updated[idx] = { ...updated[idx], status: isCurrentlyLocked ? 'active' : 'locked' };
        return updated;
      });
    } catch (err) {
      showError(err.message || 'Có lỗi xảy ra');
    }
    setLockingIdx(null);
    setOpenMenuIdx(null);
  };

  // Quản lý session và IP
  const handleSessionManagement = (idx) => {
    const user = userList[idx];
    setSelectedUserId(user.id);
    setShowSessionManagement(true);
    setOpenMenuIdx(null);
  };

  const handleCloseSessionManagement = () => {
    setShowSessionManagement(false);
    setSelectedUserId(null);
  };

  // Lấy danh sách user cho trang hiện tại
  const pagedUsers = userList.slice((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE);

  return (
    <div className="hr-listuser-container">
      {editUserIdx !== null && (
        <UserUpdate
          user={userList[editUserIdx]}
          onClose={handleCloseUpdate}
          onSubmit={handleSubmitUpdate}
          error={updateError}
          loading={updateLoading}
        />
      )}
      <table className="hr-listuser-table">
        <thead>
          <tr>
            <th>Nhân viên</th>
            <th>Phòng ban</th>
            <th>Chức vụ</th>
            <th>Trạng thái</th>
            <th>Ngày tạo tk</th>
            <th>Hành động</th>
          </tr>
        </thead>
        <tbody>
          {loadingValue ? (
            Array.from({ length: 8 }).map((_, idx) => (
              <tr key={idx} style={{ opacity: 0.7 }}>
                <td className="hr-listuser-td-user">
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <div style={{ width: 40, height: 40, borderRadius: '50%', background: '#f0f0f0', animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    <div>
                      <div style={{ width: 120, height: 16, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: 100, height: 12, background: '#f0f0f0', borderRadius: 4, marginBottom: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                  </div>
                </td>
                <td><div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                <td><div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                <td><div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                <td><div style={{ width: 80, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
                <td><div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div></td>
              </tr>
            ))
          ) : pagedUsers.length === 0 ? (
            <tr>
              <td colSpan={6} style={{ textAlign: 'center', padding: '60px 20px', color: '#666', fontSize: '16px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.3 }}>🔍</div>
                <div style={{ marginBottom: '8px', fontWeight: '500' }}>Không tìm thấy nhân viên</div>
                <div style={{ fontSize: '14px' }}>Thử điều chỉnh bộ lọc hoặc từ khóa tìm kiếm</div>
              </td>
            </tr>
          ) : (
            pagedUsers.map((user, idx) => (
              <tr key={idx}>
                <td className="hr-listuser-td-user">
                  <img src={user.avatar && user.avatar.trim() !== '' ? user.avatar : defaultAvatar} alt={user.name} className="hr-listuser-avatar" />
                  <div>
                    <div className="hr-listuser-username">
                      {highlightText(user.name, searchTerm)}
                    </div>
                    <div className="hr-listuser-email">
                      {highlightText(user.email, searchTerm)}
                    </div>
                    <div className="hr-listuser-code">
                      {highlightText(user.employeeCode || user.id, searchTerm)}
                    </div>
                  </div>
                </td>
                <td className="hr-listuser-td">
                  <span className="hr-listuser-department">
                    {highlightText(user.department, searchTerm)}
                  </span>
                </td>
                <td className="hr-listuser-td">
                  {highlightText(getRoleText(user.role), searchTerm)}
                </td>
                <td className="hr-listuser-td">
                  <span className={user.status === 'active' ? 'hr-listuser-status-active' : 'hr-listuser-status-locked'}>
                    {user.status === 'active' ? 'Hoạt động' : 'Đã khoá'}
                  </span>
                </td>
                <td className="hr-listuser-td">{user.createdAt}</td>
                <td className="hr-listuser-td" style={{ position: 'relative' }}>
                  {(user.role?.toLowerCase() === 'admin' || user.role?.toLowerCase() === 'ceo') ? (
                    null
                  ) : ((canEditSpecificUser(user) || canLockUsers) ? (
                    <span
                      className="hr-listuser-action"
                      onClick={() => setOpenMenuIdx(openMenuIdx === idx ? null : idx)}
                      style={{ cursor: 'pointer' }}
                    >
                      •••
                    </span>
                  ) : (
                    <span style={{ color: '#ccc' }}>—</span>
                  ))}
                  {openMenuIdx === idx && (user.role?.toLowerCase() !== 'admin' && user.role?.toLowerCase() !== 'ceo') && (canEditSpecificUser(user) || canLockUsers) && (
                    <div className="hr-listuser-action-menu" ref={menuRef}>
                      <div className="hr-listuser-action-menu-title">Hành động</div>
                      {canEditSpecificUser(user) && (
                        <div className="hr-listuser-action-menu-item" onClick={() => handleEditClick(idx)}>
                          Sửa chi tiết
                          <span className="hr-listuser-action-menu-icon">
                            <img src={editIcon} alt="edit" style={{ width: 18, height: 18, filter: 'brightness(0) saturate(100%)' }} />
                          </span>
                        </div>
                      )}
                      {canLockUsers && (
                        <div
                          className="hr-listuser-action-menu-item"
                          onClick={() => lockingIdx === idx ? null : handleLockAccount(idx)}
                          style={lockingIdx === idx ? { opacity: 0.6, pointerEvents: 'none' } : {}}
                        >
                          {lockingIdx === idx
                            ? (user.status === 'locked' ? 'Đang mở khoá...' : 'Đang khoá...')
                            : (user.status === 'locked' ? 'Mở khoá tài khoản' : 'Khoá tài khoản')}
                          <span className="hr-listuser-action-menu-icon">
                            <img src={lockIcon} alt="lock" style={{ width: 18, height: 18, filter: 'brightness(0) saturate(100%)' }} />
                          </span>
                        </div>
                      )}
                      {canLockUsers && (
                        <div
                          className="hr-listuser-action-menu-item"
                          onClick={() => handleSessionManagement(idx)}
                        >
                          Quản lý Session & IP
                          <span className="hr-listuser-action-menu-icon">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
      {/* PHÂN TRANG */}
      {totalPages > 1 && (
        <div className="pagination-container">
          <button
            className={`pagination-btn${currentPage === 1 ? ' disabled' : ''}`}
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={currentPage === 1}
          >Trước</button>
          {Array.from({ length: totalPages }).map((_, i) => (
            <button
              key={i}
              className={`pagination-btn${currentPage === i + 1 ? ' active' : ''}`}
              onClick={() => setCurrentPage(i + 1)}
            >
              {i + 1}
            </button>
          ))}
          <button
            className={`pagination-btn${currentPage === totalPages ? ' disabled' : ''}`}
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
          >Sau</button>
        </div>
      )}
      
      {showSessionManagement && selectedUserId && (
        <UserSessionManagement
          userId={selectedUserId}
          onClose={handleCloseSessionManagement}
        />
      )}
    </div>
  );
};

export default ListUser;
