import React from 'react';
import '../../styles/ConfirmDeletePopup.css';

const ConfirmDeletePopup = ({ isOpen, onConfirm, onCancel, fileName }) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('confirm-delete-overlay')) {
      onCancel();
    }
  };

  const handlePopupClick = (e) => {
    e.stopPropagation();
  };

  const handleCancelClick = (e) => {
    e.stopPropagation();
    onCancel();
  };

  const handleConfirmClick = (e) => {
    e.stopPropagation();
    onConfirm();
  };

  return (
    <div className="confirm-delete-overlay" onClick={handleOverlayClick}>
      <div className="confirm-delete-popup" onClick={handlePopupClick}>
        <div className="confirm-delete-header">
          <h3><PERSON><PERSON><PERSON> nhận xóa tệp</h3>
        </div>
        <div className="confirm-delete-content">
          <p><PERSON>ạn có chắc chắn muốn xóa tệp <strong>"{fileName}"</strong> không?</p>
          <p className="confirm-delete-warning">Hành động này không thể hoàn tác.</p>
        </div>
        <div className="confirm-delete-actions">
          <button className="confirm-delete-cancel" onClick={handleCancelClick}>
            Hủy
          </button>
          <button className="confirm-delete-confirm" onClick={handleConfirmClick}>
            Xóa
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeletePopup;
