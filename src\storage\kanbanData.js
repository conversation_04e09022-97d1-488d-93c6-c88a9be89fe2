export const initialTasks = [
  // Ch<PERSON> điều kiện tiên quyết
  {
    id: "TSK-001",
    name: "<PERSON><PERSON><PERSON> triển frontend",
    priority: "medium",
    status: "waiting_prerequisites",
    startDate: null,
    dueDate: null,
    assignee: [
      { name: "<PERSON>uy<PERSON><PERSON>ăn <PERSON>", avatar: "https://randomuser.me/api/portraits/men/1.jpg" }
    ],
    comments: 6,
    parentTask: "TSK-01: <PERSON><PERSON><PERSON> triển hệ thống",
    dependencies: "chưa xong"
  },
  {
    id: "TSK-002",
    name: "Thiết kế database",
    priority: "high",
    status: "waiting_prerequisites",
    startDate: null,
    dueDate: null,
    assignee: [
      { name: "<PERSON>rầ<PERSON> Thị B", avatar: "https://randomuser.me/api/portraits/women/2.jpg" }
    ],
    comments: 4,
    parentTask: "TSK-01: <PERSON><PERSON><PERSON> triển hệ thống",
    dependencies: "ch<PERSON><PERSON> xong"
  },
  
  // <PERSON><PERSON> chờ
  {
    id: "TSK-003",
    name: "TSK-01 Phát triển hệ thống",
    priority: "medium",
    status: "pending",
    startDate: null,
    dueDate: null,
    assignee: [
      { name: "Lê Văn C", avatar: "https://randomuser.me/api/portraits/men/3.jpg" }
    ],
    comments: 6
  },
  {
    id: "TSK-004",
    name: "Thiết kế logo",
    priority: "low",
    status: "pending",
    startDate: null,
    dueDate: null,
    assignee: [
      { name: "Phạm Thị D", avatar: "https://randomuser.me/api/portraits/women/4.jpg" }
    ],
    comments: 2
  },
  {
    id: "TSK-005",
    name: "Chuẩn bị tài liệu họp",
    priority: "high",
    status: "pending",
    startDate: null,
    dueDate: null,
    assignee: [
      { name: "Hoàng Văn E", avatar: "https://randomuser.me/api/portraits/men/5.jpg" }
    ],
    comments: 1
  },
  
  // Đang tiến hành
  {
    id: "TSK-006",
    name: "TSK-02 Fetch API",
    priority: "medium",
    status: "in_progress",
    startDate: "19/6/2025",
    dueDate: "19/8/2025",
    assignee: [
      { name: "Nguyễn Thị F", avatar: "https://randomuser.me/api/portraits/women/6.jpg" },
      { name: "Trần Văn G", avatar: "https://randomuser.me/api/portraits/men/7.jpg" }
    ],
    comments: 6
  },
  {
    id: "TSK-007",
    name: "Lập trình chức năng đăng nhập",
    priority: "low",
    status: "in_progress",
    startDate: "17/6/2025",
    dueDate: "22/6/2025",
    assignee: [
      { name: "Lê Thị H", avatar: "https://randomuser.me/api/portraits/women/8.jpg" }
    ],
    comments: 3
  },
  {
    id: "TSK-008",
    name: "Tối ưu giao diện mobile",
    priority: "medium",
    status: "in_progress",
    startDate: "16/6/2025",
    dueDate: "23/6/2025",
    assignee: [
      { name: "Phạm Văn I", avatar: "https://randomuser.me/api/portraits/men/9.jpg" },
      { name: "Hoàng Thị K", avatar: "https://randomuser.me/api/portraits/women/10.jpg" }
    ],
    comments: 4
  },
  
  // Hoàn thành
  {
    id: "TSK-009",
    name: "TSK-003 Quản lí chấm công",
    priority: "high",
    status: "completed",
    startDate: "19/6/2025",
    dueDate: "19/8/2025",
    assignee: [
      { name: "Nguyễn Văn L", avatar: "https://randomuser.me/api/portraits/men/11.jpg" },
      { name: "Trần Thị M", avatar: "https://randomuser.me/api/portraits/women/12.jpg" }
    ],
    comments: 6
  },
  {
    id: "TSK-010",
    name: "TSK-003 Quản lí dự án",
    priority: "high",
    status: "completed",
    startDate: "19/6/2025",
    dueDate: "19/8/2025",
    assignee: [
      { name: "Lê Văn N", avatar: "https://randomuser.me/api/portraits/men/13.jpg" },
      { name: "Phạm Thị O", avatar: "https://randomuser.me/api/portraits/women/14.jpg" }
    ],
    comments: 6
  },
  {
    id: "TSK-011",
    name: "TSK-003",
    priority: "high",
    status: "completed",
    startDate: "19/6/2025",
    dueDate: "19/8/2025",
    assignee: [
      { name: "Hoàng Văn P", avatar: "https://randomuser.me/api/portraits/men/15.jpg" }
    ],
    comments: 6
  },
  
  // Hoàn tác công việc
  {
    id: "TSK-012",
    name: "TSK-02 Fetch API",
    priority: "medium",
    status: "undone",
    startDate: "19/6/2025",
    dueDate: "19/8/2025",
    assignee: [
      { name: "Nguyễn Thị Q", avatar: "https://randomuser.me/api/portraits/women/16.jpg" },
      { name: "Trần Văn R", avatar: "https://randomuser.me/api/portraits/men/17.jpg" }
    ],
    comments: 6
  },
  {
    id: "TSK-013",
    name: "Thiết kế giao diện",
    priority: "low",
    status: "undone",
    startDate: "18/6/2025",
    dueDate: "25/6/2025",
    assignee: [
      { name: "Lê Thị S", avatar: "https://randomuser.me/api/portraits/women/18.jpg" }
    ],
    comments: 3
  },
  {
    id: "TSK-014",
    name: "Viết tài liệu API",
    priority: "medium",
    status: "undone",
    startDate: "20/6/2025",
    dueDate: "27/6/2025",
    assignee: [
      { name: "Phạm Văn T", avatar: "https://randomuser.me/api/portraits/men/19.jpg" }
    ],
    comments: 2
  }
];

// Lưu và lấy kanban tasks theo từng projectId
export function getKanbanTasksByProjectId(projectId) {
  if (!projectId) return initialTasks;
  const key = `kanban_tasks_project_${projectId}`;
  const stored = localStorage.getItem(key);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return [];
    }
  }
  // Nếu là project mặc định (ví dụ SDTC), trả về tasks mẫu
  if (projectId === '1' || projectId === 1) return initialTasks;
  return [];
}

export function saveKanbanTasksByProjectId(projectId, taskList) {
  if (!projectId) return;
  const key = `kanban_tasks_project_${projectId}`;
  localStorage.setItem(key, JSON.stringify(taskList));
}

// Hàm để lấy dữ liệu mẫu cho development/testing
export function getMockKanbanData() {
  return initialTasks;
}
