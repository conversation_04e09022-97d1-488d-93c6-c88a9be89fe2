@import url('../index.css');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.dashboard-topbar {
  background: #FFFFFF;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: 56px;
  border-bottom: 1px solid #E5E7EB;
  position: fixed;
  top: 0;
  left: 250px;
  right: 0;
  z-index: 50;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  width: calc(100% - 250px);
}
.dashboard-logo-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 32px;
}
.dashboard-logo-inline .material-icons {
  font-size: 28px;
  color: #333;
}
.dashboard-title {
  font-size: 24px;
  font-weight: 700;
  letter-spacing: 0.01em;
  color: #333;
}
.dashboard-user-avatar-wrapper {
  display: flex;
  align-items: center;
  gap: 0;
  position: relative;
  cursor: pointer;
  margin-left: 0;
}
.dashboard-user-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dashboard-user-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}
.dashboard-user-avatar-arrow {
  font-size: 36px;
  color: #757575;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dashboard-search-form {
  position: relative;
  max-width: 500px;
  width: 100%;
  margin: 0 auto;
}
.dashboard-search-input-wrapper {
  position: relative;
  width: 100%;
}
.dashboard-search-input {
  width: 100%;
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 24px;
  padding: 10px 40px 10px 16px; /* padding phải lớn để tránh icon bên phải */
  color: #333;
  font-size: 14px;
  outline: none;
  transition: all 0.2s;
  box-sizing: border-box;
}
.dashboard-search-input::placeholder {
  color: #9CA3AF;
  opacity: 1;
}
.dashboard-search-input:focus {
  border-color: #0066FF;
  box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.1);
}
.dashboard-search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  pointer-events: none;
  z-index: 2;
  opacity: 0.6;
  cursor: pointer;
}
/* Notification wrapper */
.dashboard-notification-wrapper {
  position: relative;
  margin-left: 24px;
  margin-right: 11px;
}

.dashboard-notification-icon {
  width: 22px;
  height: 22px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: opacity 0.2s ease, filter 0.2s ease;
  opacity: 0.7;
}

.dashboard-notification-icon:hover {
  opacity: 1;
  filter: brightness(0.8);
}

.dashboard-notification-icon.active {
  opacity: 1;
  filter: brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(1352%) hue-rotate(204deg) brightness(102%) contrast(97%);
}

.dashboard-user-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 24px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  border-radius: 8px;
  min-width: 180px;
  z-index: 1000;
  padding: 8px 0;
}
.dashboard-user-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  cursor: pointer;
  gap: 10px;
  font-size: 16px;
  color: #333;
  transition: background 0.2s;
}
.dashboard-user-menu-item:hover {
  background: #f5f5f5;
}
.dashboard-user-menu-icon {
  width: 20px;
  height: 20px;
  display: inline-block;
}
.dashboard-user-menu-icon.account {
  border-radius: 50%;
}
@media (max-width: 768px) {
  .dashboard-topbar {
    left: 60px;
    width: calc(100% - 60px);
  }
}
@media (max-width: 768px) {
  .dashboard-topbar {
    left: 60px;
    width: calc(100% - 60px);
  }
}