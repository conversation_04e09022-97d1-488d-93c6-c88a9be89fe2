// <PERSON><PERSON> liệu trash chung cho All, Job, Project
export const trashData = [
  {
    id: 1,
    type: "Dự án",
    title: "<PERSON><PERSON> thống Social Media cũ",
    code: "SD-01",
    desc: "<PERSON><PERSON> thống quản lí <PERSON> phiên bản cũ",
    deletedDate: "16/6/2025",
    deletedBy: "Nguyễn <PERSON>",
    deadline: "20/6/2025",
    status: "<PERSON>à<PERSON> thà<PERSON>",
    members: ["Nguyễn Hoà<PERSON>", "<PERSON>uy<PERSON><PERSON>"]
  },
  {
    id: 2,
    type: "Công việc",
    title: "Thiết kế Logo cũ",
    code: "SD-01",
    desc: "<PERSON><PERSON> thống quản lí <PERSON> phiên bản cũ",
    deletedDate: "16/6/2025",
    deletedBy: "<PERSON><PERSON><PERSON>",
    deadline: "20/6/2025",
    status: "Đang chờ",
    members: ["Nguyễn <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]
  },
  {
    id: 2,
    type: "<PERSON><PERSON>ng việc",
    title: "<PERSON><PERSON><PERSON><PERSON> kế Logo cũ",
    code: "SD-01",
    desc: "<PERSON><PERSON> thống quản lí <PERSON> phiên bản cũ",
    deletedDate: "16/6/2025",
    deletedBy: "Trí Thành",
    deadline: "20/6/2025",
    status: "Đang chờ",
    members: ["Nguyễn Hoài Gia Bảo", "Nguyễn Thị Thơ"]
  },
  {
    id: 2,
    type: "Công việc",
    title: "Thiết kế Logo cũ",
    code: "SD-01",
    desc: "Hệ thống quản lí Admin phiên bản cũ",
    deletedDate: "16/6/2025",
    deletedBy: "Trí Thành",
    deadline: "20/6/2025",
    status: "Đang chờ",
    members: ["Nguyễn Hoài Gia Bảo", "Nguyễn Thị Thơ"]
  },
  {
    id: 3,
    type: "Công việc",
    title: "Thiết kế Logo cũ",
    code: "SD-01",
    desc: "Hệ thống quản lí Admin phiên bản cũ",
    deletedDate: "16/6/2025",
    deletedBy: "Trí Thành",
    deadline: "20/6/2025",
    status: "Hoàn thành",
    members: ["Nguyễn Hoài Gia Bảo", "Nguyễn Thị Thơ"]
  }
];

export function addToTrash(item) {
  // Lấy dữ liệu hiện tại từ localStorage (nếu có), nếu không thì dùng trashData
  let currentTrash = [];
  try {
    const stored = localStorage.getItem('trashData');
    if (stored) {
      currentTrash = JSON.parse(stored);
    } else {
      currentTrash = [...trashData];
    }
  } catch (e) {
    currentTrash = [...trashData];
  }
  currentTrash.unshift(item); // Thêm vào đầu danh sách
  localStorage.setItem('trashData', JSON.stringify(currentTrash));
}
