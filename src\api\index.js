// Export tất cả các API functions để dễ dàng import

// Authentication APIs
export * from './auth.js';

// Universal Profile APIs (for all roles)
export * from './profile.js';

// Role-specific APIs
export * from './admin.js';


// API URL constant
export const API_URL = import.meta.env.VITE_API_URL || 'https://project-management-ji01.onrender.com';

// Helper functions
export const getAuthToken = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  return user.token;
};

export const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Generic API call function
export const apiCall = async (endpoint, options = {}) => {
  try {
    const response = await fetch(`${API_URL}${endpoint}`, {
      headers: getAuthHeaders(),
      ...options,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
};
