import { AUTH_ENDPOINTS } from './endpoints';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  return user.token;
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export async function login(email, password) {
  try {
    const response = await fetch(AUTH_ENDPOINTS.LOGIN, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });
    if (!response.ok) {
      let error = { message: 'Đăng nhập thất bại' };
      try {
        error = await response.json();
      } catch {}
      throw new Error(error.message || 'Đăng nhập thất bại');
    }
    return response.json();
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

export async function forgotPassword(email) {
  try {
    const response = await fetch(AUTH_ENDPOINTS.FORGOT_PASSWORD, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email }),
    });
    if (!response.ok) {
      let error = { message: 'Gửi email thất bại' };
      try {
        error = await response.json();
      } catch {}
      throw new Error(error.message || 'Gửi email thất bại');
    }
    return response.json();
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

export async function resetPassword(token, newPassword) {
  try {
    const response = await fetch(AUTH_ENDPOINTS.RESET_PASSWORD(token), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ password: newPassword }),
    });
    if (!response.ok) {
      let error = { message: 'Đặt lại mật khẩu thất bại' };
      try {
        error = await response.json();
      } catch {}
      throw new Error(error.message || 'Đặt lại mật khẩu thất bại');
    }
    return response.json();
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}
