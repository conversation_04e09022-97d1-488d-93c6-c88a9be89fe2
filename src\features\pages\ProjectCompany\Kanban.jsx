let kanbanTasksCache = {};
let kanbanCacheTimestamp = {};
const KANBAN_CACHE_DURATION = 10000; // 10 giây
let kanbanPreloadPromiseMap = {};

import React, { useState } from "react";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { useOutletContext, useParams } from "react-router-dom";
import "../../../styles/Kanban.css";
import WaitingIcon from "../../../assets/waiting.svg";
import CompleteIcon from "../../../assets/complete.svg";
import ConsiderIcon from "../../../assets/consider.svg";
import OverdueIcon from "../../../assets/triangle-alert.svg";
import Deployment from "../../../assets/deployment.svg";
import CreationDateIcon from "../../../assets/creationdate.svg";
import EndDateIcon from "../../../assets/enddate.svg";
import CommentIcon from "../../../assets/cmt.svg";
import NormalIcon from "../../../assets/Normal.svg";
import MediumIcon from "../../../assets/Medium.svg";
import HighIcon from "../../../assets/High.svg";
import NoJobIcon from "../../../assets/NoJob.svg";
import ClockIcon from "../../../assets/clock.svg";
import UndoIcon from "../../../assets/undo.svg";
import LinkIconn from "../../../assets/git-compare-arrows.svg";
import LinkIcon from "../../../assets/git-fork.svg";
import {
  getProjectTasks,
  updateProjectTask,
  transformTaskListData,
} from "../../../api/taskManagement";
import { getKanbanTasksByProjectId, saveKanbanTasksByProjectId, getMockKanbanData } from "../../../storage/kanbanData";

// Mảng cấu hình các cột của bảng Kanban
const columns = [
  {
    key: "Chờ điều kiện tiên quyết",
    title: "Chờ điều kiện tiên quyết",
  },
  {
    key: "Đang chờ",
    title: "Đang chờ",
  },
  {
    key: "Đang tiến hành",
    title: "Đang tiến hành",
  },
  {
    key: "Hoàn tác công việc",
    title: "Hoàn tác công việc",
  },
  {
    key: "Hoàn thành",
    title: "Hoàn thành",
  },
];

// Đối tượng ánh xạ trạng thái sang icon
const statusIcons = {
  "Chờ điều kiện tiên quyết": ClockIcon,
  "Đang chờ": WaitingIcon,
  "Đang tiến hành": Deployment,
  "Hoàn thành": CompleteIcon,
  "Hoàn tác công việc": UndoIcon,
};

// Đối tượng ánh xạ mức độ ưu tiên sang icon
const priorityIcons = {
  "Thấp": NormalIcon,
  "Trung Bình": MediumIcon,
  "Cao": HighIcon,
};

// Thêm ánh xạ status backend -> key Kanban
const statusMap = {
  waiting_prerequisites: "Chờ điều kiện tiên quyết",
  pending: "Đang chờ",
  in_progress: "Đang tiến hành",
  completed: "Hoàn thành",
  undone: "Hoàn tác công việc",
};

// Thêm ánh xạ priority backend -> key Kanban
const priorityMap = {
  low: "Thấp",
  medium: "Trung Bình",
  high: "Cao"
};

// Map ngược tên cột Kanban sang status backend
const kanbanColKeyToStatus = {
  "Chờ điều kiện tiên quyết": "waiting_prerequisites",
  "Đang chờ": "pending",
  "Đang tiến hành": "in_progress",
  "Hoàn thành": "completed",
  "Hoàn tác công việc": "undone",
};

// Hàm lấy trạng thái hiển thị cho từng kanban
const getStatusLabel = (kanbanKey) => {
  switch (kanbanKey) {
    case "Đang chờ":
      return "Chưa bắt đầu";
    case "Đang tiến hành":
      return "Đang tiến hành";
    case "Hoàn thành":
      return "Hoàn thành";
    case "Chờ điều kiện tiên quyết":
      return "Chờ ĐKTQ";
    case "Hoàn tác công việc":
      return "Hoàn tác";
    default:
      return "";
  }
};

// Hàm kiểm tra có hiển thị ngày tháng hay không
const shouldShowDates = (kanbanKey) => {
  return !["Đang chờ", "Chờ điều kiện tiên quyết"].includes(kanbanKey);
};

// Thêm hàm flattenTasks để chuyển danh sách task cha/con thành mảng phẳng
function flattenTasks(tasks) {
  let flat = [];
  tasks.forEach((task) => {
    flat.push(task);
    if (task.children && task.children.length > 0) {
      flat = flat.concat(flattenTasks(task.children));
    }
  });
  return flat;
}

function Kanban() {
  const { projectId } = useParams();
  // State lưu danh sách công việc
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Lấy context từ component cha (WorkContent) nếu có
  const context = useOutletContext() || {};
  const { sortBy: sortOption, filters: filterOptions } = context;

  // TODO: Áp dụng sắp xếp và lọc cho tasks nếu cần

  // Preload function - kết hợp tất cả nguồn dữ liệu
  const preloadKanbanTasks = async (projectId) => {
    if (kanbanPreloadPromiseMap[projectId]) return kanbanPreloadPromiseMap[projectId];
    kanbanPreloadPromiseMap[projectId] = (async () => {
      // Kết hợp dữ liệu từ storage và mock data
      const storageTasks = getKanbanTasksByProjectId(projectId);
      const mockTasks = getMockKanbanData();
      
      let combinedTasks = [];
      if (storageTasks.length > 0) {
        combinedTasks = [...storageTasks];
      }
      if (mockTasks.length > 0) {
        const existingIds = new Set(combinedTasks.map(task => task.id));
        const newMockTasks = mockTasks.filter(task => !existingIds.has(task.id));
        combinedTasks = [...combinedTasks, ...newMockTasks];
      }
      
      kanbanTasksCache[projectId] = combinedTasks;
      kanbanCacheTimestamp[projectId] = Date.now();
      
      // Đồng bộ với API trong background
      try {
        const data = await getProjectTasks(projectId);
        const tasks = transformTaskListData(data.data || data);
        const flat = flattenTasks(tasks);
        if (flat && flat.length > 0) {
          // Kết hợp dữ liệu API với dữ liệu hiện tại
          const existingIds = new Set(combinedTasks.map(task => task.id));
          const newApiTasks = flat.filter(task => !existingIds.has(task.id));
          const finalTasks = [...combinedTasks, ...newApiTasks];
          
          kanbanTasksCache[projectId] = finalTasks;
          kanbanCacheTimestamp[projectId] = Date.now();
          saveKanbanTasksByProjectId(projectId, finalTasks);
          return finalTasks;
        }
      } catch (err) {
        console.log("API sync failed, using combined storage/mock data:", err.message);
      }
      
      return combinedTasks;
    })();
    return kanbanPreloadPromiseMap[projectId];
  };

  // Khi projectId thay đổi, load lại task list tương ứng
  React.useEffect(() => {
    const now = Date.now();
    if (kanbanTasksCache[projectId] && (now - kanbanCacheTimestamp[projectId]) < KANBAN_CACHE_DURATION) {
      setTasks(kanbanTasksCache[projectId]);
      setLoading(false);
      return;
    }
    
    setLoading(true);
    
    // Lấy dữ liệu từ cả storage và API cùng lúc
    const storageTasks = getKanbanTasksByProjectId(projectId);
    const mockTasks = getMockKanbanData();
    
    // Kết hợp dữ liệu từ storage và mock data
    let combinedTasks = [];
    if (storageTasks.length > 0) {
      combinedTasks = [...storageTasks];
    }
    if (mockTasks.length > 0) {
      // Thêm mock data nếu chưa có trong storage
      const existingIds = new Set(combinedTasks.map(task => task.id));
      const newMockTasks = mockTasks.filter(task => !existingIds.has(task.id));
      combinedTasks = [...combinedTasks, ...newMockTasks];
    }
    
    // Hiển thị dữ liệu kết hợp ngay lập tức
    if (combinedTasks.length > 0) {
      setTasks(combinedTasks);
      kanbanTasksCache[projectId] = combinedTasks;
      kanbanCacheTimestamp[projectId] = Date.now();
      setLoading(false);
    } else {
      setLoading(false);
    }
    
    // Đồng bộ với API trong background
    if (kanbanPreloadPromiseMap[projectId]) {
      kanbanPreloadPromiseMap[projectId].then((data) => {
        if (data && data.length > 0) {
          // Kết hợp dữ liệu API với dữ liệu hiện tại
          const existingIds = new Set(combinedTasks.map(task => task.id));
          const newApiTasks = data.filter(task => !existingIds.has(task.id));
          const finalTasks = [...combinedTasks, ...newApiTasks];
          
          setTasks(finalTasks);
          kanbanTasksCache[projectId] = finalTasks;
          kanbanCacheTimestamp[projectId] = Date.now();
          saveKanbanTasksByProjectId(projectId, finalTasks);
        }
      });
    } else {
      getProjectTasks(projectId)
        .then((data) => {
          const tasks = transformTaskListData(data.data || data);
          const flat = flattenTasks(tasks);
          if (flat && flat.length > 0) {
            // Kết hợp dữ liệu API với dữ liệu hiện tại
            const existingIds = new Set(combinedTasks.map(task => task.id));
            const newApiTasks = flat.filter(task => !existingIds.has(task.id));
            const finalTasks = [...combinedTasks, ...newApiTasks];
            
            setTasks(finalTasks);
            kanbanTasksCache[projectId] = finalTasks;
            kanbanCacheTimestamp[projectId] = Date.now();
            saveKanbanTasksByProjectId(projectId, finalTasks);
          }
        })
        .catch((err) => {
          console.log("API sync failed, using combined storage/mock data:", err.message);
        });
    }
    // Lắng nghe sự kiện đồng bộ từ ListProject hoặc nơi khác
    const reloadHandler = () => {
      setLoading(true);
      setError(null);
      
      // Kết hợp dữ liệu từ storage và mock data
      const storageTasks = getKanbanTasksByProjectId(projectId);
      const mockTasks = getMockKanbanData();
      
      let combinedTasks = [];
      if (storageTasks.length > 0) {
        combinedTasks = [...storageTasks];
      }
      if (mockTasks.length > 0) {
        const existingIds = new Set(combinedTasks.map(task => task.id));
        const newMockTasks = mockTasks.filter(task => !existingIds.has(task.id));
        combinedTasks = [...combinedTasks, ...newMockTasks];
      }
      
      // Hiển thị dữ liệu kết hợp ngay lập tức
      if (combinedTasks.length > 0) {
        setTasks(combinedTasks);
        kanbanTasksCache[projectId] = combinedTasks;
        kanbanCacheTimestamp[projectId] = Date.now();
        setLoading(false);
      } else {
        setLoading(false);
      }
      
      // Đồng bộ với API trong background
      getProjectTasks(projectId)
        .then((data) => {
          const tasks = transformTaskListData(data.data || data);
          const flat = flattenTasks(tasks);
          if (flat && flat.length > 0) {
            // Kết hợp dữ liệu API với dữ liệu hiện tại
            const existingIds = new Set(combinedTasks.map(task => task.id));
            const newApiTasks = flat.filter(task => !existingIds.has(task.id));
            const finalTasks = [...combinedTasks, ...newApiTasks];
            
            setTasks(finalTasks);
            kanbanTasksCache[projectId] = finalTasks;
            kanbanCacheTimestamp[projectId] = Date.now();
            saveKanbanTasksByProjectId(projectId, finalTasks);
          }
        })
        .catch((err) => {
          console.log("API sync failed, using combined storage/mock data:", err.message);
        });
    };
    window.addEventListener("projectTasksUpdated", reloadHandler);
    return () =>
      window.removeEventListener("projectTasksUpdated", reloadHandler);
  }, [projectId]);

  React.useEffect(() => { if (projectId) preloadKanbanTasks(projectId); }, [projectId]);

  // Hàm xử lý khi kéo thả task giữa các cột
  const onDragEnd = async (result) => {
    if (!result.destination) return;
    const { source, destination, draggableId } = result;
    // Không cho phép kéo vào cột 'Chờ điều kiện tiên quyết'
    if (destination.droppableId === "Chờ điều kiện tiên quyết") return;
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    const movedTaskIndex = tasks.findIndex(
      (t) => t.id + t.status === draggableId
    );
    if (movedTaskIndex === -1) return;

    // Lưu trạng thái cũ để revert nếu cần
    const oldStatus = tasks[movedTaskIndex].status;
    const newStatus = kanbanColKeyToStatus[destination.droppableId];

    // Optimistic update: cập nhật state ngay
    const updatedTasks = [...tasks];
    updatedTasks[movedTaskIndex] = {
      ...updatedTasks[movedTaskIndex],
      status: newStatus,
    };
    setTasks(updatedTasks);

    try {
      await updateProjectTask(projectId, tasks[movedTaskIndex].id, {
        status: newStatus,
      });
      // Lưu vào storage
      saveKanbanTasksByProjectId(projectId, updatedTasks);
      // Không cần fetch lại toàn bộ, chỉ cần đồng bộ các nơi khác nếu có
      window.dispatchEvent(new Event("projectTasksUpdated"));
    } catch (err) {
      // Nếu API fail, vẫn lưu vào storage để offline
      saveKanbanTasksByProjectId(projectId, updatedTasks);
      // Không cần revert vì đã lưu vào storage
      window.dispatchEvent(new Event("projectTasksUpdated"));
    }
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="kanban-board">
        {loading ? (
          columns.map((col) => (
            <div className="kanban-column" key={col.key}>
                <div className="kanban-column-header">
                  <img
                    src={statusIcons[col.title]}
                    alt="icon"
                    className={`${col.key === "Chờ điều kiện tiên quyết" ? "kanban-icon-prerequisites" : ""}${col.key === "Hoàn tác công việc" ? "kanban-icon-undo" : ""}`}
                    style={{ width: 16, height: 16, marginRight: 4 }}
                  />
                  <span 
                    className={`kanban-column-title ${col.key === "Chờ điều kiện tiên quyết" ? "prerequisites" : ""}${col.key === "Hoàn tác công việc" ? "undo" : ""}`}
                  >
                    {col.title}
                  </span>
                  <span className="kanban-column-count">(0 công việc)</span>
                </div>
              <div className="kanban-tasks">
                {[1,2,3].map(i => (
                  <div key={i} className="kanban-task" style={{ opacity: 0.7 }}>
                    <div className="kanban-task-header">
                      <div style={{ width: 80, height: 16, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div className="kanban-task-priority">
                      <div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                    </div>
                    <div className="kanban-task-dates">
                      <div className="kanban-task-dates-row">
                        <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                      <div className="kanban-task-dates-row">
                        <div style={{ width: 80, height: 12, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                    </div>
                    <div className="kanban-task-footer">
                      <div className="kanban-task-members">
                        <div style={{ width: 60, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                      <div className="kanban-task-comments">
                        <div style={{ width: 40, height: 14, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                      </div>
                    </div>
                  </div>
                ))}
                <style>{`
                  @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.5; }
                    100% { opacity: 1; }
                  }
                `}</style>
              </div>
            </div>
          ))
        ) : error ? (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: 350,
              color: "red",
            }}
          >
            {error}
          </div>
        ) : tasks.length === 0 ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "60vh",
              color: "#ccc",
              width: "100%"
            }}
          >
            <img
              src={NoJobIcon}
              alt="Công việc trống"
              style={{ width: 600, marginBottom: 16 }}
            />
          </div>
        ) : (
          columns.map((col) => {
            const colTasks = tasks.filter(
              (t) => statusMap[t.status] === col.key
            );
            return (
              <Droppable droppableId={col.key} key={col.key}>
                {(provided) => (
                  <div
                    className={`kanban-column${col.key === "Chờ điều kiện tiên quyết" ? " kanban-column-prerequisites" : ""}`}
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                      <div className="kanban-column-header">
                        <img
                          src={statusIcons[col.title]}
                          alt="icon"
                          className={`${col.key === "Chờ điều kiện tiên quyết" ? "kanban-icon-prerequisites" : ""}${col.key === "Hoàn tác công việc" ? "kanban-icon-undo" : ""}`}
                          style={{ width: 16, height: 16, marginRight: 4 }}
                        />
                        <span 
                          className={`kanban-column-title ${col.key === "Chờ điều kiện tiên quyết" ? "prerequisites" : ""}${col.key === "Hoàn tác công việc" ? "undo" : ""}`}
                        >
                          {col.title}
                        </span>
                        <span className="kanban-column-count">
                          ({colTasks.length} công việc)
                        </span>
                      </div>
                    <div className="kanban-tasks">
                      {colTasks.map((task, idx) => (
                        <Draggable
                          draggableId={task.id + task.status}
                          index={idx}
                          key={task.id + task.status}
                          isDragDisabled={col.key === "Chờ điều kiện tiên quyết"}
                        >
                          {(provided, snapshot) => (
                            <div
                              className={`kanban-task${col.key === "Chờ điều kiện tiên quyết" ? " kanban-task-prerequisites" : ""}`}
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                boxShadow: snapshot.isDragging
                                  ? "0 4px 16px rgba(0,0,0,0.15)"
                                  : undefined,
                              }}
                            >
                              <div className="kanban-task-header">
                                <span className="kanban-task-title">
                                  {task.name}
                                </span>
                                {getStatusLabel(col.key) && (
                                  <span 
                                    className={`kanban-task-status ${
                                      col.key === "Đang chờ" ? "pending" :
                                      col.key === "Đang tiến hành" ? "in-progress" :
                                      col.key === "Hoàn thành" ? "completed" :
                                      col.key === "Chờ điều kiện tiên quyết" ? "prerequisites" : "undo"
                                    }`}
                                  >
                                    {getStatusLabel(col.key)}
                                  </span>
                                )}
                              </div>
                              
                               {/* Hiển thị thông tin "con của" và "phụ thuộc" cho kanban "Chờ điều kiện tiên quyết" */}
                               {col.key === "Chờ điều kiện tiên quyết" && (
                                 <div className="kanban-task-dependencies">
                                   <div className="kanban-task-dependency-row">
                                     <img
                                       src={LinkIcon}
                                       alt="con của"
                                       className="kanban-task-dependency-icon"
                                     />
                                     <span>
                                       Con của: {task.parentTask || "TSK-01: Phát triển hệ thống"}
                                     </span>
                                   </div>
                                   <div className="kanban-task-dependency-row">
                                     <img
                                       src={LinkIconn}
                                       alt="phụ thuộc"
                                       className="kanban-task-dependency-icon"
                                     />
                                     <span>
                                       Phụ thuộc: chưa xong
                                     </span>
                                   </div>
                                 </div>
                               )}
                              
                              <div className="kanban-task-priority">
                                {(() => {
                                  const priorityLabel = priorityMap[task.priority] || "Thấp";
                                  return <>
                                    <img
                                      src={priorityIcons[priorityLabel]}
                                      alt="icon"
                                      style={{ width: 16, marginRight: 4 }}
                                    />
                                    <span>{priorityLabel}</span>
                                  </>;
                                })()}
                              </div>
                              
                              {/* Chỉ hiển thị ngày tháng nếu không phải kanban "Đang chờ" hoặc "Chờ điều kiện tiên quyết" */}
                              {shouldShowDates(col.key) ? (
                                <div className="kanban-task-dates">
                                  <div className="kanban-task-dates-row">
                                    <span className="kanban-task-dates-label">
                                      Ngày tạo
                                    </span>
                                    <img
                                      src={CreationDateIcon}
                                      alt="ngày tạo"
                                      className="kanban-task-dates-icon"
                                    />
                                    <span className="kanban-task-dates-value">
                                      {task.startDate || "19/6/2025"}
                                    </span>
                                  </div>
                                  <div className="kanban-task-dates-row">
                                    <span className="kanban-task-dates-label">
                                      Ngày kết thúc
                                    </span>
                                    <img
                                      src={EndDateIcon}
                                      alt="ngày kết thúc"
                                      className="kanban-task-dates-icon"
                                    />
                                    <span className="kanban-task-dates-value">
                                      {task.dueDate || "19/8/2025"}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <div className="kanban-task-dates">
                                                                     <div className="kanban-task-dates-row">
                                     <span className="kanban-task-dates-label">
                                       Ngày tạo
                                     </span>
                                     <img
                                       src={CreationDateIcon}
                                       alt="ngày tạo"
                                       className="kanban-task-dates-icon"
                                     />
                                     <span className="kanban-task-dates-value empty">
                                       --/--/----
                                     </span>
                                   </div>
                                                                     <div className="kanban-task-dates-row">
                                     <span className="kanban-task-dates-label">
                                       Ngày kết thúc
                                     </span>
                                     <img
                                       src={EndDateIcon}
                                       alt="ngày kết thúc"
                                       className="kanban-task-dates-icon"
                                     />
                                     <span className="kanban-task-dates-value empty">
                                       --/--/----
                                     </span>
                                   </div>
                                </div>
                              )}
                              
                              <div className="kanban-task-footer">
                                <div className="kanban-task-members">
                                  Thành viên:{" "}
                                  {task.assignee && task.assignee.length > 0 ? (
                                    task.assignee.map((member, i) => {
                                      // Fallback: nếu không có avatar hoặc rỗng thì dùng randomuser.me
                                      let avatarUrl = member.avatar && member.avatar.trim() !== "" ? member.avatar : "https://randomuser.me/api/portraits/men/1.jpg";
                                      return (
                                        <img
                                          key={i}
                                          src={avatarUrl}
                                          alt={member.name || "avatar"}
                                          title={member.name || "Thành viên"}
                                          style={{
                                            width: "20px",
                                            height: "20px",
                                            borderRadius: "50%",
                                            objectFit: "cover",
                                            marginLeft: i > 0 ? "4px" : "0",
                                            background: "#f0f0f0",
                                            border: "1px solid #e0e0e0",
                                            cursor: "pointer"
                                          }}
                                          onError={e => { 
                                            e.currentTarget.onerror = null; 
                                            e.currentTarget.src = "https://randomuser.me/api/portraits/men/1.jpg"; 
                                          }}
                                        />
                                      );
                                    })
                                  ) : (
                                    <span style={{ color: "#999", fontStyle: "italic" }}>
                                      Chưa giao việc
                                    </span>
                                  )}
                                </div>
                                <div className="kanban-task-comments">
                                  <img
                                    src={CommentIcon}
                                    alt="cmt"
                                    style={{ width: 16, marginRight: 4 }}
                                  />{" "}
                                  {task.comments || 6}
                                </div>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            );
          })
        )}
      </div>
    </DragDropContext>
  );
}

export default Kanban;
