.searchall-container {
  background: #fff;
  padding: 0;
  border-radius: 12px;
}

.searchall-block {
  border: 1px solid #f3f3f3;
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 0;
}

.searchall-row {
  display: flex;
  align-items: stretch;
  padding: 20px 24px;
  gap: 0;
}

.searchall-col2 {
  flex: 2;
}

.searchall-col1 {
  flex: 1;
}

.searchall-flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.searchall-label {
  font-weight: 600;
  font-size: 17px;
  margin-bottom: 2px;
}

.searchall-sublabel {
  color: #888;
  font-size: 14px;
  margin-bottom: 2px;
}

.searchall-celltitle {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 2px;
}

.searchall-cellvalue {
  color: #888;
  font-size: 14px;
}

.searchall-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #e0e0e0;
  background: #fff;
  object-fit: cover;
}

.searchall-status {
  color: #4a90e2;
  display: flex;
  align-items: center;
  gap: 4px;
}

.searchall-status-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.badge {
  border-radius: 12px;
  font-weight: 500;
  font-size: 15px;
  padding: 4px 16px;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.badge-blue {
  background: #eaf3ff;
  color: #4a90e2;
}

.badge-green {
  background: #eaf3f3;
  color: #27ae60;
}

.badge-status {
  background: #eafaf3;
  color: #27ae60;
}

.badge-orange {
  background: #fff3ed;
  color: #f2994a;
}

.searchall-member {
  border: 1px solid #f3f3f3;
  border-radius: 12px;
  margin-bottom: 12px;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.searchall-member-info {
  display: flex;
  align-items: center;
  min-width: 320px;
}

.searchall-member-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 16px;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #e0e0e0;
  background: #fff;
}

.searchall-member-name {
  font-weight: 600;
  font-size: 16px;
}

.searchall-member-email,
.searchall-member-id,
.searchall-member-role,
.searchall-member-date {
  color: #888;
  font-size: 14px;
}

.searchall-member-attrs {
  display: flex;
  align-items: center;
  gap: 100px;
  flex: 1;
  justify-content: flex-end;
}

.searchall-note {
  border: 1px solid #f3f3f3;
  border-radius: 12px;
  padding: 20px 24px;
  display: flex;
  align-items: center;
}

.searchall-note-icon {
  background: #eaf3ff;
  color: #4a90e2;
  border-radius: 8px;
  padding: 8px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.searchall-note-content {
  flex: 1;
}

.searchall-note-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 2px;
}

.searchall-note-desc {
  color: #888;
  font-size: 14px;
}

.searchall-note-desc-label {
  font-weight: 600;
}

/* Loader animation */
@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}
