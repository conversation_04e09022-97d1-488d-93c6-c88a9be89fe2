import React, { useState, useRef, useEffect } from 'react';
import '../../styles/CreateNote.css';
import closeLoc from '../../assets/closeLoc.svg';
import loadFileIcon from "../../assets/loadfile.svg";
import fileTextIcon from "../../assets/file-text.svg";
import DocsIcon from '../../assets/docs.svg';
import PdfIcon from '../../assets/pdf.svg';
import ImageIcon from '../../assets/image.svg';
import x from '../../assets/x.svg';
import { validateNoteForm } from '../../utils/validation';
import { showToast } from '../../utils/toastUtils';
import { PERSONAL_NOTES_ENDPOINTS } from '../../api/endpoints';

const CreateNote = ({ onClose, onNoteCreated }) => {
  
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [errors, setErrors] = useState({ title: '', content: '' });
  const [isCreating, setIsCreating] = useState(false);
  const modalRef = useRef(null);

  const validate = () => {
    const validationErrors = validateNoteForm({ title, content });
    setErrors(validationErrors);
    return !validationErrors.title && !validationErrors.content;
  };

   const handleAddFile = () => {
    fileInputRef.current?.click();
  };

   const handleFileUpload = (e) => {
  const files = Array.from(e.target.files);
  if (files.length === 0) return;
  
  // Validate file size (max 20MB per file)
  const validFiles = files.filter(file => {
    if (file.size > 20 * 1024 * 1024) {
      showToast(`File ${file.name} quá lớn (tối đa 20MB)`, 'error');
      return false;
    }
    return true;
  });

  if (validFiles.length > 0) {
    console.log('File chọn:', validFiles);
    setAttachments(prev => [...prev, ...validFiles]);
    showToast(`Đã thêm ${validFiles.length} tệp`, 'success');
  }
  
  e.target.value = "";
};

  const [formData, setFormData] = useState({
  name: "",
  description: "",
  startDate: "",
  endDate: "",
  priority: "medium",
  members: [],
});
const [attachments, setAttachments] = useState([]);

  const removeFile = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Hàm để chọn icon dựa trên loại file
  const getFileIcon = (fileName) => {
    const ext = (fileName || '').split('.').pop().toLowerCase();
    if (ext === 'pdf') return PdfIcon;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return ImageIcon;
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'].includes(ext)) return DocsIcon;
    return DocsIcon; // Mặc định cho các loại khác
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    if (!title.trim()) {
      showToast('Vui lòng nhập tiêu đề ghi chú!', 'error');
      return;
    }

    if (isCreating) return; // Prevent double submission
    setIsCreating(true);

    try {
      const token = localStorage.getItem('token');
      const formData = new FormData();
      
      // Thêm text data
      formData.append('title', title.trim());
      formData.append('description', content.trim());
      
      // Thêm files nếu có
      attachments.forEach(file => {
        formData.append('files', file);
      });

      console.log('Sending FormData with:', {
        title: title.trim(),
        description: content.trim(),
        filesCount: attachments.length
      });

      // Gửi request đến API mới
      const response = await fetch(PERSONAL_NOTES_ENDPOINTS.CREATE_NOTE, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
        signal: AbortSignal.timeout(60000),
      });

      const result = await response.json();
      console.log('API response:', result);

      if (response.ok && result.success) {
        showToast(attachments.length > 0 ? 'Tạo ghi chú với files thành công!' : 'Tạo ghi chú thành công!', 'success');
        
        // Callback để parent component refresh data
        if (onNoteCreated) {
          onNoteCreated(result);
        }

        // Reset form
        setTitle('');
        setContent('');
        setAttachments([]);
        if (fileInputRef.current) fileInputRef.current.value = "";
        onClose && onClose();
      } else {
        const errorMessage = result?.message || 'Tạo ghi chú thất bại!';
        showToast(errorMessage, 'error');
        console.error('Create note failed:', response.status, response.statusText, result);
      }

    } catch (error) {
      console.error('Lỗi tạo ghi chú:', error);
      if (error.name === 'TimeoutError') {
        showToast('Timeout - file quá lớn hoặc kết nối chậm!', 'error');
      } else if (error.name === 'AbortError') {
        showToast('Tạo ghi chú bị hủy!', 'error');
      } else {
        showToast('Có lỗi khi tạo ghi chú!', 'error');
      }
    } finally {
      setIsCreating(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose && onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

    const fileInputRef = useRef(null);

  return (
    <div className="create-note-modal">
      <form onSubmit={handleSubmit} className="create-note-form" ref={modalRef}>
        <div className="create-note-header">
          <h2 className="create-note-title">Tạo ghi chú mới</h2>
          <button type="button" onClick={onClose} className="create-note-close">
            <img src={closeLoc} alt="Close" />
          </button>
        </div>
        <div className="create-note-input-container">
          <input
            type="text"
            placeholder="Tiêu đề"
            value={title}
            onChange={e => setTitle(e.target.value)}
            className={`create-note-input ${errors.title ? 'create-note-input-error' : ''}`}
          />
          {errors.title && <div className="create-note-error-message">{errors.title}</div>}
        </div>
        <div className="create-note-textarea-container">
          <textarea
            placeholder="Nội dung ghi chú"
            value={content}
            onChange={e => setContent(e.target.value)}
            rows={5}
            className={`create-note-textarea ${errors.content ? 'create-note-input-error' : ''}`}
          />
          {errors.content && <div className="create-note-error-message">{errors.content}</div>}
        </div>
        <div className="job-panel-rows">
            <div className="job-panel-label">Tệp đính kèm</div>
            <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
              <div className="file-upload-custom" onClick={handleAddFile}>
                <input
                  type="file"
                  multiple
                  accept="*"
                  onChange={handleFileUpload}
                  style={{ display: "none" }}
                  ref={fileInputRef}
                />
                <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
              </div>
              {attachments.length > 0 && (
                <div className="job-panel-file-list" style={{width: '100%'}}>
                  {attachments.map((file, idx) => (
                    <div className="file-item" key={idx}>
                      <img src={getFileIcon(file.name)} alt="file" className="job-panel-file-icon" />
                      <div className="job-panel-file-info">
                        <span className="job-panel-file-name">{file.name}</span>
                        <span className="job-panel-file-size">{formatFileSize(file.size)}</span>
                      </div>
                      <button type="button" className="job-panel-remove-file-btn" onClick={() => removeFile(idx)} title="Xóa tệp">
                        <img src={x} alt="delete" style={{ width: 16, height: 16 }} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        <div className="create-note-buttons">
          <button type="button" className="create-note-cancel" onClick={onClose}>
            Hủy
          </button>
          <button type="submit" className="create-note-submit" disabled={isCreating}>
            {isCreating ? 'Đang tạo...' : 'Tạo'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNote;
