import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import "../../styles/Sidebar.css";
import LogoSDTC from "../../assets/icon-sidebar/sdtc.png";
import HomeIcon from "../../assets/icon-sidebar/trangchu.svg";
import TaskIcon from "../../assets/icon-sidebar/congviec.svg";
import NoteIcon from "../../assets/icon-sidebar/ghichu.svg";
import TrashIcon from "../../assets/icon-sidebar/thungrac.svg";
import DropdownIcon from "../../assets/icon-sidebar/dropdown.svg";
import ArrowIcon from "../../assets/icon-sidebar/arrow.svg";
import StatisticalIcon from "../../assets/time.svg";
import StatisticalReportIcon from "../../assets/statistical.svg";
import ActivityLog from "../../assets/activitylog.svg";
import UsersIcon from "../../assets/users.svg";
import DetailIcon from "../../assets/detail.svg";
import ProjectCreate from "./ProjectCreate";
import ProjectDetailPopup from "./ProjectDetailPopup";
import { deleteProject, updateProject } from "../../api/projectManagement";
import DepartmentIcon from '../../assets/department.svg';
import MaintenanceIcon from '../../assets/deployment.svg';

// Cache để tránh gọi API nhiều lần
let projectsCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // 10 giây

// Preload data for instant loading
let preloadPromise = null;

const DashboardSidebar = ({ showLeaderActions = false }) => {
  const location = useLocation();

  // Lấy thông tin user hiện tại từ localStorage
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();

  // Hàm kiểm tra quyền truy cập menu
  const hasMenuAccess = (menuType) => {
    // Nếu là admin thì luôn có toàn quyền truy cập mọi menu
    if (userRole === "admin") return true;
    switch (menuType) {
      case "activity-log":
        // Chỉ admin mới có quyền xem nhật ký hoạt động
        return userRole === "admin";
      case "hr":
        // CEO, HR, departmentHead có quyền quản lý nhân sự
        return ["ceo", "hr", "departmenthead"].includes(userRole);
      case "statistical":
        // CEO, HR, Leader, departmentHead có quyền xem báo cáo thống kê
        return ["ceo", "hr", "leader", "departmenthead"].includes(userRole);
      case "create-project":
        // CEO, Leader, departmentHead có quyền tạo dự án (HR, Staff không có)
        return ["ceo", "leader", "departmenthead"].includes(userRole);
      default:
        return true; // Các menu khác mặc định cho phép truy cập
    }
  };

  // Khởi tạo state cho trạng thái mở/đóng của menu "Không gian làm việc" từ localStorage, mặc định mở
  const [isWorkspaceOpen, setIsWorkspaceOpen] = useState(() => {
    const saved = localStorage.getItem("isWorkspaceOpen");
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Khởi tạo state cho trạng thái mở/đóng của menu "Dự án công ty" từ localStorage, mặc định mở
  const [isProjectOpen, setIsProjectOpen] = useState(() => {
    const saved = localStorage.getItem("isProjectOpen");
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Khởi tạo state cho trạng thái mở/đóng của Đội ngũ từ localStorage, mặc định đóng
  const [isTeamOpen, setIsTeamOpen] = useState(() => {
    const saved = localStorage.getItem("isTeamOpen");
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Khởi tạo state cho trạng thái mở/đóng của ChopChat từ localStorage, mặc định đóng
  const [isChopChatOpen, setIsChopChatOpen] = useState(() => {
    const saved = localStorage.getItem("isChopChatOpen");
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Khởi tạo state cho trạng thái mở/đóng của Đội ngũ ChopChat từ localStorage, mặc định đóng
  const [isChopChatTeamOpen, setIsChopChatTeamOpen] = useState(() => {
    const saved = localStorage.getItem("isChopChatTeamOpen");
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Khởi tạo state cho trạng thái mở/đóng của Báo cáo & thống kê từ localStorage, mặc định đóng
  const [isStatisticalOpen, setIsStatisticalOpen] = useState(() => {
    const saved = localStorage.getItem("isStatisticalOpen");
    return saved !== null ? JSON.parse(saved) : false;
  });

  // State lưu danh sách dự án
  const [projects, setProjects] = useState([]);
  // State hiển thị loading khi lấy dữ liệu dự án
  const [loading, setLoading] = useState(true);
  // State lưu đường dẫn đang active (mặc định là trang chủ)
  const [activeItem, setActiveItem] = useState(location.pathname); // Initialize with current path
  // State để hiển thị form tạo dự án
  const [showCreateProject, setShowCreateProject] = useState(false);
  // State để quản lý popup detail của từng dự án
  const [showDetailPopup, setShowDetailPopup] = useState(null);

  // Lưu trạng thái mở/đóng menu vào localStorage khi thay đổi
  useEffect(() => {
    localStorage.setItem("isWorkspaceOpen", JSON.stringify(isWorkspaceOpen));
  }, [isWorkspaceOpen]);

  useEffect(() => {
    localStorage.setItem("isProjectOpen", JSON.stringify(isProjectOpen));
  }, [isProjectOpen]);

  // Lưu trạng thái mở/đóng của Đội ngũ và ChopChat
  useEffect(() => {
    localStorage.setItem("isTeamOpen", JSON.stringify(isTeamOpen));
  }, [isTeamOpen]);

  useEffect(() => {
    localStorage.setItem("isChopChatOpen", JSON.stringify(isChopChatOpen));
  }, [isChopChatOpen]);

  useEffect(() => {
    localStorage.setItem(
      "isChopChatTeamOpen",
      JSON.stringify(isChopChatTeamOpen)
    );
  }, [isChopChatTeamOpen]);

  useEffect(() => {
    localStorage.setItem("isStatisticalOpen", JSON.stringify(isStatisticalOpen));
  }, [isStatisticalOpen]);

  // Hàm lấy dữ liệu dự án từ API thật (KHÔNG dùng localStorage, không có dữ liệu giả)
  const fetchProjects = async () => {
    // Kiểm tra cache trước
    const now = Date.now();
    if (projectsCache && now - cacheTimestamp < CACHE_DURATION) {
      return projectsCache;
    }

    const { getEndpointsByRole, getCurrentUserRole } = await import(
      "../../api/endpoints"
    );
    const FileIcon = (await import("../../assets/icon-sidebar/tailieu.svg"))
      .default;
    const TeamIcon = (await import("../../assets/icon-sidebar/doingu.svg"))
      .default;
    const TaskIcon = (await import("../../assets/icon-sidebar/congviec.svg"))
      .default;
    const role = getCurrentUserRole();
    const endpoints = getEndpointsByRole(role);
    const apiUrl = endpoints.ALL_PROJECTS || endpoints.PROJECTS;
    if (!apiUrl) return [];
    try {
      const token =
        localStorage.getItem("token") || localStorage.getItem("accessToken");
      // Thêm parameter để lấy tất cả dự án
      const urlWithParams = `${apiUrl}?limit=1000`;
      const res = await fetch(urlWithParams, {
        headers: {
          "Content-Type": "application/json",
          ...(token
            ? { Authorization: `Bearer ${token.replace(/['"]+/g, "")}` }
            : {}),
        },
      });
      if (!res.ok) throw new Error("API error");
      const data = await res.json();
      const projects = (Array.isArray(data.data) ? data.data : data).map(
        (project) => ({
          id: project._id || project.id,
          name: project.name,
          description: project.description,
          type: "project",
          hasSubmenu: true,
          isOpen: false,
          icon: "hashtag",
          leaderId: project.leaderId?._id || project.leaderId,
          departmentId: project.departmentId?._id || project.departmentId,
          children: [
            {
              id: `${project._id || project.id}-documents`,
              name: "Tài liệu",
              path: `/projects/${project._id || project.id}/documents`,
              icon: FileIcon,
              type: "page",
            },
            {
              id: `${project._id || project.id}-team`,
              name: "Đội ngũ",
              type: "dropdown",
              icon: TeamIcon,
              isOpen: false,
              children: [
                {
                  id: `${project._id || project.id}-team-members`,
                  name: "Thành viên",
                  path: `/projects/${project._id || project.id}/team/members`,
                  icon: TeamIcon,
                  type: "page",
                },
                {
                  id: `${project._id || project.id}-team-work`,
                  name: "Công việc",
                  path: `/projects/${project._id || project.id}/work/list`,
                  icon: TaskIcon,
                  type: "page",
                },
              ],
            },
          ],
        })
      );

      // Lọc dự án cho HR chỉ thấy dự án thuộc phòng ban của mình
      let filteredProjects = projects;
      if (userRole === "hr" && currentUser.departmentId) {
        filteredProjects = projects.filter(
          (project) =>
            project.departmentId === currentUser.departmentId ||
            (typeof project.departmentId === "object" &&
              project.departmentId?._id === currentUser.departmentId)
        );
      }

      // Lưu vào cache
      projectsCache = filteredProjects;
      cacheTimestamp = now;

      return filteredProjects;
    } catch (err) {
      console.warn("Error fetching projects:", err);
      return [];
    }
  };

  // Preload function
  const preloadProjects = async () => {
    if (preloadPromise) return preloadPromise;

    preloadPromise = (async () => {
      try {
        const projects = await fetchProjects();
        return projects;
      } catch (err) {
        console.warn("Error preloading projects:", err);
        return [];
      }
    })();

    return preloadPromise;
  };

  // Lấy dữ liệu dự án từ API khi component mount
  useEffect(() => {
    const getProjects = async () => {
      setLoading(true);

      try {
        // Use preloaded data if available
        let data = [];
        if (preloadPromise) {
          data = await preloadPromise;
        } else {
          data = await fetchProjects();
        }

        setProjects(data);
      } catch (err) {
        console.warn("Error loading projects:", err);
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    getProjects();
  }, []);

  // Start preloading when component mounts
  useEffect(() => {
    preloadProjects();
  }, []);

  // Lưu trạng thái của các dự án vào localStorage mỗi khi chúng thay đổi
  useEffect(() => {
    if (projects.length > 0) {
      const projectStates = projects.map((project) => {
        const projectState = {
          id: project.id,
          isOpen: project.isOpen,
        };
        return projectState;
      });

      localStorage.setItem("projectStates", JSON.stringify(projectStates));
    }
  }, [projects]);

  // Cập nhật activeItem dựa trên đường dẫn hiện tại
  useEffect(() => {
    setActiveItem(location.pathname);

    // Tự động mở các menu dựa trên đường dẫn hiện tại
    if (location.pathname.includes("/projects/1/")) {
      setIsProjectOpen(true);

      // Nếu đường dẫn liên quan đến team hoặc công việc, mở menu Đội ngũ
      if (
        location.pathname.includes("/team/") ||
        location.pathname.includes("/work/")
      ) {
        setIsTeamOpen(true);
      }
    }
  }, [location]);

  // Hàm xử lý khi mở/đóng các submenu của project
  const toggleProjectSubmenu = (projectId) => {
    setProjects(
      projects.map((project) => {
        if (project.id === projectId) {
          return { ...project, isOpen: !project.isOpen };
        }
        return project;
      })
    );
  };

  // Kiểm tra xem một đường dẫn có đang active không
  const isPathActive = (path) => {
    // Trường hợp đặc biệt cho trang chủ
    if (path === "/") {
      return activeItem === "/";
    }

    // Trường hợp đặc biệt cho "Công việc của tôi"
    if (path === "/myjob") {
      // Chỉ active khi đúng là route /myjob, không phải route con của project
      return (
        activeItem.startsWith("/myjob") && !activeItem.includes("/projects/")
      );
    }

    // Kiểm tra chính xác đường dẫn
    if (activeItem === path) return true;

    // Kiểm tra đường dẫn con (nếu là đường dẫn cha)
    if (path !== "/" && path !== "/myjob" && activeItem.startsWith(path))
      return true;

    return false;
  };

  // Xử lý khi click vào menu item
  const handleMenuItemClick = (path) => {
    setActiveItem(path);
  };

  // Thêm event listener để đóng popup khi click bên ngoài
  useEffect(() => {
    const handleClickOutside = () => {
      setShowDetailPopup(null);
    };

    if (showDetailPopup) {
      document.addEventListener("click", handleClickOutside);
      return () => document.removeEventListener("click", handleClickOutside);
    }
  }, [showDetailPopup]);

  // Hàm xử lý refresh projects sau khi sửa
  const handleEditProject = async (oldName, newName) => {
    // Nếu là signal refresh từ EditProjectForm
    if (oldName === 'refresh') {
      // Refreshing projects after edit
      try {
        const data = await fetchProjects();
        const filtered = data.filter(
          (p) => p.name !== "SDTC" && p.name !== "ChopChat"
        );
        setProjects(
          filtered.map((p) => ({
            ...p,
            isOpen: true,
            children: p.children
              ? p.children.map((child) => ({
                  ...child,
                  isOpen: child.type === "dropdown" ? false : undefined,
                }))
              : undefined,
          }))
        );
      } catch (err) {
        console.error("Error refreshing projects:", err);
      }
      return;
    }
    
    // Legacy call - không làm gì
            // DEPRECATED: handleEditProject legacy call ignored
  };

  // Hàm xử lý xóa dự án
  const handleDeleteProject = async (projectName) => {
          // Deleting project

    // Không cho phép xóa SDTC và ChopChat (dự án hệ thống)
    if (projectName === "SDTC" || projectName === "ChopChat") {
              // Cannot delete system project
      alert("Không thể xóa dự án hệ thống!");
      return;
    }

    const project = projects.find((p) => p.name === projectName);
    if (!project) return;

    try {
      await deleteProject(project.id);
      // Reload lại danh sách dự án sau khi xóa
      const data = await fetchProjects();
      setProjects(data);
      // Có thể thêm toast/thông báo thành công ở đây
      // Project deleted successfully
    } catch (err) {
      alert("Xóa dự án thất bại: " + (err.message || ""));
    }
  };
  return (
    <>
      {/* Hiển thị popup detail khi showDetailPopup có giá trị */}
      {showDetailPopup && (
        <ProjectDetailPopup
          projectName={showDetailPopup.projectName}
          projectId={showDetailPopup.projectId}
          position={showDetailPopup.position}
          onClose={() => setShowDetailPopup(null)}
          onEditProject={handleEditProject}
          onDeleteProject={handleDeleteProject}
          leaderId={showDetailPopup.leaderId}
          departmentId={showDetailPopup.departmentId}
          currentUserId={currentUser._id || currentUser.id}
          currentUserRole={userRole}
          currentUserDepartmentId={currentUser.departmentId}
        />
      )}

      {/* Hiển thị form tạo dự án khi showCreateProject = true */}
      {showCreateProject && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0,0,0,0.15)",
            zIndex: 1000,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onClick={() => setShowCreateProject(false)}
        >
          <div onClick={(e) => e.stopPropagation()}>
            <ProjectCreate
              onCancel={() => setShowCreateProject(false)}
              onCreate={async (project) => {
                setShowCreateProject(false);
                try {
                  // Reload projects sau khi tạo thành công
                  const data = await fetchProjects();
                  const filtered = data.filter(
                    (p) => p.name !== "SDTC" && p.name !== "ChopChat"
                  );
                  setProjects(
                    filtered.map((p) => ({
                      ...p,
                      isOpen: true,
                      // Đảm bảo các children dropdown có trạng thái isOpen
                      children: p.children
                        ? p.children.map((child) => ({
                            ...child,
                            isOpen:
                              child.type === "dropdown" ? false : undefined,
                          }))
                        : undefined,
                    }))
                  );

                  // Hiển thị thông báo thành công (có thể thêm toast notification)
                  // Dự án đã được tạo thành công
                } catch (error) {
                  console.error("Error reloading projects:", error);
                }
              }}
            />
          </div>
        </div>
      )}
      <aside className="dashboard-sidebar">
        <div className="dashboard-logo">
          <img src={LogoSDTC} alt="Sea Dragon Technology" />
        </div>

        <nav className="dashboard-menu">
          <ul>
            <li
              className={isPathActive("/") ? "active" : ""}
              style={{
                padding: 0,
                margin: 0,
                ...(isPathActive("/") && {
                  paddingLeft: 6,
                  paddingRight: 6,
                  borderRadius: 6,
                }),
              }}
            >
              <Link
                to="/"
                className="sidebar-home-link"
                onClick={() => handleMenuItemClick("/")}
              >
                <img src={HomeIcon} alt="Home" className="sidebar-icon" />
                <span>Trang chủ</span>
              </Link>
            </li>

            <li className="dropdown">
              <div
                className="menu-item"
                onClick={() => setIsWorkspaceOpen(!isWorkspaceOpen)}
              >
                <span>Không gian làm việc</span>
                <span className="dropdown-icon">
                  <img
                    src={isWorkspaceOpen ? DropdownIcon : ArrowIcon}
                    alt="dropdown"
                  />
                </span>
              </div>
              {isWorkspaceOpen && (
                <ul className="submenu">
                  <li
                    className={isPathActive("/myjob") ? "active" : ""}
                    style={
                      isPathActive("/myjob")
                        ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                        : {}
                    }
                  >
                    <Link
                      to="/myjob"
                      onClick={() => handleMenuItemClick("/myjob")}
                    >
                      <img
                        src={TaskIcon}
                        alt="Tasks"
                        className="sidebar-icon"
                      />
                      <span>Công việc của tôi</span>
                    </Link>
                  </li>
                  <li
                    className={isPathActive("/notes") ? "active" : ""}
                    style={
                      isPathActive("/notes")
                        ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                        : {}
                    }
                  >
                    <Link
                      to="/notes"
                      onClick={() => handleMenuItemClick("/notes")}
                    >
                      <img
                        src={NoteIcon}
                        alt="Notes"
                        className="sidebar-icon"
                      />
                      <span>Ghi chú cá nhân</span>
                    </Link>
                  </li>
                </ul>
              )}
            </li>

            {/* Menu Dự án công ty luôn hiển thị, chỉ ẩn danh sách dự án với HR */}
            <li className="dropdown">
              <div
                className="menu-item"
                onClick={() => setIsProjectOpen(!isProjectOpen)}
              >
                <span className="dropdown-label">
                  Dự án công ty
                  <span
                    className="dropdown-icon"
                    style={{
                      marginLeft: 4,
                      marginRight: 0,
                      display: "inline-flex",
                      alignItems: "center",
                    }}
                  >
                    <img
                      src={isProjectOpen ? DropdownIcon : ArrowIcon}
                      alt="dropdown"
                      style={{ margin: 0, padding: 0, width: 16, height: 16 }}
                    />
                  </span>
                  {hasMenuAccess("create-project") && (
                    <span
                      className="plus-icon"
                      style={{
                        marginLeft: 85,
                        color: "#7c7c7c",
                        fontWeight: "bold",
                        fontSize: 18,
                        cursor: "pointer",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowCreateProject(true);
                      }}
                    >
                      +
                    </span>
                  )}
                </span>
              </div>
              {isProjectOpen && (
                <ul className="submenu">
                  {/* Loading skeleton */}
                  {loading && (
                    <>
                      {[1, 2, 3].map((i) => (
                        <li
                          key={`skeleton-${i}`}
                          className="dropdown"
                          style={{ opacity: 0.7 }}
                        >
                          <div className="menu-item">
                            <span className="sidebar-hashtag">#</span>
                            <span
                              className="dropdown-label"
                              style={{
                                display: "flex",
                                alignItems: "center",
                                width: "100%",
                              }}
                            >
                              <span
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                }}
                              >
                                <div
                                  style={{
                                    width: "120px",
                                    height: "16px",
                                    background: "#f0f0f0",
                                    borderRadius: "4px",
                                    animation:
                                      "pulse 1.5s ease-in-out infinite",
                                  }}
                                ></div>
                                <span
                                  className="dropdown-icon"
                                  style={{
                                    marginLeft: 4,
                                    marginRight: 0,
                                    display: "inline-flex",
                                    alignItems: "center",
                                  }}
                                >
                                  <img
                                    src={ArrowIcon}
                                    alt="dropdown"
                                    style={{
                                      margin: 0,
                                      padding: 0,
                                      width: 16,
                                      height: 16,
                                    }}
                                  />
                                </span>
                              </span>
                            </span>
                          </div>
                        </li>
                      ))}
                      <style>{`
                        @keyframes pulse {
                          0% { opacity: 1; }
                          50% { opacity: 0.5; }
                          100% { opacity: 1; }
                        }
                      `}</style>
                    </>
                  )}
                  {/* Các dự án khác từ API, chỉ hiện với user không phải HR */}
                  {!loading &&
                    userRole !== "hr" &&
                    projects.map((project) => (
                      <li key={project.id} className="dropdown">
                        <div
                          className="menu-item"
                          onClick={(e) => {
                            if (e.target.closest(".detail-icon")) return;
                            toggleProjectSubmenu(project.id);
                          }}
                          onMouseEnter={(e) => {
                            const detailIcon =
                              e.currentTarget.querySelector(".detail-icon");
                            if (detailIcon) detailIcon.style.opacity = "1";
                          }}
                          onMouseLeave={(e) => {
                            const detailIcon =
                              e.currentTarget.querySelector(".detail-icon");
                            if (detailIcon) detailIcon.style.opacity = "0";
                          }}
                        >
                          {project.icon === "hashtag" ? (
                            <span className="sidebar-hashtag">#</span>
                          ) : (
                            <img
                              src={project.icon}
                              alt={project.name}
                              className="sidebar-icon"
                            />
                          )}
                          <span
                            className="dropdown-label"
                            style={{
                              display: "flex",
                              alignItems: "center",
                              width: "100%",
                            }}
                          >
                            <span
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              {project.name}
                              {project.hasSubmenu && (
                                <>
                                  <span
                                    className="dropdown-icon"
                                    style={{
                                      marginLeft: 4,
                                      marginRight: 0,
                                      display: "inline-flex",
                                      alignItems: "center",
                                    }}
                                  >
                                    <img
                                      src={
                                        project.isOpen
                                          ? DropdownIcon
                                          : ArrowIcon
                                      }
                                      alt="dropdown"
                                      style={{
                                        margin: 0,
                                        padding: 0,
                                        width: 16,
                                        height: 16,
                                      }}
                                    />
                                  </span>
                                </>
                              )}
                            </span>
                            {userRole !== "staff" && (
                              <img
                                src={DetailIcon}
                                alt="detail"
                                className="detail-icon"
                                style={{
                                  marginLeft: "auto",
                                  marginRight: 4,
                                  width: 16,
                                  height: 16,
                                  opacity: 0,
                                  transition: "opacity 0.2s ease",
                                  cursor: "pointer",
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const rect = e.target.getBoundingClientRect();
                                  setShowDetailPopup({
                                    projectName: project.name,
                                    projectId: project._id || project.id,
                                    position: {
                                      top: rect.bottom + 5,
                                      left: rect.left - 150,
                                    },
                                    leaderId:
                                      project.leaderId || project.leader?._id,
                                    departmentId:
                                      project.departmentId ||
                                      project.department?._id,
                                  });
                                }}
                              />
                            )}
                          </span>
                        </div>
                        {project.hasSubmenu &&
                          project.isOpen &&
                          project.children && (
                            <ul className="submenu">
                              {project.children.map((child) => {
                                if (child.type === "dropdown") {
                                  // Render dropdown submenu (như Đội ngũ)
                                  return (
                                    <li key={child.id} className="dropdown">
                                      <div
                                        className="menu-item"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setProjects(
                                            projects.map((p) => {
                                              if (p.id === project.id) {
                                                return {
                                                  ...p,
                                                  children: p.children.map(
                                                    (c) =>
                                                      c.id === child.id
                                                        ? {
                                                            ...c,
                                                            isOpen: !c.isOpen,
                                                          }
                                                        : c
                                                  ),
                                                };
                                              }
                                              return p;
                                            })
                                          );
                                        }}
                                      >
                                        <img
                                          src={child.icon}
                                          alt={child.name}
                                          className="sidebar-icon"
                                        />
                                        <span className="dropdown-label">
                                          {child.name}
                                          <span
                                            className="dropdown-icon"
                                            style={{
                                              marginLeft: 4,
                                              marginRight: 0,
                                              display: "inline-flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <img
                                              src={
                                                child.isOpen
                                                  ? DropdownIcon
                                                  : ArrowIcon
                                              }
                                              alt="dropdown"
                                              style={{
                                                margin: 0,
                                                padding: 0,
                                                width: 16,
                                                height: 16,
                                              }}
                                            />
                                          </span>
                                        </span>
                                      </div>
                                      {child.isOpen && child.children && (
                                        <ul className="submenu">
                                          {child.children.map((grandChild) => (
                                            <li
                                              key={grandChild.id}
                                              className={
                                                isPathActive(grandChild.path)
                                                  ? "active"
                                                  : ""
                                              }
                                              style={
                                                isPathActive(grandChild.path)
                                                  ? {
                                                      paddingLeft: 6,
                                                      paddingRight: 6,
                                                      borderRadius: 6,
                                                    }
                                                  : {}
                                              }
                                            >
                                              <Link
                                                to={grandChild.path}
                                                onClick={() =>
                                                  handleMenuItemClick(
                                                    grandChild.path
                                                  )
                                                }
                                              >
                                                <img
                                                  src={grandChild.icon}
                                                  alt={grandChild.name}
                                                  className="sidebar-icon"
                                                />
                                                <span>{grandChild.name}</span>
                                              </Link>
                                            </li>
                                          ))}
                                        </ul>
                                      )}
                                    </li>
                                  );
                                } else {
                                  // Render simple link (như Tài liệu)
                                  return (
                                    <li
                                      key={child.id}
                                      className={
                                        isPathActive(child.path) ? "active" : ""
                                      }
                                      style={
                                        isPathActive(child.path)
                                          ? {
                                              paddingLeft: 6,
                                              paddingRight: 6,
                                              borderRadius: 6,
                                            }
                                          : {}
                                      }
                                    >
                                      <Link
                                        to={child.path}
                                        onClick={() =>
                                          handleMenuItemClick(child.path)
                                        }
                                      >
                                        <img
                                          src={child.icon}
                                          alt={child.name}
                                          className="sidebar-icon"
                                        />
                                        <span>{child.name}</span>
                                      </Link>
                                    </li>
                                  );
                                }
                              })}
                            </ul>
                          )}
                      </li>
                    ))}
                </ul>
              )}
            </li>

            {/* Báo cáo & thống kê - admin, CEO, Leader, departmentHead */}
            {hasMenuAccess("statistical") && (
              <li className="dropdown">
                <div
                  className="menu-item"
                  onClick={() => setIsStatisticalOpen(!isStatisticalOpen)}
                >
                  <img
                    src={StatisticalIcon}
                    alt="Báo cáo & thống kê"
                    className="sidebar-icon"
                  />
                  <span>Báo cáo & thống kê</span>
                  <span className="dropdown-icon">
                    <img
                      src={isStatisticalOpen ? DropdownIcon : ArrowIcon}
                      alt="dropdown"
                    />
                  </span>
                </div>
                {isStatisticalOpen && (
                  <ul className="submenu">
                    <li
                      className={
                        isPathActive("/statistical/reports") ? "active" : ""
                      }
                      style={
                        isPathActive("/statistical/reports")
                          ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                          : {}
                      }
                    >
                      <Link
                        to="/statistical/reports"
                        onClick={() => handleMenuItemClick("/statistical/reports")}
                      >
                        <img
                          src={StatisticalIcon}
                          alt="Báo cáo công việc"
                          className="sidebar-icon"
                        />
                        <span>Báo cáo công việc</span>
                      </Link>
                    </li>
                    <li
                      className={
                        isPathActive("/statistical/list") ? "active" : ""
                      }
                      style={
                        isPathActive("/statistical/list")
                          ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                          : {}
                      }
                    >
                      <Link
                        to="/statistical/list"
                        onClick={() => handleMenuItemClick("/statistical/list")}
                      >
                        <img
                          src={StatisticalReportIcon}
                          alt="Thống kê công việc"
                          className="sidebar-icon"
                        />
                        <span>Thống kê công việc</span>
                      </Link>
                    </li>
                  </ul>
                )}
              </li>
            )}

            {/* Quản lý phòng ban - admin, CEO, HR, departmentHead */}
            {['admin','ceo','hr','departmenthead'].includes(userRole) && (
              <li
                className={isPathActive("/departments") ? "active" : ""}
                style={{
                  padding: 0,
                  margin: 0,
                  ...(isPathActive("/departments")
                    ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                    : {}),
                }}
              >
                <Link
                  to="/departments"
                  className="sidebar-home-link"
                  onClick={() => handleMenuItemClick("/departments")}
                >
                  <img
                    src={DepartmentIcon}
                    alt="Quản lý phòng ban"
                    className="sidebar-icon"
                    style={{ opacity: 0.7 }}
                  />
                  <span>Quản lý phòng ban</span>
                </Link>
              </li>
            )}

            {/* Quản lý nhân sự - admin, CEO, HR, departmentHead */}
            {hasMenuAccess("hr") && (
              <li
                className={isPathActive("/hr") ? "active" : ""}
                style={{
                  padding: 0,
                  margin: 0,
                  ...(isPathActive("/hr")
                    ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                    : {}),
                }}
              >
                <Link
                  to="/hr"
                  className="sidebar-home-link"
                  onClick={() => handleMenuItemClick("/hr")}
                >
                  <img
                    src={UsersIcon}
                    alt="Quản lý nhân sự"
                    className="sidebar-icon"
                  />
                  <span>Quản lý nhân sự</span>
                </Link>
              </li>
            )}

            {/* Nhật ký hoạt động - chỉ admin */}
            {userRole === "admin" && (
              <li
                className={isPathActive("/activity-log") ? "active" : ""}
                style={{
                  padding: 0,
                  margin: 0,
                  ...(isPathActive("/activity-log")
                    ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                    : {}),
                }}
              >
                <Link
                  to="/activity-log"
                  className="sidebar-home-link"
                  onClick={() => handleMenuItemClick("/activity-log")}
                >
                  <img
                    src={ActivityLog}
                    alt="Nhật ký hoạt động"
                    className="sidebar-icon"
                  />
                  <span>Nhật ký hoạt động</span>
                </Link>
              </li>
            )}

            {/* Bảo trì hệ thống - chỉ admin */}
            {userRole === "admin" && (
              <li
                className={isPathActive("/system-maintenance") ? "active" : ""}
                style={{
                  padding: 0,
                  margin: 0,
                  ...(isPathActive("/system-maintenance")
                    ? { paddingLeft: 6, paddingRight: 6, borderRadius: 6 }
                    : {}),
                }}
              >
                <Link
                  to="/system-maintenance"
                  className="sidebar-home-link"
                  onClick={() => handleMenuItemClick("/system-maintenance")}
                >
                  <img
                    src={MaintenanceIcon}
                    alt="Bảo trì hệ thống"
                    className="sidebar-icon"
                  />
                  <span>Bảo trì hệ thống</span>
                </Link>
              </li>
            )}
          </ul>
        </nav>

        <div className="dashboard-footer">
          {userRole !== "staff" && (
            <Link
              to="/trash"
              className={isPathActive("/trash") ? "active-link" : ""}
              style={
                isPathActive("/trash")
                  ? {
                      paddingLeft: 6,
                      paddingRight: 6,
                      borderRadius: 6,
                      display: "flex",
                      alignItems: "center",
                    }
                  : { display: "flex", alignItems: "center" }
              }
              onClick={() => handleMenuItemClick("/trash")}
            >
              <img src={TrashIcon} alt="Trash" className="sidebar-icon" />
              <span>Thùng rác</span>
            </Link>
          )}
        </div>
      </aside>
    </>
  );
};

export default DashboardSidebar;
