.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.2s ease-out;
}

.confirm-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  min-width: 400px;
  max-width: 500px;
  animation: slideIn 0.3s ease-out;
}

.confirm-dialog-header {
  display: flex;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
}

.confirm-dialog-icon {
  font-size: 24px;
  margin-right: 12px;
}

.confirm-dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.confirm-dialog-body {
  padding: 16px 24px;
}

.confirm-dialog-message {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.confirm-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.btn-cancel {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.btn-cancel:hover {
  background: #e2e6ea;
  color: #5a6268;
}

.btn-confirm {
  color: white;
}

.btn-confirm.warning {
  background: #ffc107;
  color: #212529;
}

.btn-confirm.warning:hover {
  background: #e0a800;
}

.btn-confirm.danger {
  background: #dc3545;
}

.btn-confirm.danger:hover {
  background: #c82333;
}

.btn-confirm.success {
  background: #28a745;
}

.btn-confirm.success:hover {
  background: #218838;
}

.btn-confirm.info {
  background: #17a2b8;
}

.btn-confirm.info:hover {
  background: #138496;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive */
@media (max-width: 480px) {
  .confirm-dialog {
    min-width: 320px;
    margin: 20px;
  }
  
  .confirm-dialog-footer {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}