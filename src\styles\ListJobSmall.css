.task-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 0;
  max-width: 100%;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  border: 1px solid #e5e7eb;
}

/* Header Section */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-category {
  font-weight: 700;
  font-size: 16px;
  color: #333;
}

.info-icon {
  width: 16px;
  height: 16px;
  background: #6b7280;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.task-status {
  color: #6b7280;
  font-size: 14px;
}

.start-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.start-button:hover {
  background: #2563eb;
}

.play-icon {
  width: 14px;
  height: 14px;
}

/* Task Description */
.task-description {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.4;
  font-weight: 500;
}

/* Middle Section */
.task-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.assignee-section {
  flex: 1;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 12px;
  margin-bottom: 8px;
}

.person-icon,
.clock-icon,
.clock-plus-icon {
  width: 14px;
  height: 14px;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-avatar {
  width: 32px;
  height: 32px;
  background: #f97316;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.assignee-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.time-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.time-labels-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.time-label-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.time-item {
  text-align: right;
}

.time-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* Bottom Section */
.task-dependencies {
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.dependency-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.dependency-content {
  /* display: flex; */
  align-items: center;
  gap: 12px;
}

.dependency-label {
  font-size: 12px;
  color: #6b7280;
}

.dependency-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
} 