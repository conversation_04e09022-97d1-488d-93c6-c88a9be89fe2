// Thông báo lỗi cụ thể
export const errorMessages = {
  email: {
    required: '<PERSON>ail không được để trống',
    invalid: '<PERSON>ail không hợp lệ',
  },
  password: {
    required: 'M<PERSON>t khẩu không được để trống',
    minLength: 'Mật khẩu phải có ít nhất 6 ký tự',
  },
  confirmPassword: {
    required: 'Vui lòng xác nhận mật khẩu',
    notMatch: 'Mật khẩu xác nhận không khớp',
  },
  currentPassword: {
    required: 'Mật khẩu hiện tại không được để trống',
  },
  newPassword: {
    required: 'Mật khẩu mới không được để trống',
    minLength: 'Mật khẩu mới phải có ít nhất 6 ký tự',
  },
  firstName: {
    required: 'Tên không được để trống',
    minLength: 'Tên phải có ít nhất 1 ký tự',
  },
  lastName: {
    required: '<PERSON><PERSON> không được để trống',
    minLength: 'Họ phải có ít nhất 1 ký tự',
  },
  noteTitle: {
    required: 'Tiêu đề không được để trống',
    maxLength: 'Tiêu đề không được vượt quá 100 ký tự',
  },
  noteContent: {
    required: 'Nội dung không được để trống',
    maxLength: 'Nội dung không được vượt quá 1000 ký tự',
  },
  required: (fieldName) => `${fieldName} không được để trống`,
  fullName: {
    required: 'Họ và tên không được để trống',
    minLength: 'Họ và tên phải có ít nhất 2 ký tự',
    maxLength: 'Họ và tên không được vượt quá 50 ký tự',
    invalid: 'Họ và tên chỉ được chứa chữ cái và khoảng trắng',
  },
  department: {
    required: 'Vui lòng chọn phòng ban',
  },
  position: {
    required: 'Vị trí không được để trống',
    minLength: 'Vị trí phải có ít nhất 2 ký tự',
    maxLength: 'Vị trí không được vượt quá 50 ký tự',
  },
  role: {
    required: 'Vui lòng chọn chức vụ và phân quyền',
  },
};

// Kiểm tra email hợp lệ
export function validateEmail(email) {
  if (!email) return errorMessages.email.required;
  const trimmed = email.trim();
  // Chỉ cho phép ký tự chữ, số, ., _, -
  if (/[^a-zA-Z0-9@._-]/.test(trimmed)) return 'Email chỉ gồm chữ, số, dấu chấm, gạch dưới, gạch ngang.';
  const re = /^[\w.-]+@[\w-]+\.[a-zA-Z]{2,}$/;
  if (!re.test(trimmed)) return errorMessages.email.invalid;
  return '';
}

// Kiểm tra password hợp lệ
export function validatePassword(password) {
  if (!password) return errorMessages.password.required;
  const trimmed = password.trim();
  // Chỉ cho phép ký tự chữ, số và các ký tự đặc biệt !@#$%&*_.+=-
  if (/[^a-zA-Z0-9!@#$%&*_.+=-]/.test(trimmed)) return 'Mật khẩu chỉ được chứa ký tự chữ, số và các ký tự !@#$%&*_.+=-';
  if (trimmed.length < 6) return errorMessages.password.minLength;
  return '';
}

// Kiểm tra xác nhận mật khẩu
export function validateConfirmPassword(password, confirmPassword) {
  if (!confirmPassword) return errorMessages.confirmPassword.required;
  const trimmed = confirmPassword.trim();
  if (password !== trimmed) return errorMessages.confirmPassword.notMatch;
  return '';
}

// Kiểm tra mật khẩu hiện tại
export function validateCurrentPassword(currentPassword) {
  if (!currentPassword) return errorMessages.currentPassword.required;
  return '';
}

// Kiểm tra mật khẩu mới
export function validateNewPassword(newPassword) {
  if (!newPassword) return errorMessages.newPassword.required;
  const trimmed = newPassword.trim();
  // Chỉ cho phép ký tự chữ, số và các ký tự đặc biệt !@#$%&*_.+=-
  if (/[^a-zA-Z0-9!@#$%&*_.+=-]/.test(trimmed)) return 'Mật khẩu mới chỉ được chứa ký tự chữ, số và các ký tự !@#$%&*_.+=-';
  if (trimmed.length < 6) return errorMessages.newPassword.minLength;
  return '';
}

// Kiểm tra họ, tên hợp lệ
export function validateName(name, field) {
  if (!name || !name.trim()) return errorMessages[field].required;
  const trimmed = name.trim();
  if (trimmed.length < 1) return errorMessages[field].minLength;
  if (/\s{2,}/.test(trimmed)) return `${field === 'firstName' ? 'Tên' : 'Họ'} không khoảng trắng liên tiếp`;
  if (/[0-9]/.test(trimmed)) return `${field === 'firstName' ? 'Tên' : 'Họ'} không được chứa số`;
  if (/[^a-zA-ZÀ-ỹà-ỹ\s]/.test(trimmed)) return `${field === 'firstName' ? 'Tên' : 'Họ'} không được chứa ký tự đặc biệt`;
  return '';
}

// Kiểm tra tiêu đề ghi chú
export function validateNoteTitle(title) {
  if (!title || !title.trim()) return errorMessages.noteTitle.required;
  const trimmed = title.trim();
  if (trimmed.length > 100) return errorMessages.noteTitle.maxLength;
  return '';
}

// Kiểm tra nội dung ghi chú
export function validateNoteContent(content) {
  if (!content || !content.trim()) return errorMessages.noteContent.required;
  const trimmed = content.trim();
  if (trimmed.length > 1000) return errorMessages.noteContent.maxLength;
  return '';
}

// Kiểm tra form ghi chú
export function validateNoteForm({ title, content }) {
  return {
    title: validateNoteTitle(title),
    content: validateNoteContent(content),
  };
}

// Kiểm tra trường bắt buộc
export function validateRequired(value, fieldName) {
  if (!value) return errorMessages.required(fieldName);
  return '';
}

// Đăng nhập
export function validateLoginForm({ email, password }) {
  return {
    email: validateEmail(email),
    password: validatePassword(password),
  };
}

// Đăng ký
export function validateRegisterForm({ email, password, confirmPassword, firstName, lastName }) {
  return {
    email: validateEmail(email),
    password: validatePassword(password),
    confirmPassword: validateConfirmPassword(password, confirmPassword),
    firstName: validateName(firstName, 'firstName'),
    lastName: validateName(lastName, 'lastName'),
  };
}

// Quên mật khẩu
export function validateForgotPasswordForm({ email }) {
  return {
    email: validateEmail(email),
  };
}

// Đặt lại mật khẩu
export function validateResetPasswordForm({ password, confirmPassword }) {
  return {
    password: validatePassword(password),
    confirmPassword: validateConfirmPassword(password, confirmPassword),
  };
}

// Đổi mật khẩu
export function validateChangePasswordForm({ currentPassword, newPassword, confirmPassword }) {
  return {
    currentPassword: validateCurrentPassword(currentPassword),
    newPassword: validateNewPassword(newPassword),
    confirmPassword: validateConfirmPassword(newPassword, confirmPassword),
  };
}

// Kiểm tra họ và tên đầy đủ
export function validateFullName(fullName) {
  if (!fullName || !fullName.trim()) return errorMessages.fullName.required;
  const trimmed = fullName.trim();
  if (trimmed.length < 2) return errorMessages.fullName.minLength;
  if (trimmed.length > 50) return errorMessages.fullName.maxLength;
  // Chỉ cho phép chữ cái, khoảng trắng và các ký tự tiếng Việt
  if (!/^[a-zA-ZÀ-ỹà-ỹ\s]+$/.test(trimmed)) return errorMessages.fullName.invalid;
  // Không cho phép khoảng trắng liên tiếp
  if (/\s{2,}/.test(trimmed)) return 'Họ và tên không được có khoảng trắng liên tiếp';
  return '';
}

// Kiểm tra phòng ban
export function validateDepartment(department) {
  if (!department || !department.trim()) return errorMessages.department.required;
  return '';
}

// Kiểm tra vị trí
export function validatePosition(position) {
  if (!position || !position.trim()) return errorMessages.position.required;
  const trimmed = position.trim();
  if (trimmed.length < 2) return errorMessages.position.minLength;
  if (trimmed.length > 50) return errorMessages.position.maxLength;
  return '';
}

// Kiểm tra chức vụ
export function validateRole(role) {
  if (!role || !role.trim()) return errorMessages.role.required;
  return '';
}

// Kiểm tra form tạo nhân viên
export function validateUserCreateForm({ name, email, department, position, role }) {
  return {
    name: validateFullName(name),
    email: validateEmail(email),
    department: validateDepartment(department),
    position: validatePosition(position),
    role: validateRole(role),
  };
}

// Kiểm tra form cập nhật nhân viên
export function validateUserUpdateForm({ name, email, department, position, role }) {
  return {
    name: validateFullName(name),
    email: validateEmail(email),
    department: validateDepartment(department),
    position: validatePosition(position),
    role: validateRole(role),
  };
}