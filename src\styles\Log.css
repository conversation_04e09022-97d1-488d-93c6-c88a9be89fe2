@import url('../index.css');

/* Header và Filter section gộp chung */
.log-activity-log-header-filters {
  background: #fff;
  border-radius: 12px;
  padding: 20px 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

/* Header section */
.log-activity-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.log-activity-log-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-activity-log-title h2 {
  font-size: 22px;
  font-weight: 600;
  color: #5d5d5d;
  margin: 0;
}

.log-activity-count {
  font-size: 14px;
  color: #5D5D5D;
  font-weight: 400;
}

.log-refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;
}

.log-refresh-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.log-refresh-btn img {
  opacity: 0.7;
}

.log-refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.log-refresh-btn:disabled:hover {
  background: #fff;
  border-color: #e0e0e0;
}

.log-filter-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  align-items: end;
}

.log-filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.log-filter-group select{
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  color: #333;
  outline: none;
  transition: border-color 0.2s;
}

/* Search input styling */
.log-search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.log-search-input {
  padding: 10px 40px 10px 12px !important;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  color: #333;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.log-search-icon {
  position: absolute;
  right: 12px;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  pointer-events: none;
}

/* Date input wrapper styling */
.log-date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}



.log-date-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  pointer-events: none;
  z-index: 1;
}

.log-date-input-field {
  width: 100%;
  padding: 10px 12px 10px 36px !important;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  color: #333;
  outline: none;
  transition: border-color 0.2s;
  cursor: pointer;
}

.log-date-input-field:focus {
  border-color: #2196f3;
}

.log-date-input-field:hover {
  border-color: #ccc;
}

/* Ẩn icon datepicker mặc định nhưng mở rộng click area */
.log-date-input-field::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}



/* Custom dropdown styling for action filter */
.log-activity-log-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

.log-activity-log-dropdown-btn {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: #fff;
  cursor: pointer;
  text-align: left;
  font-weight: 400;
  color: #333;
  box-shadow: none;
  outline: none;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.log-activity-log-dropdown-btn:hover {
  border-color: #ccc;
}

.log-activity-log-dropdown-btn:focus {
  border-color: #2196f3;
}

.log-activity-log-dropdown-btn .log-dropdown-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
  pointer-events: none;
  flex-shrink: 0;
  transition: transform 0.2s;
}

.log-activity-log-dropdown.open .log-dropdown-icon {
  transform: rotate(180deg);
}

.log-activity-log-dropdown-menu {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  min-width: 180px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
  z-index: 1000;
  padding: 8px 0;
  margin-top: 4px;
  border: 1px solid #e0e0e0;
  max-height: 200px;
  overflow-y: auto;
}

.log-activity-log-dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.log-activity-log-dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.log-activity-log-dropdown-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.log-activity-log-dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.log-activity-log-dropdown-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  background: #fff;
  transition: background-color 0.15s;
  white-space: nowrap;
}

.log-activity-log-dropdown-item:hover {
  background: #f5f7fa;
}

.log-activity-log-dropdown-item:first-child {
  font-weight: 500;
  color: #666;
}

/* Activity log list */
.log-activity-log-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* New activity log item design */
.log-activity-log-item-new {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.log-log-avatar {
  flex-shrink: 0;
}

.log-log-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.log-log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.log-log-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.log-log-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.log-user-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.log-status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: lowercase;
}

.log-status-deleted {
  background: #ffebee;
  color: #c62828;
}

.log-status-created {
  background: #e8f5e8;
  color: #2e7d32;
}

.log-status-updated {
  background: #fff3e0;
  color: #ef6c00;
}

.log-status-completed {
  background: #e3f2fd;
  color: #1565c0;
}

.log-status-member-added {
  background: #f3e5f5;
  color: #7b1fa2;
}

.log-status-info-updated {
  background: #e0f2f1;
  color: #00695c;
}

.log-status-default {
  background: #f0f0f0;
  color: #666;
}

.log-status-login {
  background: #e3f2fd;
  color: #1976d2;
}

.log-status-logout {
  background: #fff3e0;
  color: #f57c00;
}

.log-status-restored {
  background: #e8f5e8;
  color: #388e3c;
}

.log-status-blocked {
  background: #ffebee;
  color: #d32f2f;
}

.log-status-unblocked {
  background: #e8f5e8;
  color: #388e3c;
}

.log-status-role-changed {
  background: #f3e5f5;
  color: #7b1fa2;
}

.log-status-task-assigned {
  background: #e1f5fe;
  color: #0277bd;
}

.log-status-task-completed {
  background: #e8f5e8;
  color: #388e3c;
}

.log-status-notification-read {
  background: #e8f5e8;
  color: #2e7d32;
}

.log-status-notification-unread {
  background: #fff3e0;
  color: #ef6c00;
}

.log-log-date {
  color: #666;
  font-size: 14px;
}

.log-log-actions {
  flex-shrink: 0;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.log-activity-log-restore-btn {
  background: #2196f3;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.log-activity-log-restore-btn:hover {
  background: #1976d2;
}

.log-log-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px 24px;
}

.log-log-detail-row {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.log-detail-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.log-detail-value {
  color: #666;
}

.log-log-description {
  display: flex;
  gap: 8px;
  font-size: 14px;
}

.log-log-description .log-detail-label {
  font-weight: 600;
  color: #333;
}

.log-log-description .log-detail-value {
  color: #666;
  flex: 1;
}

/* Loading, Error and No Data States */
.log-loading-container,
.log-no-data-container,
.log-error-container,
.log-empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.log-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes log-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.log-loading-container p,
.log-no-data-container p,
.log-error-container p,
.log-empty-container p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

.log-error-message {
  color: #d32f2f;
  font-weight: 500;
}

.log-retry-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #2196f3;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.log-retry-btn:hover {
  background: #1976d2;
}

/* Pagination styles */
.log-pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

.log-pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.log-pagination-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #ccc;
}

.log-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9f9f9;
}

.log-pagination-info {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 1200px) {
  .log-filter-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 800px) {
  .log-filter-row {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .log-filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .log-log-details {
    grid-template-columns: 1fr;
  }

  .log-activity-log-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .log-log-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .log-log-user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  /* Custom dropdown responsive */
  .log-activity-log-dropdown-menu {
    min-width: 100%;
    max-height: 150px;
  }

  .log-activity-log-dropdown-item {
    padding: 12px 16px;
    font-size: 15px;
  }

  /* Date input responsive */
  .log-date-input-field {
    padding: 12px 12px 12px 36px;
    font-size: 15px;
  }

  .log-date-icon {
    width: 18px;
    height: 18px;
  }
}