import React from "react";
import DeploymentIcon from "../../../assets/deployment.svg";
import "../../../styles/SearchAll.css";

const avatarList = [
  "https://randomuser.me/api/portraits/women/1.jpg",
  "https://randomuser.me/api/portraits/men/2.jpg",
  "https://randomuser.me/api/portraits/men/3.jpg",
];

const All = () => {
  return (
    <div className="searchall-container">
      {/* Dự án */}
      <div className="searchall-block">
        <div className="searchall-row">
          <div className="searchall-col2">
            <div className="searchall-label"><PERSON><PERSON> thống quản lí dự án</div>
            <div className="searchall-sublabel">Mã dự án: SD-01</div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle"><PERSON><PERSON><PERSON><PERSON> gian</div>
            <div className="searchall-cellvalue">20/7/2025 - 25/7/2025</div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle">Thành viên</div>
            <div>
              {avatarList.map((src, idx) => (
                <img
                  key={idx}
                  src={src}
                  alt="avatar"
                  className="searchall-avatar"
                  style={{ marginLeft: idx === 0 ? 0 : -8 }}
                />
              ))}
            </div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle">Trạng thái</div>
            <div className="searchall-status">
              <img
                src={DeploymentIcon}
                alt="Đang tiến hành"
                className="searchall-status-icon"
              />
              Đang tiến hành
            </div>
          </div>
          <div className="searchall-col1 searchall-flex-end">
            <span className="badge badge-blue">Dự án</span>
          </div>
        </div>
      </div>

      {/* Công việc */}
      <div className="searchall-block">
        <div className="searchall-row">
          <div className="searchall-col2">
            <div className="searchall-label">Hệ thống quản lí bán hàng</div>
            <div className="searchall-sublabel">Mã dự án: SD-01</div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle">Thiết kế giao diện người dùng</div>
            <div className="searchall-cellvalue">Mã công việc: TSK-01</div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle">Thành viên</div>
            <div>
              {avatarList.map((src, idx) => (
                <img
                  key={idx}
                  src={src}
                  alt="avatar"
                  className="searchall-avatar"
                  style={{ marginLeft: idx === 0 ? 0 : -8 }}
                />
              ))}
            </div>
          </div>
          <div className="searchall-col2">
            <div className="searchall-celltitle">Trạng thái</div>
            <div className="searchall-status">
              <img
                src={DeploymentIcon}
                alt="Đang tiến hành"
                className="searchall-status-icon"
              />
              Đang tiến hành
            </div>
          </div>
          <div className="searchall-col1 searchall-flex-end">
            <span className="badge badge-green">Công việc</span>
          </div>
        </div>
      </div>

      {/* Thành viên */}
      <div className="searchall-member">
        {/* Avatar và thông tin */}
        <div className="searchall-member-info">
          <img
            src="https://randomuser.me/api/portraits/women/1.jpg"
            alt="avatar"
            className="searchall-member-avatar"
          />
          <div>
            <div className="searchall-member-name">Nguyễn Văn An</div>
            <div className="searchall-member-email"><EMAIL></div>
            <div className="searchall-member-id">NA01000</div>
          </div>
        </div>
        {/* Các thuộc tính còn lại dàn đều */}
        <div className="searchall-member-attrs">
          <span className="badge badge-blue">IT</span>
          <span className="searchall-member-role">Trưởng phòng</span>
          <span className="badge badge-status">Hoạt động</span>
          <span className="searchall-member-date">20/1/2025</span>
          <span className="badge badge-blue">Thành viên</span>
        </div>
      </div>

      {/* Ghi chú dự án */}
      <div className="searchall-note">
        <span className="searchall-note-icon">
          <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.828a2 2 0 0 0-.586-1.414l-4.828-4.828A2 2 0 0 0 11.172 1H4zm0 2h7v4a2 2 0 0 0 2 2h4v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4zm9 0.414L17.586 7H13a1 1 0 0 1-1-1V2.414z"></path>
          </svg>
        </span>
        <div className="searchall-note-content">
          <div className="searchall-note-title">Dự án SDTC</div>
          <div className="searchall-note-desc">
            <span className="searchall-note-desc-label">Mô tả:</span> Thiết kế giao diện người dùng
          </div>
        </div>
        <span className="badge badge-orange">Ghi chú</span>
      </div>
    </div>
  );
};

export default All;