@import url('../index.css');

.reset-container {
  display: flex;
  height: 100vh;
  align-items: stretch;
  justify-content: center;
  background: #f7f8fa;
}

.reset-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: none;
  border-right: none;
  min-width: 0;
}

.reset-illustration {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  margin: 0 auto;
}

.reset-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  height: 100vh;
  min-width: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12), 0 1.5px 6px rgba(0,0,0,0.08);
  position: relative;
}

.reset-form-wrapper {
  padding: 48px 36px 36px 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 420px;
  border-radius: 16px;
  text-align: center;
  margin: 0 auto;
}

.reset-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.reset-logo-img {
  width: 48px;
  margin-bottom: 8px;
}

.reset-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #22213a;
  margin-bottom: 8px;
  margin-top: 0;
  letter-spacing: 0.01em;
}

.reset-desc {
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 28px;
}

.reset-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
}

.reset-label {
  text-align: left;
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 2px;
  font-weight: 500;
}

.reset-input {
  width: 420px;
  box-sizing: border-box;
  padding: 12px 14px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 8px;
  background: #faf7fa;
  color: #3d2c3c;
  transition: border-color 0.2s;
}

.reset-input:focus {
  outline: none;
  border-color: #a83279;
}

.reset-password-wrapper {
  position: relative;
  width: 100%;
}

.reset-password-wrapper .reset-input {
  padding-right: 45px;
}

.reset-password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #7a6e7a;
  padding: 0;
  margin-top: -4px;
}

.reset-password-toggle:hover {
  color: #a83279;
}

.reset-eye-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.reset-password-toggle:hover .reset-eye-icon {
  opacity: 1;
}

.reset-btn {
  width: 100%;
  padding: 13px 0;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 10px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
}

.reset-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.reset-back-row {
  margin-top: 18px;
  color: #7a6e7a;
  font-size: 1rem;
  position: absolute;
  top: 0;
  left: 0;
  margin-top: 0;
  margin-left: 0;
  padding: 12px 0 0 16px;
  z-index: 10;
}

.reset-back-link {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  margin-left: 4px;
}

.reset-back-link:hover {
  text-decoration: underline;
}

.reset-input-error {
  border: 2px solid #e53935 !important;
  box-shadow: none;
  background: #fdf6f6;
  position: relative;
  z-index: 2;
}

.reset-input-validation-wrapper {
  position: relative;
  width: 100%;
}

.reset-validation-error-message {
  color: #e53935;
  font-size: 0.75rem;
  position: absolute;
  left: 0;
  top: 100%;
  background: #fff;
  padding: 0 6px;
  z-index: 10;
  margin-top: -10px;
  margin-left: 10px;
  line-height: 0.2;
  pointer-events: none;
}

.reset-success {
  color: #4caf50;
  font-size: 0.9rem;
  margin-bottom: 10px;
  padding: 8px;
  background: #f1f8e9;
  border-radius: 4px;
  border: 1px solid #c8e6c9;
}

@media (max-width: 1200px) {
  .reset-left {
    max-width: 45vw;
  }
}

@media (max-width: 900px) {
  .reset-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .reset-left, .reset-right {
    flex: unset;
    width: 100%;
    min-height: 320px;
    max-width: 100vw;
    height: auto;
  }
  .reset-form-wrapper {
    margin: 32px auto;
    max-width: 95vw;
  }
  .reset-illustration {
    width: 100%;
    height: auto;
    margin: 0 auto;
  }
}

@media (max-width: 600px) {
  .reset-form-wrapper {
    padding: 8px;
    max-width: 100vw;
  }
  .reset-right {
    padding: 16px 4px;
    min-width: unset;
    max-width: 100vw;
  }
  .reset-input {
    width: 288px;
    height: 44px;
    min-width: 0;
    font-size: 0.98rem;
    padding: 10px 10px;
  }
  .reset-password-wrapper .reset-input {
    padding-right: 40px;
  }
  .reset-btn {
    font-size: 1rem;
    padding: 11px 0;
  }
  .reset-title {
    font-size: 1.2rem;
  }
  .reset-desc {
    font-size: 0.95rem;
  }
}

@media (max-width: 400px) {
  .reset-form-wrapper {
    padding: 2px;
  }
  .reset-title {
    font-size: 1rem;
  }
  .reset-btn {
    font-size: 0.95rem;
    padding: 9px 0;
  }
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
