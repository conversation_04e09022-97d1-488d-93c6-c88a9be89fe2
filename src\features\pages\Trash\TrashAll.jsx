// Cache và preload để tăng tốc độ loading
let trashCache = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 10000; // 10 giây
let preloadPromise = null;

import ActionTrash from "../../components/TrashAction";
import TrashActionBar from "../../components/TrashActionBar";
import React, { useState, useEffect } from "react";
import "../../../styles/TrashList.css";
import "../../../styles/Trash.css";
import FolderIcon from "../../../assets/file-text.svg";
import SquareCheckIcon from "../../../assets/square-check-big.svg";

import { getDeletedUsers, restoreUser, deleteUser } from "../../../api/userManagement";
import { getDeletedProjects, restoreProject, permanentDeleteProject } from "../../../api/projectManagement";
import { getDeletedProjectTasks, restoreProjectTask, permanentDeleteProjectTask } from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import { showSuccess, showError } from "../../../utils/toastUtils";

const statusColor = (status) => {                                                                     
  if (status === "Hoàn thành") return { color: '#27ae60' };
  if (status === "Đang chờ") return { color: '#e67e22' };
  return {};
};

const getIconByType = (type) => {
  switch (type) {
    case "Dự án":
      return FolderIcon;
    case "Công việc":
      return SquareCheckIcon;
    case "Nhân sự":
      return SquareCheckIcon;
    default:
      return FolderIcon;
  }
};

const All = ({ onDataChange }) => {
  const [trashData, setTrashData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionIdx, setActionIdx] = useState(null);
  const [selected, setSelected] = useState([]);
  const [lastStats, setLastStats] = useState(null); // Để tránh gọi callback liên tục

  // Hàm gửi thống kê lên component cha
  const sendStatsToParent = (allTrash, userTrash, filteredProjectTrash, filteredTaskTrash = []) => {
    if (onDataChange) {
      const userCount = userTrash.length;
      const projectCount = filteredProjectTrash.length;
      const taskCount = filteredTaskTrash.length;
      const stats = {
        totalItems: allTrash.length,
        projectCount: projectCount,
        jobCount: taskCount,
        userCount: userCount,
        selectedCount: selected.length
      };
      
      // Chỉ gửi nếu thống kê thay đổi
      if (JSON.stringify(stats) !== JSON.stringify(lastStats)) {
        setLastStats(stats);
        onDataChange(stats);
      }
    }
  };

  // Preload function
  const preloadTrashData = async () => {
    if (preloadPromise) return preloadPromise;

    preloadPromise = (async () => {
      try {
        // Fetch từng API riêng lẻ để tránh một lỗi làm fail toàn bộ
        let users = [];
        let projects = [];
        let tasks = [];

        try {
          const usersResponse = await getDeletedUsers();
          users = usersResponse?.data || usersResponse || [];
        } catch (err) {
          console.warn('Error fetching deleted users:', err);
          users = [];
        }

        try {
          const projectsResponse = await getDeletedProjects();
          projects = projectsResponse?.data || projectsResponse || [];
        } catch (err) {
          console.warn('Error fetching deleted projects:', err);
          projects = [];
        }

        // Thêm fetch task đã xóa (dự án)
        try {
          // Lấy tất cả projectId
          let allProjectIds = [];
          try {
            const projectsRes = await getAllProjects();
            const allProjects = projectsRes.data || projectsRes || [];
            allProjectIds = allProjects.map(p => p._id || p.id);
          } catch (err) {
            allProjectIds = [];
          }
          for (const projectId of allProjectIds) {
            try {
              const tasksResponse = await getDeletedProjectTasks(projectId);
              const projectTasks = tasksResponse?.data || tasksResponse || [];
              // Gán projectId vào mỗi task để dùng cho việc khôi phục/xóa
              tasks = tasks.concat(projectTasks.map(t => ({ ...t, projectId })));
            } catch (err) {
              // Bỏ qua lỗi từng project
            }
          }
        } catch (err) {
          console.warn('Error fetching deleted tasks:', err);
          tasks = [];
        }

        const userTrash = (users || []).map(u => ({ ...u, type: "Nhân sự" }));
        const projectTrash = (projects || []).map(p => ({ ...p, type: "Dự án" }));
        const taskTrash = (tasks || []).map(t => ({ ...t, type: "Công việc" }));

        // Lọc theo quyền
        const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
        const role = userRaw.user?.role?.toLowerCase() || userRaw.role?.toLowerCase() || 'staff';
        const userId = userRaw.user?._id || userRaw._id || userRaw.user?.id || userRaw.id;
        const userDeptId = userRaw.user?.departmentId || userRaw.departmentId || userRaw.user?.department?._id || userRaw.department?._id || userRaw.user?.department || userRaw.department;

        // Lọc cho project
        let filteredProjectTrash = projectTrash;
        // Lọc task theo quyền
        let filteredTaskTrash = taskTrash;

        let allTrash = [...userTrash, ...filteredProjectTrash, ...filteredTaskTrash];

        trashCache = {
          allTrash,
          userTrash,
          filteredProjectTrash,
          filteredTaskTrash
        };
        cacheTimestamp = Date.now();

        return trashCache;
      } catch (err) {
        console.error("Error in preloadTrashData:", err);
        return {
          allTrash: [],
          userTrash: [],
          filteredProjectTrash: [],
          filteredTaskTrash: []
        };
      }
    })();

    return preloadPromise;
  };

  // Fetch tất cả loại thùng rác khi mount
  useEffect(() => {
    const fetchAllTrash = async () => {
      // Kiểm tra cache trước
      const now = Date.now();
      if (trashCache && (now - cacheTimestamp) < CACHE_DURATION) {
        setTrashData(trashCache.allTrash);
        sendStatsToParent(trashCache.allTrash, trashCache.userTrash, trashCache.filteredProjectTrash, trashCache.filteredTaskTrash);
        setLoading(false);
        return;
      }

      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);

      try {
        let cacheData;
        if (preloadPromise) {
          cacheData = await preloadPromise;
        } else {
          cacheData = await preloadTrashData();
        }

        setTrashData(cacheData.allTrash);
        sendStatsToParent(cacheData.allTrash, cacheData.userTrash, cacheData.filteredProjectTrash, cacheData.filteredTaskTrash);
      } catch (err) {
        console.error("Error in fetchAllTrash:", err);
        setError("Lỗi khi tải dữ liệu thùng rác");
      } finally {
        setLoading(false);
      }
    };
    fetchAllTrash();
  }, []); // Empty dependency array để chỉ chạy một lần

  // Start preloading when component mounts
  useEffect(() => {
    preloadTrashData();
  }, []);

  // Handler API cho từng loại
  const handleRestoreItem = async (item) => {
    try {
      if (item.type === "Nhân sự") {
        await restoreUser(item._id || item.id);
      } else if (item.type === "Dự án") {
        await restoreProject(item._id || item.id);
      } else if (item.type === "Công việc") {
        if (!item.projectId) throw new Error("Công việc thiếu ID dự án để khôi phục.");
        await restoreProjectTask(item.projectId, item._id || item.id);
      }
      setTrashData(prev => prev.filter(i => (i._id || i.id) !== (item._id || item.id)));
      showSuccess("Khôi phục thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục: " + (err.message || err));
    }
    setActionIdx(null);
  };
  const handleDeleteItem = async (item) => {
    try {
      if (item.type === "Nhân sự") {
        await deleteUser(item._id || item.id);
      } else if (item.type === "Dự án") {
        await permanentDeleteProject(item._id || item.id);
      } else if (item.type === "Công việc") {
        if (!item.projectId) throw new Error("Công việc thiếu ID dự án để xóa.");
        await permanentDeleteProjectTask(item.projectId, item._id || item.id);
      }
      setTrashData(prev => prev.filter(i => (i._id || i.id) !== (item._id || item.id)));
      showSuccess("Xóa vĩnh viễn thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn: " + (err.message || err));
    }
    setActionIdx(null);
  };
  const handleSelect = (id) => {
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = () => {
    if (selected.length === trashData.length) {
      setSelected([]);
    } else {
      setSelected(trashData.map((item) => item._id || item.id));
    }
  };
  const handleClearSelection = () => setSelected([]);

  // Xử lý xóa vĩnh viễn nhiều mục
  const handleDeleteSelected = async () => {
    const itemsToDelete = selected.map(id => trashData.find(i => (i._id || i.id) === id)).filter(Boolean);

    const promises = itemsToDelete.map(item => {
      if (item.type === "Nhân sự") return deleteUser(item._id || item.id);
      if (item.type === "Dự án") return permanentDeleteProject(item._id || item.id);
      if (item.type === "Công việc" && item.projectId) return permanentDeleteProjectTask(item.projectId, item._id || item.id);
      return Promise.reject(new Error(`Không thể xóa mục: ${item.title || item.name}`));
    });

    try {
      await Promise.all(promises);
      setTrashData(prev => prev.filter(item => !selected.includes(item._id || item.id)));
      setSelected([]);
      showSuccess("Xóa vĩnh viễn thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn hàng loạt: " + (err.message || err));
    }
  };
  // Xử lý khôi phục nhiều mục
  const handleRestoreSelected = async () => {
    const itemsToRestore = selected.map(id => trashData.find(i => (i._id || i.id) === id)).filter(Boolean);

    const promises = itemsToRestore.map(item => {
      if (item.type === "Nhân sự") return restoreUser(item._id || item.id);
      if (item.type === "Dự án") return restoreProject(item._id || item.id);
      if (item.type === "Công việc" && item.projectId) return restoreProjectTask(item.projectId, item._id || item.id);
      return Promise.reject(new Error(`Không thể khôi phục mục: ${item.title || item.name}`));
    });

    try {
      await Promise.all(promises);
      setTrashData(prev => prev.filter(item => !selected.includes(item._id || item.id)));
      setSelected([]);
      showSuccess("Khôi phục hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục hàng loạt: " + (err.message || err));
    }
  };

  // Skeleton loading component
  const TrashSkeleton = () => (
    <div className="trash-skeleton-container">
      {[1, 2, 3, 4, 5, 6].map((idx) => (
        <div key={idx} className="trash-skeleton-item">
          <div className="trash-skeleton-checkbox"></div>
          <div className="trash-skeleton-main">
            <div className="trash-skeleton-title-row">
              <div className="trash-skeleton-icon"></div>
              <div className="trash-skeleton-title"></div>
              <div className="trash-skeleton-type"></div>
            </div>
            <div className="trash-skeleton-info">
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
            </div>
            <div className="trash-skeleton-members">
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
            </div>
          </div>
          <div className="trash-skeleton-actions">
            <div className="trash-skeleton-action-type"></div>
            <div className="trash-skeleton-more-btn"></div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading && trashData.length === 0) {
    return (
      <div className="trash-list-container">
        <div className="trash-list-header">
          <div className="trash-list-header-left">
            <input type="checkbox" disabled style={{ opacity: 0.5 }} />
            <span style={{ color: '#666' }}>Đang tải dữ liệu...</span>
          </div>
          <div className="trash-list-header-right" style={{ color: '#666' }}>Đang tải...</div>
        </div>
        <TrashSkeleton />
      </div>
    );
  }

  if (error) return (
    <div style={{
      color: "red",
      textAlign: "center",
      padding: "40px",
      background: "#fff",
      borderRadius: "8px",
      margin: "20px 0"
    }}>
      <div>❌ {error}</div>
      <button
        onClick={() => window.location.reload()}
        style={{
          marginTop: '10px',
          padding: '8px 16px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Thử lại
      </button>
    </div>
  );

  return (
    <div className="trash-list-container">
      {selected.length > 0 && (
        <TrashActionBar
          selectedCount={selected.length}
          onRestore={handleRestoreSelected}
          onDelete={handleDeleteSelected}
          onClearSelection={handleClearSelection}
        />
      )}
      <div className="trash-list-header">
        <div className="trash-list-header-left">
          <input
            type="checkbox"
            checked={selected.length === trashData.length && trashData.length > 0}
            onChange={handleSelectAll}
          />
          <span>Chọn tất cả ({trashData.length} mục)</span>
        </div>
        <div className="trash-list-header-right">Hiển thị {trashData.length}/{trashData.length} mục</div>
      </div>
      <div className="trash-list-content">
        {trashData.map((item, idx) => (
        <div className="trash-list-item" key={item._id || item.id}>
          <input
            type="checkbox"
            className="trash-list-checkbox"
            checked={selected.includes(item._id || item.id)}
            onChange={() => handleSelect(item._id || item.id)}
          />
          <div className="trash-list-main">
            <div className="trash-list-title">
              <span className="trash-list-icon">
                <img src={getIconByType(item.type)} alt={item.type} />
              </span>
              <span>{item.title || item.name}</span>
              <span className="trash-list-type">{item.type}</span>
            </div>
            <div className="trash-list-info">
              {item.code && <div>Mã dự án: <b>{item.code}</b></div>}
              {item.desc && <div>{item.desc}</div>}
            </div>
            <div className="trash-list-info trash-list-info-2">
              {item.deletedDate && <div>Ngày xóa <b>{item.deletedDate}</b></div>}
              {item.deletedBy && <div>Người xóa <b>{typeof item.deletedBy === 'object' ? item.deletedBy.fullName : item.deletedBy}</b></div>}
              {item.deadline && <div>Deadline <b>{item.deadline}</b></div>}
              {item.status && <div>Trạng thái <span className="trash-list-status" style={statusColor(item.status)}>{item.status}</span></div>}
            </div>
            {item.members && (
              <div className="trash-list-members">
                {item.members.map((m, i) => (
                  <span className="trash-list-member" key={i}>{m}</span>
                ))}
              </div>
            )}
          </div>
          <div className="trash-list-actions">
            <span className="trash-list-type">{item.type}</span>
            <button className="trash-list-more" onClick={() => setActionIdx(idx)}>⋯</button>
            {actionIdx === idx && (
              <ActionTrash
                onRestore={() => handleRestoreItem(item)}
                onDelete={() => handleDeleteItem(item)}
                onClose={() => setActionIdx(null)}
              />
            )}
          </div>
        </div>
      ))}

        {/* Show loading indicator at bottom if still loading but have data */}
        {loading && trashData.length > 0 && (
          <></>
        )}
      </div>
    </div>
  );
};

export default All;
