@import url('../index.css');
.forgot-container {
  display: flex;
  height: 100vh;
  align-items: stretch;
  justify-content: center;
  background: #f7f8fa;
}
.forgot-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: none;
  border-right: none;
  min-width: 0;
}
.forgot-illustration {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  margin: 0 auto;
}
.forgot-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  height: 100vh;
  min-width: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12), 0 1.5px 6px rgba(0,0,0,0.08);
  position: relative;
}
.forgot-form-wrapper {
  padding: 48px 36px 36px 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 420px;
  border-radius: 16px;
  text-align: center;
  margin: 0 auto;
}
.forgot-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}
.forgot-logo-img {
  width: 48px;
  margin-bottom: 8px;
}
.forgot-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #5D5D5D;
  margin-bottom: 8px;
  margin-top: 0;
  letter-spacing: 0.01em;
}
.forgot-desc {
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 28px;
}
.forgot-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
}
.forgot-label {
  text-align: left;
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 2px;
  font-weight: 500;
}
.forgot-input {
  width: 420px;
  box-sizing: border-box;
  padding: 12px 14px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 8px;
  background: #faf7fa;
  color: #3d2c3c;
  transition: border-color 0.2s;
}
.forgot-input:focus {
  outline: none;
  border-color: #a83279;
}
.forgot-btn {
  width: 100%;
  padding: 13px 0;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 10px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
  display: flex;
  align-items: center;
  justify-content: center;
}
.forgot-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.forgot-back-row {
  margin-top: 18px;
  color: #7a6e7a;
  font-size: 1rem;
  position: absolute;
  top: 0;
  left: 0;
  margin-top: 0;
  margin-left: 0;
  padding: 12px 0 0 16px;
  z-index: 10;
}
.forgot-back-link {
  color: #5D5D5D;
  font-weight: 600;
  text-decoration: none;
  margin-left: 4px;
  display: flex;
  align-items: center;
}
.forgot-back-link:hover {
  color: #333;
}
.forgot-input-error {
  border: 2px solid #e53935 !important;
  box-shadow: none;
  background: #fdf6f6;
  position: relative;
  z-index: 2;
}
.forgot-input-validation-wrapper {
  position: relative;
  width: 100%;
}
.forgot-validation-error-message {
  color: #e53935;
  font-size: 0.75rem;
  position: absolute;
  left: 0;
  top: 100%;
  background: #fff;
  padding: 0 6px;
  z-index: 10;
  margin-top: -10px;
  margin-left: 10px;
  line-height: 0.2;
  pointer-events: none;
}
@media (max-width: 1200px) {
  .forgot-left {
    max-width: 45vw;
  }
}
@media (max-width: 900px) {
  .forgot-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .forgot-left, .forgot-right {
    flex: unset;
    width: 100%;
    min-height: 320px;
    max-width: 100vw;
    height: auto;
  }
  .forgot-form-wrapper {
    margin: 32px auto;
    max-width: 95vw;
  }
  .forgot-illustration {
    width: 100%;
    height: auto;
    margin: 0 auto;
  }
}
@media (max-width: 600px) {
  .forgot-form-wrapper {
    padding: 8px;
    max-width: 100vw;
  }
  .forgot-right {
    padding: 16px 4px;
    min-width: unset;
    max-width: 100vw;
  }
  .forgot-input {
    width: 288px;
    height: 44px;
    min-width: 0;
    font-size: 0.98rem;
    padding: 10px 10px;
  }
  .forgot-btn {
    font-size: 1rem;
    padding: 11px 0;
  }
  .forgot-title {
    font-size: 1.2rem;
  }
  .forgot-desc {
    font-size: 0.95rem;
  }
}
@media (max-width: 400px) {
  .forgot-form-wrapper {
    padding: 2px;
  }
  .forgot-title {
    font-size: 1rem;
  }
  .forgot-btn {
    font-size: 0.95rem;
    padding: 9px 0;
  }
}
