import React, { useEffect, useState } from "react";
import { searchAll } from "../../../api/search";

const badgeStyle = (bg, color) => ({
  background: bg,
  color,
  borderRadius: 12,
  fontWeight: 500,
  fontSize: 15,
  padding: "4px 16px",
  display: "inline-block",
  minWidth: 70,
  textAlign: "center",
});

const cellValue = {
  color: "#888",
  fontSize: 14,
};

const statusToVietnamese = (status) => {
  switch (status) {
    case 'completed': return 'Hoàn thành';
    case 'in-progress':
    case 'in_progress': return 'Đang triển khai';
    case 'waiting':
    case 'pending': return 'Đang chờ';
    case 'overdue': return 'Quá hạn';
    case 'review':
    case 'consider': return 'Đang xem xét';
    default: return status;
  }
};

const Note = ({ searchValue, onCountChange }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!searchValue) return;
    setLoading(true);
    searchAll(searchValue)
      .then((res) => {
        const group = (res.data?.groups || []).find(g => g.type === "note");
        setResults(group ? group.items : []);
      })
      .catch(() => setResults([]))
      .finally(() => setLoading(false));
  }, [searchValue]);

  useEffect(() => {
    if (onCountChange) onCountChange(results.length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [results]);

  if (loading) return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Đang tìm kiếm...</div>;
  if (!results.length) {
    return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Không tìm thấy ghi chú cho "{searchValue}"</div>;
  }

  return (
    <div style={{ background: "#fff", padding: 0, borderRadius: 12 }}>
      {results.map((note, idx) => (
        <div
          key={note.id || idx}
          style={{
            border: "1px solid #f3f3f3",
            borderRadius: 12,
            padding: "20px 24px",
            display: "flex",
            alignItems: "center",
            marginBottom: 12,
          }}
        >
          <span
            style={{
              background: "#eaf3ff",
              color: "#4a90e2",
              borderRadius: 8,
              padding: 8,
              marginRight: 16,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: 36,
              height: 36,
            }}
          >
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.828a2 2 0 0 0-.586-1.414l-4.828-4.828A2 2 0 0 0 11.172 1H4zm0 2h7v4a2 2 0 0 0 2 2h4v7a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4zm9 0.414L17.586 7H13a1 1 0 0 1-1-1V2.414z"></path>
            </svg>
          </span>
          <div style={{ flex: 1 }}>
            <div style={{ fontWeight: 600, fontSize: 16, marginBottom: 2 }}>
              {note.title}
            </div>
            <div style={cellValue}>
              <span style={{ fontWeight: 600 }}>Mô tả:</span> {note.description}
            </div>
          </div>
          <span style={badgeStyle("#fff3ed", "#f2994a")}>Ghi chú</span>
        </div>
      ))}
    </div>
  );
};

export default Note;
