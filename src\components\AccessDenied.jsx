import React from 'react';
import { Link } from 'react-router-dom';
// Removed DashboardSidebar and DashboardTopbar imports as they're now handled by DashboardLayout
import '../styles/AccessDenied.css';

const AccessDenied = ({ message = "Bạn không có quyền truy cập trang này" }) => {
  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '400px'
    }}>
          <div className="access-denied-container">
            <div className="access-denied-icon">
              <svg width="120" height="120" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#dc3545" strokeWidth="2"/>
                <path d="M15 9l-6 6" stroke="#dc3545" strokeWidth="2"/>
                <path d="M9 9l6 6" stroke="#dc3545" strokeWidth="2"/>
              </svg>
            </div>
            <h1 className="access-denied-title">T<PERSON>y cập bị từ chối</h1>
            <p className="access-denied-message">{message}</p>
            <div className="access-denied-actions">
              <Link to="/" className="btn btn-primary">
                Về trang chủ
              </Link>
              <button 
                onClick={() => window.history.back()} 
                className="btn btn-secondary"
              >
                Quay lại
              </button>
            </div>
        </div>
      </div>
  );
};

export default AccessDenied;
