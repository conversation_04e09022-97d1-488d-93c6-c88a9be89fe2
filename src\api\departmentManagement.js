import { getCurrentUserRole, getEndpointsByRole } from './endpoints';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;
    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  } 
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Helper function để lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  const role = getCurrentUserRole();
  return getEndpointsByRole(role);
};

// ========== DEPARTMENT MANAGEMENT FUNCTIONS ==========

// Lấy danh sách tất cả phòng ban
export async function getAllDepartments(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const queryString = new URLSearchParams(params).toString();

    // Sử dụng unified department endpoint
    const url = `${endpoints.DEPARTMENTS || endpoints.ALL_DEPARTMENTS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách phòng ban (format đơn giản cho dropdown)
export async function getDepartmentsList(params = {}) {
  try {
    // Sử dụng getAllDepartments vì backend chỉ có một endpoint
    return getAllDepartments(params);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo phòng ban mới
export async function createDepartment(departmentData) {
  try {
    const endpoints = getCurrentEndpoints();

    if (!endpoints.CREATE_DEPARTMENT) {
      throw new Error('Không có quyền tạo phòng ban');
    }

    const response = await fetch(endpoints.CREATE_DEPARTMENT, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(departmentData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật thông tin phòng ban
export async function updateDepartment(departmentId, departmentData) {
  try {
    const endpoints = getCurrentEndpoints();
    
    if (!endpoints.UPDATE_DEPARTMENT) {
      throw new Error('Không có quyền cập nhật phòng ban');
    }

    const response = await fetch(endpoints.UPDATE_DEPARTMENT(departmentId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(departmentData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa phòng ban
export async function deleteDepartment(departmentId) {
  try {
    const endpoints = getCurrentEndpoints();
    
    if (!endpoints.DELETE_DEPARTMENT) {
      throw new Error('Không có quyền xóa phòng ban');
    }

    const response = await fetch(endpoints.DELETE_DEPARTMENT(departmentId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thông tin chi tiết phòng ban
export async function getDepartmentById(departmentId) {
  try {
    const endpoints = getCurrentEndpoints();

    if (!endpoints.GET_DEPARTMENT) {
      throw new Error('Không có quyền xem chi tiết phòng ban');
    }

    const response = await fetch(endpoints.GET_DEPARTMENT(departmentId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Thêm thành viên vào phòng ban
export async function addMemberToDepartment(departmentId, memberData) {
  try {
    const endpoints = getCurrentEndpoints();

    if (!endpoints.ADD_MEMBER_TO_DEPARTMENT) {
      throw new Error('Không có quyền thêm thành viên vào phòng ban');
    }

    const response = await fetch(endpoints.ADD_MEMBER_TO_DEPARTMENT(departmentId), {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(memberData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa thành viên khỏi phòng ban
export async function removeMemberFromDepartment(departmentId, userId) {
  try {
    const endpoints = getCurrentEndpoints();

    if (!endpoints.REMOVE_MEMBER_FROM_DEPARTMENT) {
      throw new Error('Không có quyền xóa thành viên khỏi phòng ban');
    }

    const response = await fetch(endpoints.REMOVE_MEMBER_FROM_DEPARTMENT(departmentId, userId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách phòng ban cho dropdown (chỉ tên)
export async function getDepartmentOptions() {
  try {
    const response = await getDepartmentsList();
    if (response.success) {
      const departments = (response.data || []).map(dept => ({
        value: dept.name || dept.code || dept.id,
        label: dept.name || 'Chưa có tên'
      }));
      
      // Thêm option "Tất cả phòng ban" ở đầu
      return [
        { value: 'Tất cả phòng ban', label: 'Tất cả phòng ban' },
        ...departments
      ];
    }
    return [{ value: 'Tất cả phòng ban', label: 'Tất cả phòng ban' }];
  } catch (err) {
    console.error('Error fetching department options:', err);
    // Fallback về danh sách mặc định
    return [
      { value: 'Tất cả phòng ban', label: 'Tất cả phòng ban' },
      { value: 'IT', label: 'IT' },
      { value: 'Marketing', label: 'Marketing' },
      { value: 'Kế toán', label: 'Kế toán' },
      { value: 'Sale', label: 'Sale' },
      { value: 'HCNS', label: 'HCNS' }
    ];
  }
}

// Transform dữ liệu phòng ban từ backend về format frontend
export const transformDepartmentData = (backendDepartment) => {
  return {
    id: backendDepartment._id || backendDepartment.id || 'N/A',
    name: backendDepartment.name || 'Chưa có tên',
    code: backendDepartment.code || 'N/A',
    description: backendDepartment.description || 'Chưa có mô tả',
    headEmployeeCode: backendDepartment.headEmployeeCode || null,
    headEmployeeName: backendDepartment.headEmployeeName || null,
    memberCount: backendDepartment.memberCount || 0,
    createdAt: backendDepartment.createdAt ? new Date(backendDepartment.createdAt).toLocaleDateString('vi-VN') : 'N/A',
    updatedAt: backendDepartment.updatedAt ? new Date(backendDepartment.updatedAt).toLocaleDateString('vi-VN') : 'N/A'
  };
};

// Kiểm tra quyền quản lý phòng ban theo backend middleware
export const checkDepartmentPermissions = () => {
  const role = getCurrentUserRole();
  return {
    canViewDepartments: true, // Tất cả role đều có thể xem (GET /)
    canCreateDepartments: ['admin', 'ceo', 'hr'].includes(role), // POST / - authMiddleware.hr
    canEditDepartments: ['admin', 'ceo', 'hr'].includes(role), // PUT /:id - authMiddleware.hr
    canDeleteDepartments: ['admin', 'ceo', 'hr'].includes(role), // DELETE /:id - authMiddleware.hr
    canManageMembers: ['admin', 'ceo', 'hr'].includes(role), // POST /:id/members, DELETE /:id/members/:userId - authMiddleware.hr
    role: role
  };
};

// Export getCurrentUserRole để sử dụng ở nơi khác
export { getCurrentUserRole };
