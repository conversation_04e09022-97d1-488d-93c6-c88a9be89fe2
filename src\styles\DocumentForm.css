/* C<PERSON>i thiện scrollbar chung cho toàn bộ giao diện detail */
.document-form-overlay *::-webkit-scrollbar {
  width: 5px;
}

.document-form-overlay *::-webkit-scrollbar-track {
  background: transparent;
}

.document-form-overlay *::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.document-form-overlay *:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
}

.document-form-overlay * {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

/* Document Form Overlay */
.document-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 999; /* Tăng z-index để hiển thị trên topbar */
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

/* Document Form Container */
.document-form-container {
  position: fixed;
  top: 0; /* Đã sửa: bắt đầu từ đỉnh màn hình, che cả topbar */
  right: 0;
  height: 100vh; /* Đ<PERSON> sửa: chiều cao full màn hình */
  width: 420px;
  max-height: 100vh;
  background: #F6F6F6;
  box-shadow: -2px 0 16px 0 rgba(60, 72, 88, 0.10);
  padding: 0;
  font-size: 15px;
  color: #222;
  z-index: 1001; /* Tăng cao hơn overlay để đảm bảo hiển thị trên cùng */
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.25s cubic-bezier(.4, 0, .2, 1), opacity 0.18s;
  overflow: hidden;
}

.document-form-container.show {
  transform: translateX(0);
  opacity: 1;
}

/* Đảm bảo form luôn hiển thị trên cùng */
.document-form-overlay,
.document-form-container {
  pointer-events: auto !important;
}

/* Close Button */
.document-form-close-btn {
  position: absolute;
  top: 30px;
  right: 20px;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s;
  z-index: 9999; /* Tăng z-index cao hơn */
  padding: 0;
  opacity: 1 !important;
  visibility: visible !important;
}

.document-form-close-btn:hover {
  background: #f1f3f4;
}

.document-form-close-btn img {
  width: 20px;
  height: 20px;
  display: block !important;
  opacity: 1 !important;
}

/* Form Content */
.document-form-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 20px 20px 20px;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.document-form-content:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.document-form-content::-webkit-scrollbar {
  width: 5px;
}

.document-form-content::-webkit-scrollbar-track {
  background: transparent;
}

.document-form-content::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.document-form-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
}

/* Header cố định */
.document-form-header {
  position: sticky;
  top: 0;
  border-bottom: 1px solid #eaeaea;
  padding: 30px 20px 16px 20px; /* Tăng padding-top để tránh bị che bởi thanh trạng thái trên mobile */
  z-index: 1003; /* Tăng z-index để đảm bảo header hiển thị đúng */
  flex-shrink: 0;
  background: #F6F6F6; /* Đảm bảo header có background giống container */
}

.document-form-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #222;
  line-height: 1.4;
  padding-right: 50px; /* Để tránh chồng lên nút đóng */
}

/* Form Styles */
.document-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* Special styling for file upload row */
.form-row.file-upload-row {
  flex-direction: column;
  align-items: stretch;
  gap: 8px;
}

.form-row.file-upload-row .form-label {
  min-width: auto;
  padding-top: 0;
  margin-bottom: 8px;
}

.form-row.file-upload-row .form-value {
  width: 100%;
}

.form-label {
  min-width: 110px;
  color: #888;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.form-value {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

/* Form Inputs */
.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  color: #222;
  background: #fff;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.date-input {
  max-width: 226px;
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  color: #222;
  background: #fff;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* File Upload Section */
.file-upload-section {
  width: 100%;
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  padding: 50px 20px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  min-height: 120px;
}

.file-upload-area:hover {
  border-color: #2563eb;
  background: #eff6ff;
  color: #2563eb;
}

.loadfile-icon {
  width: 48px;
  height: 48px;
  opacity: 0.7;
}

.upload-text {
  font-size: 14px;
  color: #6b7280;
}

.file-upload-area:hover .upload-text {
  color: #2563eb;
}

.files-container {
  width: 100%;
}

.add-more-files-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 16px 20px;
  border: 1px dashed #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 12px;
}

.add-more-files-btn:hover {
  border-color: #2563eb;
  background: #eff6ff;
  color: #2563eb;
}

.loadfile-icon-small {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.file-input-hidden {
  display: none;
}

.selected-files {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 14px;
}

.file-name {
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.remove-file-btn:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Comment Section */
.comment-section {
  width: 100%;
}

.comment-input-wrapper {
  display: flex;
  align-items: center;
  background: #fafbfc;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 12px;
  gap: 8px;
}

.comment-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #222;
  outline: none;
}

.comment-input::placeholder {
  color: #9ca3af;
}

.comment-send-btn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.comment-send-btn:hover {
  background: #f1f3f4;
  color: #2563eb;
}

/* Document Form Footer cố định */
.document-form-footer {
  position: sticky;
  bottom: 0;
  border-top: 1px solid #eaeaea;
  padding: 12px 20px 16px 20px;
  width: 100%;
  z-index: 5;
  flex-shrink: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #fff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  min-width: 105px;
}

.btn-cancel:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-submit {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background: #007BFF;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  height: 40px;
  min-width: 105px;
}

.btn-submit:hover {
  background: #1d4ed8;
}

/* CSS cho comment box giống JobDetail */
.detail-row {
  display: flex;
  align-items: flex-start;
  margin: 16px 0;
}

.detail-row input.detail-comment-input {
  flex: 1;
  border-radius: 8px;
  padding: 8px 0;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
  border: none;
  background: transparent;
  height: 36px;
}

.detailform-comment-box {
   background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 64px;
  min-height: 38px;
  height: 38px;
  box-shadow: none;
  width: 100%;
}

.detail-comment-actions {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Mobile responsive */
@media (max-width: 600px) {
  .document-form-container {
    width: 100vw;
    min-width: 0;
    border-radius: 0;
    left: 0;
  }

  .document-form-header {
    border-radius: 0;
    padding-top: 40px; /* Tăng padding-top thêm cho mobile để tránh bị che bởi thanh trạng thái */
  }

  .document-form-close-btn {
    top: 40px; /* Điều chỉnh vị trí nút đóng trên mobile */
  }
}

.detail-comment-btn, .detail-comment-attach {
  background: none;
  color: #757575;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 18px;
  margin: 0;
  cursor: pointer;
  transition: background 0.2s;
}

.detail-comment-btn:hover, .detail-comment-attach:hover {
  background: #f1f3f4;
}

.detail-comment-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.detail-comment-btn:disabled:hover {
  background: none;
}

/* Tách biệt bình luận */
.detail-row-comment {
  margin-top: 0;
  margin-bottom: 0;
}
