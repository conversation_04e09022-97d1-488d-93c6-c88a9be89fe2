/* System Maintenance Styles - Compatible with HRLayout */

.system-maintenance-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 32px;
  color: #6c757d;
}

.system-maintenance-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0;
}

.maintenance-info-card,
.maintenance-controls-card,
.maintenance-warning-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.maintenance-info-card {
  padding: 24px;
  background: #f8f9fa;
  border-left: 4px solid #21194e;
}

.maintenance-info-card h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-item span {
  color: #2c3e50;
  font-size: 1rem;
}

.maintenance-controls-card {
  padding: 24px;
}

.maintenance-controls-card h3 {
  margin: 0 0 24px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.control-group {
  margin-bottom: 20px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.control-group textarea,
.control-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  font-family: inherit;
  resize: vertical;
  box-sizing: border-box;
}

.control-group textarea:focus,
.control-group input:focus {
  outline: none;
  border-color: #2d5be3;
  box-shadow: 0 0 0 3px rgba(45, 91, 227, 0.1);
}

.control-group textarea:disabled,
.control-group input:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.7;
}

.maintenance-actions {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.maintenance-toggle-btn {
  padding: 12px 32px;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.maintenance-toggle-btn.disabled {
  background: #007BFF;
  color: white;
}

.maintenance-toggle-btn.disabled:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.maintenance-toggle-btn.enabled {
  background: #dc3545;
  color: white;
}

.maintenance-toggle-btn.enabled:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.maintenance-toggle-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.maintenance-warning-card {
  padding: 20px;
  background: #fff3cd;
  border-left: 4px solid #ffc107;
  display: flex;
  gap: 16px;
}

.warning-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warning-content {
  flex: 1;
  color: #856404;
}

.warning-content strong {
  display: block;
  margin-bottom: 8px;
  color: #664d03;
  font-weight: 600;
}

.warning-content ul {
  margin: 0;
  padding-left: 20px;
}

.warning-content li {
  margin-bottom: 4px;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* Status Indicator - sử dụng trong toolbar */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
}

.status-indicator.active {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-indicator.inactive {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #b6d7ff;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 14px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #2d5be3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.button-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .system-maintenance-content {
    gap: 16px;
  }

  .maintenance-info-card,
  .maintenance-controls-card,
  .maintenance-warning-card {
    padding: 16px;
  }

  .maintenance-actions {
    flex-direction: column;
    align-items: center;
  }

  .maintenance-toggle-btn {
    min-width: 140px;
    padding: 10px 24px;
    font-size: 14px;
  }

  .info-grid {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .maintenance-info-card,
  .maintenance-controls-card,
  .maintenance-warning-card {
    padding: 12px;
  }

  .maintenance-toggle-btn {
    padding: 10px 20px;
    font-size: 13px;
    min-width: 120px;
  }

  .maintenance-warning-card {
    flex-direction: column;
    gap: 12px;
  }

  .warning-icon {
    text-align: center;
  }
} 