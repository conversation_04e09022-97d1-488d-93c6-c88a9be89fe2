import { useEffect, useRef, useState } from "react";
import { getProfile } from "../../api/profile";
import { transformUserData } from "../../api/userManagement";
import addIcon from "../../assets/add.svg";
import closeIcon from "../../assets/closePanel.svg";
import startDateIcon from "../../assets/creationdate.svg";
import endDateIcon from "../../assets/enddate.svg";
import fileTextIcon from "../../assets/file-text.svg";
import loadFileIcon from "../../assets/loadfile.svg";
import user1Img from "../../assets/user1.png";
import "../../styles/JobCreate.css";
import MemberAddPopup from "./MemberAddPopup";
import FollowerAddPopup from "./FollowerAddPopup";

// Custom Select Dropdown Component
const CustomSelect = ({
  options,
  value,
  onChange,
  placeholder,
  disabled,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const selectedOption = options.find((option) => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-select" ref={dropdownRef}>
      <div
        className={`job-form-group select ${error ? "error" : ""}`}
        onClick={toggleDropdown}
        style={{
          position: "relative",
          cursor: disabled ? "not-allowed" : "pointer",
          opacity: disabled ? 0.7 : 1,
        }}
      >
        <div
          className="select-selected"
          style={{
            padding: "6px 8px",
            border: "1px solid #bdbdbd",
            borderRadius: "4px",
            fontSize: "13px",
            fontWeight: "500",
            backgroundColor: "#fff",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            height: "32px",
            boxSizing: "border-box",
          }}
        >
          <span style={{ color: selectedOption ? "#5B5B5B" : "#999" }}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ marginLeft: "8px" }}
          >
            <path
              d="M6 9L12 15L18 9"
              stroke="#666666"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>

      {isOpen && (
        <div className="job-dropdown">
          {options.map((option) => (
            <div
              key={option.value}
              className="job-dropdown-item"
              onClick={() => handleSelect(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const JobCreate = ({
  onCancel,
  onSubmit,
  disableDepartmentFilter = false,
  projectId,
  projectMembers = [],
  loadingProjectMembers = false,
  allUsers = [],
  loadingAllUsers = false,
  isSubTaskCreation = false,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "medium",
    members: [],
    followers: [],
    attachments: [],
    isRecurring: false,
    recurringFrequency: "daily", // Mặc định là "Hằng ngày"
    weeklyDays: [], // Mảng chứa các ngày trong tuần được chọn
    mainTask: "",
    dependentTask: "",
    estimatedDays: "",
  });
  const [showMemberPopup, setShowMemberPopup] = useState(false);
  const [showFollowerPopup, setShowFollowerPopup] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    priority: "",
    members: "",
    mainTask: "",
    dependentTask: "",
    estimatedDays: "",
  });
  const [users, setUsers] = useState([]);
  const [userProfile, setUserProfile] = useState(null);

  // Lấy thông tin user hiện tại từ localStorage
  const getUserFromStorage = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
      // Xử lý cả trường hợp user nested và không nested
      return userRaw.user || userRaw;
    } catch (error) {
      console.error("Error parsing user from localStorage:", error);
      return {};
    }
  };

  const currentUser = userProfile || getUserFromStorage();

  // Load user profile từ API nếu localStorage không có đủ thông tin
  useEffect(() => {
    const loadUserProfile = async () => {
      const storageUser = getUserFromStorage();
      if (!storageUser.avatar || !storageUser.fullName) {
        try {
          const profileRes = await getProfile();
          const profile = profileRes.data || profileRes;
          setUserProfile(profile);
          // Loaded user profile from API
        } catch (error) {
          console.error("Error loading user profile:", error);
        }
      }
    };
    loadUserProfile();
  }, []);

  // Debug: Log để kiểm tra cấu trúc dữ liệu user
          // Current user data loaded

  // Sử dụng projectMembers thay vì gọi getAllUsers
  useEffect(() => {
    if (projectMembers && projectMembers.length > 0) {
      // Transform projectMembers để đảm bảo format đúng
      const transformed = projectMembers.map(member => {
        if (member.user) {
          // Nếu member có nested user object
          return transformUserData(member.user);
        } else {
          // Nếu member đã là user object
          return transformUserData(member);
        }
      });
            setUsers(transformed);
    } else {
          setUsers([]);
    }
  }, [projectMembers]);

  const fileInputRef = useRef(null);

  // Fetch users from API when component mounts
  // useEffect(() => {
  //   const fetchUsers = async () => {
  //     try {
  //       setLoadingUsers(true);
  //       const response = await getAllUsers({
  //         populate: "department,departmentId",
  //         include: "department",
  //       });

  //       if (response.success) {
  //         // Transform user data to match the expected format
  //         const transformedUsers = (response.data || []).map((backendUser) =>
  //           transformUserData(backendUser)
  //         );
  //         setUsers(transformedUsers);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching users:", error);
  //     } finally {
  //       setLoadingUsers(false);
  //     }
  //   };

  //   fetchUsers();
  // }, []);

  const handleInputChange = (field, value) => {
    setFormData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Khi chuyển sang "weekly", tự động chọn tất cả các ngày làm việc
      if (field === "recurringFrequency" && value === "weekly") {
        newData.weeklyDays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
      }
      // Khi chuyển sang "daily", xóa các ngày đã chọn
      else if (field === "recurringFrequency" && value === "daily") {
        newData.weeklyDays = [];
      }

      return newData;
    });

    // Clear error when user starts typing
    if (error) setError("");
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
          // Form submitted

    // Reset field errors
    setFieldErrors({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      priority: "",
      members: "",
      mainTask: "",
      dependentTask: "",
      estimatedDays: "",
    });

    let hasErrors = false;
    const newFieldErrors = {};

    // Validation
    if (!formData.name.trim()) {
      newFieldErrors.name = "Tên công việc không được để trống";
      hasErrors = true;
    }

    // Validation cho mô tả - bắt buộc phải có
    if (!formData.description.trim()) {
      newFieldErrors.description = "Mô tả công việc không được để trống";
      hasErrors = true;
    } else if (formData.description.trim().split(/\s+/).length > 1000) {
      newFieldErrors.description = "Mô tả không được vượt quá 1000 từ";
      hasErrors = true;
    }

    const today = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD

    if (!formData.startDate) {
      newFieldErrors.startDate = "Vui lòng chọn thời gian bắt đầu";
      hasErrors = true;
    } else if (formData.startDate < today) {
      newFieldErrors.startDate = "Thời gian bắt đầu không thể là quá khứ";
      hasErrors = true;
    }

    if (!formData.endDate) {
      newFieldErrors.endDate = "Vui lòng chọn thời gian kết thúc";
      hasErrors = true;
    } else if (formData.endDate < today) {
      newFieldErrors.endDate = "Thời gian kết thúc không thể là quá khứ";
      hasErrors = true;
    }

    if (
      formData.startDate &&
      formData.endDate &&
      new Date(formData.startDate) > new Date(formData.endDate)
    ) {
      newFieldErrors.endDate = "Thời gian kết thúc phải sau thời gian bắt đầu";
      hasErrors = true;
    }

    if (!formData.priority) {
      newFieldErrors.priority = "Vui lòng chọn mức độ ưu tiên";
      hasErrors = true;
    }

    // Validation cho thành viên - bắt buộc phải có ít nhất 1 thành viên
    if (!formData.members || formData.members.length === 0) {
      newFieldErrors.members = "Vui lòng thêm ít nhất một thành viên";
      hasErrors = true;
    }



    if (!isSubTaskCreation && (formData.estimatedDays === "" || isNaN(Number(formData.estimatedDays)) || Number(formData.estimatedDays) <= 0)) {
      newFieldErrors.estimatedDays = "Vui lòng nhập thời gian dự kiến hợp lệ (>0)";
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(newFieldErrors);
      return;
    }

    try {
      setLoading(true);
      setError("");
      // Map FE -> BE
      const priorityMap = {
        medium: "medium",
        high: "high",
        low: "low",
        urgent: "urgent",
      };
      // Lấy ID của user hiện tại với nhiều fallback
      const createdById = currentUser._id ||
                         currentUser.id ||
                         (currentUser.user && currentUser.user._id) ||
                         (currentUser.user && currentUser.user.id) ||
                         "unknown_user";
      
      // Tạo danh sách assignedToIds từ formData.members
      const assignedToIds = formData.members.map(member => member.id || member.userId).filter(Boolean);

      // Tạo danh sách followerIds từ formData.followers
      const followerIds = formData.followers.map(follower => follower.id || follower.userId).filter(Boolean);

      // Debug log để kiểm tra dữ liệu trước khi gửi
      console.log('JobCreate - formData.followers:', formData.followers);
      console.log('JobCreate - followerIds:', followerIds);

      const jobData = {
        title: formData.name.trim(),
        description: formData.description.trim(),
        projectId: projectId,
        assignedToIds: assignedToIds, // Gửi array thay vì single ID
        assignedToId: assignedToIds[0] || "", // backward compatibility
        followerIds: followerIds, // Danh sách người theo dõi
        createdById,
        status: "pending", // Mặc định là pending (đang chờ)
        priority: priorityMap[formData.priority] || "medium",
        dueDate: new Date(formData.endDate).toISOString(),
        startDate: new Date(formData.startDate).toISOString(),
        // Thêm thông tin members để có thể sử dụng trong WorkContent
        membersData: formData.members, // Gửi thêm thông tin đầy đủ về members
        followersData: formData.followers, // Gửi thêm thông tin đầy đủ về followers
        // Thêm original status và priority để hiển thị đúng trong UI
        originalStatus: "waiting", // Status gốc mặc định là waiting
        originalPriority: formData.priority, // Priority gốc từ form để hiển thị
        // Thêm timestamp để đảm bảo công việc mới nằm đầu danh sách
        createdAt: new Date().toISOString(),
        // Thêm tệp đính kèm để WorkContent có thể upload sau khi tạo task
        attachments: formData.attachments, // Gửi danh sách tệp đính kèm để upload riêng
        // Thêm thông tin lặp lại công việc
        isRecurring: formData.isRecurring,
        recurringFrequency: formData.isRecurring ? formData.recurringFrequency : null,
        weeklyDays: formData.isRecurring && formData.recurringFrequency === "weekly" ? formData.weeklyDays : null,
        // Thêm thông tin cho sub-task creation
        dependentTask: isSubTaskCreation ? formData.dependentTask : null,

        estimatedDays: formData.estimatedDays,
      };

      // Debug log để kiểm tra jobData cuối cùng
      console.log('JobCreate - Final jobData:', jobData);
      console.log('JobCreate - jobData.followersData:', jobData.followersData);
      console.log('JobCreate - jobData.followerIds:', jobData.followerIds);

      if (onSubmit) {
        await onSubmit(jobData);
      }
    } catch (error) {
      console.error("Error creating job:", error);
      setError("Có lỗi xảy ra khi tạo công việc");
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = () => {
    setShowMemberPopup(true);
  };

  const handleMemberAdded = (member) => {
    setFormData((prev) => ({
      ...prev,
      members: [...prev.members, member],
    }));
    // Clear members error when user adds a member
    if (fieldErrors.members) {
      setFieldErrors((prev) => ({
        ...prev,
        members: "",
      }));
    }
  };

  const handleRemoveMember = (indexToRemove) => {
    setFormData((prev) => ({
      ...prev,
      members: prev.members.filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleAddFollower = () => {
    setShowFollowerPopup(true);
  };

  const handleFollowerAdded = (follower) => {
    setFormData((prev) => ({
      ...prev,
      followers: [...prev.followers, follower],
    }));
  };

  const handleRemoveFollower = (indexToRemove) => {
    setFormData((prev) => ({
      ...prev,
      followers: prev.followers.filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleWeeklyDayChange = (dayValue, isChecked) => {
    setFormData((prev) => ({
      ...prev,
      weeklyDays: isChecked
        ? [...prev.weeklyDays, dayValue]
        : prev.weeklyDays.filter(day => day !== dayValue)
    }));
  };

  const handleAddFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setFormData((prev) => ({
      ...prev,
      attachments: [...prev.attachments, ...files],
    }));
    // Reset input để có thể chọn lại cùng file
    e.target.value = "";
  };

  const handleRemoveFile = (indexToRemove) => {
    setFormData((prev) => ({
      ...prev,
      attachments: prev.attachments.filter(
        (_, index) => index !== indexToRemove
      ),
    }));
  };

  // Chuyển đổi các lựa chọn priority thành định dạng options cho CustomSelect
  const priorityOptions = [
    { value: "low", label: "Thấp" },
    { value: "medium", label: "Trung bình" },
    { value: "high", label: "Cao" },
  ];

  // Dữ liệu mẫu cho dropdown nhiệm vụ
  const mainTaskOptions = [
    { value: "task1", label: "Làm giao diện trang chủ dự án" },
    { value: "task2", label: "Thiết kế database" },
    { value: "task3", label: "Viết API" },
  ];
  const dependentTaskOptions = [
    { value: "task1", label: "Làm menu" },
    { value: "task2", label: "Làm logo" },
    { value: "task3", label: "Làm dropdown" },
  ];

  return (
    <div className="job-create-panel-backdrop" onClick={onCancel}>
    <div className="job-create-panel-container" onClick={e => e.stopPropagation()}>
      <div className="job-create-panel-header">
        <span className="job-create-panel-title">
          {isSubTaskCreation ? "Tạo công việc phụ" : "Tạo công việc chính"}
        </span>
        <button className="job-panel-close-btn" onClick={onCancel}>
          <img src={closeIcon} alt="Đóng" />
        </button>
      </div>
      <form onSubmit={handleSubmit} className="job-create-panel-form">
        {error && (
          <div className="error-message-panel">
            {error}
          </div>
        )}
        <div className="job-panel-section">
          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Tên công việc</div>
              <div className="job-panel-value">
                <input
                  type="text"
                  placeholder="Nhập tên công việc"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className={`job-create-input ${fieldErrors.name ? "error" : ""}`}
                />
              </div>
            </div>
            {fieldErrors.name && (
              <div className="job-create-field-error">
                {fieldErrors.name}
              </div>
            )}
          </div>
          <div className="job-panel-row">
            <div className="job-panel-label">Người tạo</div>
            <div className="job-panel-value">
              <img
                src={currentUser.avatar || currentUser.profilePicture || user1Img}
                alt="avatar"
                className="job-panel-avatar"
              />
              <span className="job-panel-label">
                {currentUser.fullName || currentUser.name || currentUser.username || currentUser.email || "Admin"}
              </span>
            </div>
          </div>
          {/*---------------- Trường thêm người theo dõi -------------------------- */}
          {!isSubTaskCreation && (
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Người theo dõi</div>
                <div className="job-panel-value job-panel-members-flex-row">
                  <button type="button" className="job-panel-add-member-btn" onClick={handleAddFollower}>
                    <img src={addIcon} alt="Thêm người theo dõi" />
                  </button>
                  <div className="job-panel-members-list">
                    {formData.followers.map((follower, index) => (
                      <span key={index} className="job-panel-member">
                        <img src={follower.avatar} alt={follower.name} className="job-panel-avatar" />
                        <button type="button" className="job-panel-remove-member-btn" onClick={() => handleRemoveFollower(index)} title="Xóa người theo dõi">×</button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
          {/*---------------- Trường thêm thành viên -------------------------- */}
          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Thành viên</div>
              <div className="job-panel-value job-panel-members-flex-row">
                <button type="button" className="job-panel-add-member-btn" onClick={handleAddMember}>
                  <img src={addIcon} alt="Thêm thành viên" />
                </button>
                <div className="job-panel-members-list">
                  {formData.members.map((member, index) => (
                    <span key={index} className="job-panel-member">
                      <img src={member.avatar} alt={member.name} className="job-panel-avatar" />
                      <button type="button" className="job-panel-remove-member-btn" onClick={() => handleRemoveMember(index)} title="Xóa thành viên">×</button>
                    </span>
                  ))}
                </div>
              </div>
            </div>
            {fieldErrors.members && (
              <div className="job-create-field-error">
                {fieldErrors.members}
              </div>
            )}
          </div>
          
          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Mức độ ưu tiên</div>
              <div className="job-panel-value">
                <CustomSelect
                  options={priorityOptions}
                  value={formData.priority}
                  onChange={(value) => handleInputChange("priority", value)}
                  placeholder="Chọn mức độ ưu tiên"
                  error={fieldErrors.priority}
                />
              </div>
            </div>
            {fieldErrors.priority && (
              <div className="job-create-field-error">
                {fieldErrors.priority}
              </div>
            )}
          </div>

          {/* Conditional fields for sub-task creation */}
          {isSubTaskCreation && (
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Nhiệm vụ phụ thuộc</div>
                <div className="job-panel-value">
                  <CustomSelect
                    options={dependentTaskOptions}
                    value={formData.dependentTask}
                    onChange={(value) => handleInputChange("dependentTask", value)}
                    placeholder="Thêm nhiệm vụ phụ thuộc"
                    style={{ color: 'rgba(255, 0, 0, 1)' }}
                  />
                </div>
              </div>
            </div>
          )}

          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Ngày bắt đầu</div>
              <div className="job-panel-value">
                <div 
                  className={`job-create-date-input-group-custom ${fieldErrors.startDate ? "error" : ""}`} 
                  style={{width: '100%', cursor: 'pointer'}}
                  onClick={() => document.querySelector('input[name="jobStartDate"]')?.showPicker?.()}
                >
                  <img src={startDateIcon} alt="calendar" className="job-calendar-icon-custom" />
                  <input
                    name="jobStartDate"
                    type="date"
                    value={formData.startDate}
                    min={new Date().toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange("startDate", e.target.value)}
                    placeholder="Chọn ngày bắt đầu"
                    className="job-create-date-custom"
                    style={{cursor: 'pointer'}}
                  />
                </div>
              </div>
            </div>
            {fieldErrors.startDate && (
              <div className="job-create-field-error">
                {fieldErrors.startDate}
              </div>
            )}
          </div>
          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Ngày kết thúc</div>
              <div className="job-panel-value">
                <div 
                  className={`job-create-date-input-group-custom ${fieldErrors.endDate ? "error" : ""}`} 
                  style={{width: '100%', cursor: 'pointer'}}
                  onClick={() => document.querySelector('input[name="jobEndDate"]')?.showPicker?.()}
                >
                  <img src={endDateIcon} alt="calendar" className="job-calendar-icon-custom" />
                  <input
                    name="jobEndDate"
                    type="date"
                    value={formData.endDate}
                    min={new Date().toISOString().split('T')[0]}
                    onChange={(e) => handleInputChange("endDate", e.target.value)}
                    placeholder="Chọn ngày kết thúc"
                    className="job-create-date-custom"
                    style={{cursor: 'pointer'}}
                  />
                </div>
              </div>
            </div>
            {fieldErrors.endDate && (
              <div className="job-create-field-error">
                {fieldErrors.endDate}
              </div>
            )}
          </div>
          {!isSubTaskCreation && (
            <>
              <div className="job-create-field-wrapper">
                <div className="job-panel-row">
                  <div className="job-panel-label">Lặp lại</div>
                  <div className="job-panel-value">
                    <label className="ios-switch">
                      <input
                        type="checkbox"
                        checked={formData.isRecurring}
                        onChange={(e) => handleInputChange("isRecurring", e.target.checked)}
                      />
                      <span className="slider"></span>
                    </label>
                  </div>
                </div>
              </div>
              {formData.isRecurring && (
                <div className="job-create-field-wrapper">
                  <div className="job-panel-row">
                    <div className="job-panel-label">Tần suất lặp lại</div>
                    <div className="job-panel-value">
                      <CustomSelect
                        options={[
                          { value: "daily", label: "Hằng ngày" },
                          { value: "weekly", label: "Hằng tuần" }
                        ]}
                        value={formData.recurringFrequency}
                        onChange={(value) => handleInputChange("recurringFrequency", value)}
                        placeholder="Chọn tần suất lặp lại"
                      />
                    </div>
                  </div>
                </div>
              )}
              {formData.isRecurring && formData.recurringFrequency === "weekly" && (
                <div className="job-create-field-wrapper">
                  <div className="job-panel-row">
                    <div className="job-panel-label">Lặp lại vào</div>
                    <div className="job-panel-value">
                      <div className="weekly-days-container">
                        {[
                          { value: "monday", label: "Thứ 2" },
                          { value: "tuesday", label: "Thứ 3" },
                          { value: "wednesday", label: "Thứ 4" },
                          { value: "thursday", label: "Thứ 5" },
                          { value: "friday", label: "Thứ 6" },
                          { value: "saturday", label: "Thứ 7" }
                        ].map((day) => (
                          <label key={day.value} className="weekly-day-checkbox">
                            <input
                              type="checkbox"
                              checked={formData.weeklyDays.includes(day.value)}
                              onChange={(e) => handleWeeklyDayChange(day.value, e.target.checked)}
                            />
                            <span className="weekly-day-label">{day.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {!isSubTaskCreation && (
            <div className="job-create-field-wrapper">
              <div className="job-panel-row">
                <div className="job-panel-label">Thời gian dự kiến (ngày)</div>
                <div className="job-panel-value">
                  <input
                    type="number"
                    min="1"
                    placeholder="Nhập số ngày"
                    value={formData.estimatedDays}
                    onChange={(e) => handleInputChange("estimatedDays", e.target.value)}
                    className={`job-create-input ${fieldErrors.estimatedDays ? "error" : ""}`}
                    style={{ width: "100%" }}
                  />
                </div>
              </div>
              {fieldErrors.estimatedDays && (
                <div className="job-create-field-error">{fieldErrors.estimatedDays}</div>
              )}
            </div>
          )}
          <div className="job-create-field-wrapper">
            <div className="job-panel-row">
              <div className="job-panel-label">Mô tả</div>
              <div className="job-panel-value">
                <textarea
                  placeholder="Nhập mô tả công việc"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  rows={3}
                  className={`job-create-textarea ${fieldErrors.description ? "error" : ""}`}
                />
              </div>
            </div>
            {fieldErrors.description && (
              <div className="job-create-field-error">
                {fieldErrors.description}
              </div>
            )}
          </div>
          {!isSubTaskCreation && (
            <div className="job-panel-rows">
              <div className="job-panel-label">Tệp đính kèm</div>
              <div className="job-panel-value" style={{flexDirection: 'column', alignItems: 'flex-start', gap: 0}}>
                <div className="job-panel-file-upload-custom" onClick={handleAddFile}>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    style={{ display: "none" }}
                    ref={fileInputRef}
                  />
                  <img src={loadFileIcon} alt="Tải tệp" className="job-panel-file-upload-icon" />
                  <span className="job-panel-file-upload-text">Bấm vào để tải lên tệp</span>
                </div>
                {formData.attachments && formData.attachments.length > 0 && (
                  <div className="job-panel-file-list" style={{width: '100%'}}>
                    {formData.attachments.map((file, idx) => (
                      <div className="job-panel-file-item" key={idx}>
                        <img src={fileTextIcon} alt="file" className="job-panel-file-icon" />
                        <span className="job-panel-file-name">{file.name}</span>
                        <button type="button" className="job-panel-remove-file-btn" onClick={() => handleRemoveFile(idx)} title="Xóa tệp">×</button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </form>
      <div className="job-create-panel-footer">
        <button type="button" className="job-panel-cancel-btn" onClick={onCancel}>Huỷ</button>
        <button type="submit" className="job-panel-create-btn" onClick={handleSubmit} disabled={loading}>
          {loading ? "Đang tạo..." : "Tạo công việc"}
        </button>
      </div>
      <MemberAddPopup
        isOpen={showMemberPopup}
        onClose={() => setShowMemberPopup(false)}
        onAddMember={handleMemberAdded}
        existingMembers={formData.members}
        users={users}
        loadingUsers={loadingProjectMembers}
        disableDepartmentFilter={disableDepartmentFilter}
      />
      <FollowerAddPopup
        isOpen={showFollowerPopup}
        onClose={() => setShowFollowerPopup(false)}
        onAddMember={handleFollowerAdded}
        existingMembers={formData.followers}
        users={allUsers.length > 0 ? allUsers : users}
        loadingUsers={loadingAllUsers || loadingProjectMembers}
        disableDepartmentFilter={true}
      />
    </div>
    </div>
  );
};

export default JobCreate;