import React from 'react';
import '../styles/MaintenancePage.css';

const MaintenancePage = ({ maintenanceData }) => {
  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'Asia/Ho_Chi_Minh'
      });
    } catch (error) {
      return '';
    }
  };

  return (
    <div className="maintenance-page">
      <div className="maintenance-container">
        <div className="maintenance-icon">
          <svg 
            width="120" 
            height="120" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H9V3H14L19 8V11H21V9ZM4 8V6L6 4H8V2H6L4 4V6L2 8V10H4V8ZM18 12H6C5.45 12 5 12.45 5 13V19C5 19.55 5.45 20 6 20H18C18.55 20 19 19.55 19 19V13C19 12.45 18.55 12 18 12ZM17 18H7V14H17V18Z" 
              fill="#ff9800"
            />
          </svg>
        </div>
        
        <h1 className="maintenance-title">
          Hệ thống đang bảo trì
        </h1>
        
        <div className="maintenance-message">
          {maintenanceData?.message || 'Hệ thống đang được bảo trì. Vui lòng quay lại sau.'}
        </div>
        
        {maintenanceData?.enabledAt && (
          <div className="maintenance-info">
            <p>
              <strong>Thời gian bắt đầu:</strong> {formatDateTime(maintenanceData.enabledAt)}
            </p>
          </div>
        )}
        
        {maintenanceData?.estimatedDowntime && (
          <div className="maintenance-info">
            <p>
              <strong>Thời gian dự kiến:</strong> {maintenanceData.estimatedDowntime}
            </p>
          </div>
        )}
        
        <div className="maintenance-actions">
          <button 
            className="refresh-button"
            onClick={() => window.location.reload()}
          >
            <svg 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z" 
                fill="currentColor"
              />
            </svg>
            Thử lại
          </button>
        </div>
        
        <div className="maintenance-footer">
          <p>Cảm ơn bạn đã kiên nhẫn chờ đợi!</p>
          <p>Chúng tôi đang nỗ lực để mang đến trải nghiệm tốt nhất.</p>
        </div>
      </div>
    </div>
  );
};

export default MaintenancePage; 