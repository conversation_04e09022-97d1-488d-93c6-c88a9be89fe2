@import url('../index.css');

.kanban-board {
  display: flex;
  gap: 15px;
  /* padding: 24px; */
  background: #fff;
  height: 100vh;
  overflow-x: auto;
  overflow-y: hidden;
}
.kanban-column {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  min-width: 337px;
  width: 337px;
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  border: 1px solid #7C7C7C1A;

}
.kanban-column-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 16px;
}
.kanban-column-title {
  font-size: 13px;
  font-weight: 400;
  color: #5D5D5D;
}

/* <PERSON><PERSON>u sắc cho kanban header */
.kanban-column-title.prerequisites {
  color: #d32f2f;
}

.kanban-column-title.undo {
  color: #518CAE;
}

.kanban-column-count {
  color: #888;
  font-size: 14px;
  font-weight: 400;
}
.kanban-tasks {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  padding-right: 4px;
  min-height: 0;
}
.kanban-tasks::-webkit-scrollbar {
  width: 8px;
  background: transparent;
  transition: background 0.2s;
}
.kanban-tasks::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: background 0.2s;
}
.kanban-tasks:hover::-webkit-scrollbar {
  background: #eee;
}
.kanban-tasks:hover::-webkit-scrollbar-thumb {
  background: #bdbdbd;
}
.kanban-task {
  /* background: #f3f4f6; */
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: box-shadow 0.2s;
  width: 305px;
}
.kanban-task:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
}
.kanban-task-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  justify-content: space-between;
}
.kanban-task-id {
  font-weight: 500;
  color: #7C7C7C;
  font-size: 14px;
}
.kanban-task-title {
  font-size: 14px;
  font-weight: 500;
  color: #7C7C7C;
  flex: 1;
  line-height: 1.4;
}

/* Trạng thái task */
.kanban-task-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
  white-space: nowrap;
  align-self: flex-start;
  flex-shrink: 0;
}

.kanban-task-status.pending {
  background-color: #007BFF1A;
  color: #007BFF;
}

.kanban-task-status.in-progress {
  background-color: #007BFF1A;
  color: #007BFF;
}

.kanban-task-status.completed {
  background-color: #007BFF1A;
  color: #007BFF;
}

.kanban-task-status.prerequisites {
  background-color: #007BFF1A;
  color: #007BFF;
}

.kanban-task-status.undo {
  background-color: #007BFF1A;
  color: #007BFF;
}

.kanban-task-prerequisites {
  background: #EEEEEE1A !important;
}

.kanban-column-prerequisites {
  background: #EEEEEE1A !important;
  border: 1px solid #E4E4E4 !important;
}

.kanban-task-dependency-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}

.kanban-task-dependency-row:last-child {
  margin-bottom: 0;
}

.kanban-task-dependency-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.kanban-task-priority {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #7C7C7C;
  font-weight: 500;
}
.kanban-task-priority.cao {
  color: #ef4444;
}
.kanban-task-priority.trung {
  color: #f59e42;
}

.kanban-task-dates-value {
  color: #5D5D5D;
  font-size: 14px;
}

.kanban-task-dates-value.empty {
  color: #999;
}

.kanban-task-dates {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
}
.kanban-task-dates-row {
  display: flex;
  align-items: center;
}
.kanban-task-dates-label {
  min-width: 106px;
  color: #7C7C7C;
  font-size: 14px;
  font-weight: 500;
}
.kanban-task-dates-icon {
  width: 20px;
  height: 20px;
  margin: 0 8px;
  flex-shrink: 0;
}
.kanban-task-members {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8a8a8a;
}
.kanban-task-comments {
  font-size: 13px;
  color: #888;
  display: flex;
  align-items: center;
  gap: 4px;
}
.kanban-task-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Icon filters cho màu sắc */
.kanban-icon-prerequisites {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.kanban-icon-undo {
  filter: brightness(0) saturate(100%) invert(45%) sepia(15%) saturate(1000%) hue-rotate(180deg) brightness(95%) contrast(90%);
}
