@import url("../index.css");

.my-job-container {
  background-color: #f9f9f9;
  height: calc(100vh - 139px); /* Fixed height to prevent page scroll */
  overflow: hidden; /* Prevent container scroll */
  display: flex;
  flex-direction: column;
}
.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.job-icon {
  width: 20px;
  height: 20px;
  padding-bottom: 3px;
}

.page-header h1 {
  display: flex;
  align-items: center;
  font-size: 22px;
  color: #5D5D5D;
  margin: 0;
  font-weight: 600;
}

.job-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  justify-content: space-between;
}

.list-label {
  font-size: 16px;
  font-weight: 600;
  color: #5b5b5b;
}

.search-container {
  position: relative;
  width: 200px;
  display: flex;
  align-items: center;
}

.searc-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  opacity: 0.6;
  z-index: 2;
  pointer-events: none;
}

.job-search-input {
  padding: 8px 35px 8px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 16px !important;
  width: 100%;
  box-sizing: border-box;
  outline: none;
  position: relative;
  z-index: 1;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #1890ff;
  background-color: #fff;
}

.search-input::placeholder {
  color: #999;
}

/* Responsive design for search */
@media (max-width: 768px) {
  .search-container {
    width: 150px;
  }

  .job-actions {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .search-container {
    width: 100%;
    max-width: 250px;
  }
}

.job-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
  flex: 1; /* Take remaining space */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Hide scrollbar by default for Chrome, Safari and Opera */
.job-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.job-list::-webkit-scrollbar-track {
  background: transparent;
}

.job-list::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
}

/* Show scrollbar on hover for Chrome, Safari and Opera */
.job-list:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

/* For Firefox */
.job-list:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.job-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.job-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #ffffff;
}

.job-table th {
  background-color: #f9fafb;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #7c7c7c;
  border-bottom: 1px solid #d2cfcf;
  font-size: 14px;
}

.job-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  color: #7c7c7c;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.job-table th:nth-child(1),
.job-table td:nth-child(1) {
  width: 25%;
}

.job-table th:nth-child(2),
.job-table td:nth-child(2) {
  width: 25%;
  text-align: left;
}

.job-table th:nth-child(3),
.job-table td:nth-child(3) {
  width: 15%;
}

.job-table th:nth-child(4),
.job-table td:nth-child(4) {
  width: 10%;
}

.job-table th:nth-child(5),
.job-table td:nth-child(5) {
  width: 15%;
  text-align: center;
}

.job-table th:nth-child(6),
.job-table td:nth-child(6) {
  width: 15%;
  text-align: center;
}

.job-table tbody tr {
  cursor: pointer;
}

.job-table tbody tr:hover {
  background-color: #f9f9f9;
}

.status-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  width: 16px;
  height: 16px;
}

.status-badge {
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
}

.assignee-cell {
  text-align: center;
  vertical-align: middle;
  display: table-cell;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.no-assignee {
  font-size: 12px;
  color: #999;
  font-style: italic;
  cursor: help;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #666;
}

/* Layout styles */
.layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.job-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

/* Status styling */
.task-status-cell {
  position: relative;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
  border-radius: 4px;
  width: fit-content;
  cursor: pointer;
  transition: background-color 0.2s;
}

.task-status:hover {
  background-color: #f5f5f5;
}

.task-status.status-changing {
  animation: pulse 1s;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* Status-specific styling */
.task-status.pending .status-text {
  color: #7c7c7c;
}

/* .task-status.in-progress .status-text {
  color: #1890ff;
} */

/* .task-status.completed .status-text {
  color: #52c41a;
} */
/* 
.task-status.overdue .status-text {
  font-weight: 500;
} */

/* .task-status.review .status-text {
  color: #722ed1;
} */

/* Priority styling */
.task-priority-cell {
  position: relative;
}

.task-priority {
  display: flex;
  align-items: center;
  padding: 4px 8px 4px 0px;
  border-radius: 4px;
  width: fit-content;
  cursor: pointer;
  transition: background-color 0.2s;
}

.task-priority:hover {
  background-color: #f5f5f5;
}

.priority-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.priority-text {
  font-size: 14px;
}

/* Status popup */
.ui-elements-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 1000;
}

.status-popup {
  position: absolute;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 200px;
  overflow: hidden;
  z-index: 1000;
}

.status-popup-header {
  padding: 8px 12px;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #eee;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.status-option:hover,
.status-option.active {
  background-color: #f9fafb;
}

.status-option img {
  width: 16px;
  height: 16px;
}

/* Priority popup */
.priority-popup {
  position: absolute;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 200px;
  overflow: hidden;
  z-index: 1000;
}

.priority-popup-header {
  padding: 8px 12px;
  background-color: #f5f5f5;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #eee;
}

.priority-popup-options {
  max-height: 200px;
  overflow-y: auto;
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.priority-option:hover,
.priority-option.active {
  background-color: #f9fafb;
}

.priority-option img {
  width: 16px;
  height: 16px;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
