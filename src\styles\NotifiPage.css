@import url('../index.css');
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@100..900&display=swap');

.notification-page {
  min-height: 100vh;
  background: #FAFBFC;
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Header Section */
.notification-header {
  background: white;
  border-bottom: 1px solid #E5E7EB;
  padding: 24px 32px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.notification-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.notification-title h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1F2937;
  margin: 0;
}

.notification-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #F3F4F6;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #E5E7EB;
  border-color: #9CA3AF;
}

.more-actions {
  padding: 8px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.more-actions:hover {
  background: #F3F4F6;
  color: #374151;
}

/* Tabs Section */
.notification-tabs {
  display: flex;
  gap: 0;
  border-bottom: 1px solid #E5E7EB;
}

.tab-button {
  padding: 12px 24px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: #6B7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  color: #374151;
  background: #F9FAFB;
}

.tab-button.active {
  color: #3B82F6;
  border-bottom-color: #3B82F6;
  background: #F8FAFF;
}

/* Content Section */
.notification-content {
  padding: 24px 32px;
}

/* Filter Buttons for each tab */
.notification-filters {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.notification-filters button {
  padding: 6px 12px;
  background: #F3F4F6;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-filters button:hover {
  background: #E5E7EB;
  border-color: #9CA3AF;
}

.notification-filters button.active {
  background: #3B82F6;
  border-color: #3B82F6;
  color: white;
}

/* Notifications List */
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:hover {
  border-color: #D1D5DB;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.notification-item.unread {
  border-left: 4px solid #3B82F6;
  background: #F8FAFF;
}

.notification-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F3F4F6;
  border-radius: 10px;
  flex-shrink: 0;
}

.notification-avatar {
  position: relative;
  flex-shrink: 0;
}

.notification-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.type-icon {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 2px solid white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin: 0;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.priority-badge,
.category-badge {
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.notification-time {
  color: #6B7280;
  font-size: 12px;
  font-weight: 500;
}

.notification-message {
  color: #4B5563;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.comment-preview {
  background: #F9FAFB;
  border-left: 3px solid #E5E7EB;
  padding: 8px 12px;
  margin: 8px 0;
  border-radius: 0 6px 6px 0;
}

.comment-preview p {
  margin: 0;
  font-size: 13px;
  color: #6B7280;
  font-style: italic;
}

.notification-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.notification-details span,
.detail-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #6B7280;
  font-size: 12px;
  font-weight: 500;
}

.project-info {
  margin: 8px 0;
}

.project-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.project-name {
  font-weight: 600;
  color: #374151;
}

.project-code {
  color: #6B7280;
  font-size: 12px;
  background: #F3F4F6;
  padding: 2px 6px;
  border-radius: 4px;
}

.unread-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 8px;
  height: 8px;
  background: #3B82F6;
  border-radius: 50%;
}

.no-notifications {
  text-align: center;
  padding: 60px 20px;
  color: #6B7280;
}

.no-notifications p {
  font-size: 16px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-header {
    padding: 16px 20px;
  }
  
  .notification-title {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .notification-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tab-button {
    white-space: nowrap;
    min-width: fit-content;
  }
  
  .notification-content {
    padding: 16px 20px;
  }
  
  .notification-item {
    padding: 16px;
  }
  
  .notification-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .notification-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
