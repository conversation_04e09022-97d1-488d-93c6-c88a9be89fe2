import React, { useEffect, useState } from "react";
import { searchAll } from "../../../api/search";

const badgeStyle = (bg, color) => ({
  background: bg,
  color,
  borderRadius: 12,
  fontWeight: 500,
  fontSize: 15,
  padding: "4px 16px",
  display: "inline-block",
  minWidth: 70,
  textAlign: "center",
});

const statusBadgeStyle = {
  background: "#eafaf3",
  color: "#27ae60",
  borderRadius: 12,
  fontWeight: 500,
  fontSize: 15,
  padding: "4px 16px",
  display: "inline-block",
  minWidth: 70,
  textAlign: "center",
};

const cellValue = {
  color: "#888",
  fontSize: 14,
};

const statusToVietnamese = (status) => {
  switch (status) {
    case 'completed': return 'Hoàn thành';
    case 'in-progress':
    case 'in_progress': return 'Đang triển khai';
    case 'waiting':
    case 'pending': return 'Đang chờ';
    case 'overdue': return 'Qu<PERSON> hạn';
    case 'review':
    case 'consider': return '<PERSON>ang xem xét';
    case 'active': return 'Hoạt động';
    default: return status;
  }
};

const Member = ({ searchValue, onCountChange }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!searchValue) return;
    setLoading(true);
    searchAll(searchValue)
      .then((res) => {
        const group = (res.data?.groups || []).find(g => g.type === "user" || g.type === "member");
        setResults(group ? group.items : []);
      })
      .catch(() => setResults([]))
      .finally(() => setLoading(false));
  }, [searchValue]);

  useEffect(() => {
    if (onCountChange) onCountChange(results.length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [results]);

  if (loading) return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Đang tìm kiếm...</div>;
  if (!results.length) {
    return <div style={{ textAlign: "center", color: "#888", margin: 32 }}>Không tìm thấy thành viên cho "{searchValue}"</div>;
  }

  return (
    <div style={{ background: "#fff", padding: 0, borderRadius: 12 }}>
      {results.map((user, idx) => (
        <div
          key={user.id || idx}
          style={{
            border: "1px solid #f3f3f3",
            borderRadius: 12,
            marginBottom: 12,
            padding: "20px 24px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          {/* Avatar và thông tin */}
          <div style={{ display: "flex", alignItems: "center", minWidth: 320 }}>
            <img
              src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.fullName || user.name || 'U')}`}
              alt="avatar"
              style={{
                width: 48,
                height: 48,
                borderRadius: "50%",
                marginRight: 16,
                objectFit: "cover",
                border: "2px solid #fff",
                boxShadow: "0 0 0 1px #e0e0e0",
                background: "#fff",
              }}
            />
            <div>
              <div style={{ fontWeight: 600, fontSize: 16 }}>{user.fullName}</div>
              <div style={cellValue}>{user.email}</div>
              <div style={cellValue}>{user.employeeCode}</div>
            </div>
          </div>
          {/* Các thuộc tính còn lại dàn đều */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 100,
              flex: 1,
              justifyContent: "flex-end",
            }}
          >
            <span style={badgeStyle("#eaf3ff", "#4a90e2")}>{user.department || "Phòng ban"}</span>
            <span style={{ color: "#888" }}>{user.position || "Chức vụ"}</span>
            <span style={statusBadgeStyle}>{statusToVietnamese(user.status) || "Hoạt động"}</span>
            <span style={{ color: "#888" }}>{user.createdAt ? new Date(user.createdAt).toLocaleDateString() : ""}</span>
            <span style={badgeStyle("#eaf3ff", "#4a90e2")}>Thành viên</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Member;
