import React from "react";

const Restore = ({ open, onCancel, onConfirm, selectedLog }) => {
  if (!open) return null;
  
  // <PERSON><PERSON>c định loại đối tượng và tên hiển thị
  const getObjectTypeInfo = () => {
    if (!selectedLog) return { type: 'project', displayName: 'dự án' };
    
    const objectType = selectedLog.objectType?.toLowerCase();
    const objectTypeMap = {
      'project': 'dự án',
      'task': 'công việc',
      'user': 'người dùng',
      'note': 'ghi chú',
      'member': 'thành viên',
      'comment': 'bình luận'
    };
    
    return {
      type: objectType || 'project',
      displayName: objectTypeMap[objectType] || 'đối tượng'
    };
  };
  
  const { type, displayName } = getObjectTypeInfo();
  
  return (
    <div style={{
      position: "fixed",
      top: 0,
      left: 0,
      width: "100vw",
      height: "100vh",
      background: "rgba(0,0,0,0.15)",
      zIndex: 1000,
      display: "flex",
      alignItems: "center",
      justifyContent: "center"
    }}>
      <div style={{
        background: "#fff",
        borderRadius: 12,
        padding: "32px 32px 24px 32px",
        minWidth: 340,
        boxShadow: "0 2px 16px rgba(0,0,0,0.10)",
        maxWidth: "90vw"
      }}>
        <div style={{ fontWeight: 700, fontSize: 22, marginBottom: 12 }}>
          Khôi phục {displayName}
        </div>
        <div style={{ fontSize: 16, marginBottom: 28, color: "#444" }}>
          Bạn có chắc chắn muốn khôi phục {displayName} này không?
        </div>
        <div style={{ display: "flex", justifyContent: "flex-end", gap: 16 }}>
          <button
            onClick={onCancel}
            style={{
              background: "none",
              border: "none",
              color: "#444",
              fontSize: 16,
              fontWeight: 500,
              cursor: "pointer",
              padding: "8px 16px"
            }}
          >
            Hủy
          </button>
          <button
            onClick={onConfirm}
            style={{
              background: "#2196f3",
              color: "#fff",
              border: "none",
              borderRadius: 8,
              padding: "8px 24px",
              fontWeight: 500,
              fontSize: 16,
              cursor: "pointer"
            }}
          >
            Khôi phục
          </button>
        </div>
      </div>
    </div>
  );
};

export default Restore;
