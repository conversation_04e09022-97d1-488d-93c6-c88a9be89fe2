@import url('../index.css');
.detail-member-list {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* C<PERSON>i thiện scrollbar chung cho toàn bộ giao diện detail */
.detail-job-overlay *::-webkit-scrollbar {
  width: 5px;
}

.detail-job-overlay *::-webkit-scrollbar-track {
  background: transparent;
}

.detail-job-overlay *::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.detail-job-overlay *:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
}

.detail-job-overlay * {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.detail-job-container {
  position: fixed;
  top: 0; /* Đã sửa: bắt đầu từ đỉnh màn hình, che cả topbar */
  right: 0;
  height: 100vh; /* <PERSON><PERSON> sửa: chi<PERSON><PERSON> cao full màn hình */
  width: 420px;
  max-height: 100vh;
  background: #F6F6F6;
  box-shadow: -2px 0 16px 0 rgba(60,72,88,0.10);
  padding: 0;
  font-size: 15px;
  color: #222;
  z-index: 1000; /* Đã sửa: tăng z-index để hiển thị trên topbar */
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.25s cubic-bezier(.4,0,.2,1), opacity 0.18s;
  overflow: hidden;
}

.detail-job-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 20px 20px 20px;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.detail-job-content:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Webkit (Chrome, Safari, Edge) */
.detail-job-content::-webkit-scrollbar {
  width: 6px;
}

.detail-job-content::-webkit-scrollbar-track {
  background: transparent;
}

.detail-job-content::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.detail-job-content:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.detail-comment-footer {
  position: sticky;
  bottom: 0;
  border-top: 1px solid #eaeaea;
  padding: 12px 20px;
  width: 100%;
  z-index: 5;
  flex-shrink: 0;
}

.detail-job-container.show {
  transform: translateX(0);
  opacity: 1;
}

/* Header cố định */
.detail-job-header {
  position: sticky;
  top: 0;
  border-bottom: 1px solid #eaeaea;
  padding: 30px 20px 16px 20px; /* Tăng padding-top để tránh bị che bởi thanh trạng thái trên mobile */
  z-index: 10;
  flex-shrink: 0;
  background: #F6F6F6; /* Đảm bảo header có background giống container */
  border-radius: 14px 0 0 0; /* Bo góc trên bên trái */
}

.detail-job-header h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #222;
  padding-right: 50px; /* Để tránh chồng lên nút đóng */
}
.detail-row {
  display: flex;
  align-items: flex-start;
  margin: 16px 0; /* Tăng margin để tạo khoảng cách đồng đều */
  gap: 16px; /* Tăng gap để cân đối hơn */
  padding: 0; /* Đảm bảo không có padding thừa */
}

.detailjob-label {
  min-width: 120px; /* Tăng width để các label dài hơn không bị wrap */
  color: #5b5b5b;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
  line-height: 1.4; /* Đồng đều line-height */
  padding-top: 2px; /* Căn chỉnh với content */
}

.detailjob-value {
  flex: 1;
  color: #5b5b5b;
  font-size: 14px;
  display: block;
  line-height: 1.5;
  min-height: 20px;
  word-break: break-word;
  white-space: pre-line;
  max-width: 100%;
  overflow-wrap: anywhere;
  padding: 2px 0 2px 0;
  text-align: left;
}

/* Đảm bảo phần mô tả không bị tràn form */
.detail-row .detailjob-label + .detailjob-value {
  word-break: break-word;
  white-space: pre-line;
  max-width: 100%;
  overflow-wrap: anywhere;
  display: block;
  text-align: left;
  padding: 2px 0 2px 0;
}
.detail-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0; /* Không cho phép avatar bị co lại */
}
.detail-row input.detail-comment-input {
  width: 100%;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  margin-top: 4px;
  outline: none;
  transition: border 0.2s;
  border: none;
  background: transparent;
  height: 36px;
}
.detail-row .detailjob-value > div {
  width: 100%;
}

.detail-row .detailjob-value span[style*='color: #aaa'] {
  margin-left: 8px;
}

.detail-row .detailjob-value span[style*='font-weight: 500'] {
  color: #1a73e8;
}

/* Căn chỉnh icon trong detailjob-value */
.detailjob-value img {
  flex-shrink: 0;
  vertical-align: middle;
}

/* Đảm bảo text và icon căn chỉnh đều */
.detailjob-value > span {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* For attachment section specifically */
.attachment-section .detailjob-value {
  flex-direction: column;
  width: 100%;
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden; /* Bỏ scroll ngang */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.attachment-section .detailjob-value:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.attachment-section .detailjob-value::-webkit-scrollbar {
  width: 4px;
}

.attachment-section .detailjob-value::-webkit-scrollbar-track {
  background: transparent;
}

.attachment-section .detailjob-value::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.attachment-section .detailjob-value:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Attachment styles */
.detail-attachment-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  width: 100%;
  min-width: 0; /* Cho phép flex item co lại */
  cursor: pointer;
  transition: background-color 0.2s;
}

.detail-attachment-item:hover {
  background-color: #f5f5f5;
}

.detail-attachment-icon {
  margin-right: 8px;
  flex-shrink: 0; /* Không cho phép icon bị co lại */
}

.detail-attachment-name {
  flex: 1;
  min-width: 0; /* Quan trọng để ellipsis hoạt động trong flex */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detail-attachment-date {
  color: #aaa;
  margin-left: 8px;
  font-size: 12px;
  flex-shrink: 0; /* Không cho phép ngày tháng bị co lại */
  white-space: nowrap; /* Đảm bảo ngày tháng không wrap */
}

.detail-attachment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
  flex-shrink: 0; /* Không cho phép actions bị co lại */
  min-width: 60px; /* Đủ chỗ cho cả 2 nút */
  justify-content: flex-start;
}

.detail-attachment-download {
  color: #1a73e8;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
}

.detail-attachment-delete {
  color: #dc3545;
  opacity: 0;
  transition: opacity 0.2s;
  cursor: pointer;
}

.detail-attachment-delete:hover {
  color: #c82333;
}

.detail-attachment-item:hover .detail-attachment-download,
.detail-attachment-item:hover .detail-attachment-delete {
  opacity: 1;
}

@media (max-width: 600px) {
  .detail-job-container {
    width: 100vw;
    min-width: 0;
    border-radius: 0;
    left: 0;
  }
  
  .detail-job-header {
    border-radius: 0;
    padding-top: 40px; /* Tăng padding-top thêm cho mobile để tránh bị che bởi thanh trạng thái */
  }
  
  .detail-job-header-actions {
    top: 40px; /* Điều chỉnh vị trí các nút trên mobile */
  }
}

.detail-activity-list {
  margin-bottom: 18px;
  max-height: 240px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.detail-activity-list:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.detail-activity-list::-webkit-scrollbar {
  width: 4px;
}

.detail-activity-list::-webkit-scrollbar-track {
  background: transparent;
}

.detail-activity-list::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.detail-activity-list:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.detail-activity-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 8px;
  width: calc(100% - 15px);
  box-sizing: border-box;
}
.detail-activity-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  background: #f5f5f5;
  margin-top: 2px;
}
.detail-activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: calc(100% - 30px);
  overflow-wrap: break-word; 
  word-break: break-word; 
  box-sizing: border-box; 
  max-width: 310px; 
}
.detail-activity-name {
  font-weight: 600;
  color: #5D5D5D;
  font-size: 14px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.detail-activity-text {
  color: #5D5D5D;
  font-size: 14px;
  line-height: 1.5;
  max-width: 100%; 
  overflow-wrap: break-word; 
  word-break: break-word; 
}

.detail-activity-time {
  color: #9e9e9e;
  font-size: 12px;
  margin-top: 2px;
}

.detailjob-comment-box {
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 64px;
  min-height: 38px;
  height: 38px;
  box-shadow: none;
  width: 100%;
}
.detail-comment-actions {
  display: flex;
  align-items: center;
  gap: 2px;
}
.detail-comment-btn, .detail-comment-attach {
  background: none;
  color: #757575;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 18px;
  margin: 0;
}
.detail-comment-btn:hover, .detail-comment-attach:hover {
  background: #f1f3f4;
}

/* Tách biệt hoạt động và bình luận */
.detail-row-activity {
  flex-direction: column;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: -20px;
}

.detail-row-comment {
  margin: 16px 0; /* Đồng đều với các row khác */
}

/* Header actions container */
.detail-job-header-actions {
  position: absolute;
  top: 30px;
  right: 20px;
  display: flex;
  gap: 8px;
  z-index: 101;
}

.detail-job-close-btn,
.detail-job-edit-btn {
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.18s;
  padding: 0;
}

.detail-job-close-btn:hover,
.detail-job-edit-btn:hover {
  background: #f1f3f4;
}

/* .detail-job-edit-btn {
  background: #007bff;
  color: white;
}

.detail-job-edit-btn:hover {
  background: #0056b3;
} */

/* Edit form styles - sử dụng style giống JobCreate */
.detail-job-edit-form {
  padding: 20px;
  background: #f6f6f6;
  border-bottom: 1px solid #e0e0e0;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* Import JobCreate styles for edit form */
.detail-job-edit-form .job-create-field-wrapper {
  margin-bottom: 16px;
}

.detail-job-edit-form .job-panel-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.detail-job-edit-form .job-panel-rows {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.detail-job-edit-form .job-panel-label {
  width: 120px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-right: 16px;
  flex-shrink: 0;
}

.detail-job-edit-form .job-panel-value {
  flex: 1;
  display: flex;
  align-items: center;
}

.detail-job-edit-form .job-create-input,
.detail-job-edit-form .job-create-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.detail-job-edit-form .job-create-input:focus,
.detail-job-edit-form .job-create-textarea:focus {
  outline: none;
  border-color: #007bff;
}

.detail-job-edit-form .job-create-input.error,
.detail-job-edit-form .job-create-textarea.error {
  border-color: #dc3545;
}

.detail-job-edit-form .job-create-textarea {
  resize: vertical;
  min-height: 80px;
}

.detail-job-edit-form .job-create-field-error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 136px;
}

.detail-job-edit-form .job-panel-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.detail-job-edit-form .job-panel-cancel-btn,
.detail-job-edit-form .job-panel-create-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.detail-job-edit-form .job-panel-cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e0e0e0;
}

.detail-job-edit-form .job-panel-cancel-btn:hover {
  background: #e9ecef;
}

.detail-job-edit-form .job-panel-create-btn {
  background: #007bff;
  color: white;
}

.detail-job-edit-form .job-panel-create-btn:hover {
  background: #0056b3;
}

/* Members styles */
.detail-job-edit-form .job-panel-members-flex-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-job-edit-form .job-panel-add-member-btn {
  width: 32px;
  height: 32px;
  border: 2px dashed #ccc;
  border-radius: 50%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.detail-job-edit-form .job-panel-add-member-btn:hover {
  border-color: #007bff;
}

.detail-job-edit-form .job-panel-members-list {
  display: flex;
  gap: 4px;
}

.detail-job-edit-form .job-panel-member {
  position: relative;
  display: inline-block;
}

.detail-job-edit-form .job-panel-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.detail-job-edit-form .job-panel-remove-member-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background: #dc3545;
  color: white;
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* File upload styles */
.detail-job-edit-form .job-panel-file-upload-custom {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  margin-bottom: 8px;
}

.detail-job-edit-form .job-panel-file-upload-custom:hover {
  border-color: #007bff;
}

.detail-job-edit-form .job-panel-file-upload-icon {
  width: 16px;
  height: 16px;
}

.detail-job-edit-form .job-panel-file-upload-text {
  font-size: 14px;
  color: #666;
}

.detail-job-edit-form .job-panel-file-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-job-edit-form .job-panel-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.detail-job-edit-form .job-panel-file-icon {
  width: 16px;
  height: 16px;
}

.detail-job-edit-form .job-panel-file-name {
  flex: 1;
  font-size: 12px;
  color: #333;
}

.detail-job-edit-form .job-panel-remove-file-btn {
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background: #dc3545;
  color: white;
  font-size: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-progress-value {
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 20px; /* Đồng đều với các row khác */
}

.detail-progress-bar {
  width: 200px;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
  margin-left: 4px;
}
.detail-progress-bar-fill {
  height: 100%;
  background: #2563eb;
  border-radius: 4px;
  transition: width 0.3s;
}

.detail-export-btns {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 16px 0 16px 0;
  flex-wrap: wrap;
}
.detail-export-btn {
  border: none;
  border-radius: 6px;
  padding: 10px 24px;
  font-weight: 500;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #fff;
}
.detail-export-btn-pdf {
  background: #2563eb;
}
.detail-export-btn-excel {
  background: #0ea960;
}

.detail-row-activity .detailjob-value {
  width: 100%;
  display: block;
  box-sizing: border-box;
  position: relative;
}

/* Styles cho phần mô tả có thể mở rộng/thu gọn */
.description-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.description-text {
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.description-toggle-btn {
  align-self: flex-start;
  background: none;
  border: none;
  color: #0074b7;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  padding: 4px 0;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.description-toggle-btn:hover {
  color: #005a8b;
  text-decoration: none;
}

.description-toggle-btn:focus {
  outline: none;
  color: #005a8b;
}

