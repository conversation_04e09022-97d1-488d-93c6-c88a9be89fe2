/* Custom Sort Popup Styles */
.custom-sort-popup {
  width: 280px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  padding: 0;
  position: fixed !important;
  z-index: 999999 !important;
  display: flex;
  flex-direction: column;
  gap: 0;
  overflow: hidden;
  margin: 0;
  animation: fadeIn 0.2s ease-out;
  transform: none !important;
  pointer-events: auto;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(-10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.custom-sort-title {
  font-size: 17px;
  font-weight: 600;
  color: #444;
  margin-bottom: 0;
  padding: 16px 20px 12px 20px;
}

/* Header hiển thị trạng thái sắp xếp hiện tại */
.sort-status-header {
  background: #e6f3ff;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sort-status-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.sort-status-value {
  font-size: 14px;
  color: #1890ff;
  font-weight: 600;
}

.custom-sort-options {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0 12px 12px 12px;
}

.custom-sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  color: #222;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 8px;
  margin-bottom: 4px;
  min-height: 48px;
  user-select: none;
  border: 2px solid transparent;
}

.custom-sort-option:hover {
  background: #f5f7fa;
}

.custom-sort-option.selected {
  background: #f0f8ff;
  border: 2px solid #1890ff;
}

.sort-option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sort-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.sort-label {
  font-size: 16px;
  font-weight: 500;
}

/* iOS Switch for Sort Popup */
.ios-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  margin-left: 8px;
  cursor: pointer;
  z-index: 10;
  pointer-events: auto;
}

.ios-switch input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.ios-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  border-radius: 24px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  user-select: none;
  pointer-events: auto;
  z-index: 1;
}

.ios-switch .slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  pointer-events: none;
}

.ios-switch input:checked + .slider {
  background-color: #1890ff;
}

.ios-switch input:checked + .slider:before {
  transform: translateX(20px);
}

.ios-switch:hover .slider {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ios-switch:active .slider:before {
  transform: scale(0.95) translateX(0);
}

.ios-switch input:checked:active + .slider:before {
  transform: scale(0.95) translateX(20px);
}
