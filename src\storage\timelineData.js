import userAvatar from '../assets/user1.png';

const timelineData = [
  // Team phát triển
  { 
    id: 1, 
    name: 'Vẽ Diagram', 
    assigneeId: 4, 
    startDate: new Date(2025, 4, 2), 
    endDate: new Date(2025, 4, 3),
    dueDate: '03/05/2025',
    type: 'pink',
    status: 'in-progress',
    priority: 'high',
    progress: 60,
    assignee: [{ name: '<PERSON><PERSON><PERSON>', avatar: userAvatar }],
    creator: { name: '<PERSON><PERSON><PERSON>', avatar: userAvatar },
    description: 'Vẽ các diagram cần thiết cho dự án, bao gồm use case, class diagram và sequence diagram.',
    activities: [
      { user: 'Huỳnh', content: 'Đ<PERSON> hoàn thành use case diagram', time: 'Hôm qua' },
      { user: '<PERSON><PERSON><PERSON>', content: 'Cần bổ sung thêm sequence diagram', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'UseCase.pdf', date: 'Hôm qua', type: 'pdf' },
      { name: 'ClassDiagram.png', date: 'Hôm nay', type: 'png' }
    ],
    code: 'TASK-001'
  },
  { 
    id: 2, 
    name: '<PERSON><PERSON>m giao diện UX/UI', 
    assigneeId: 7, 
    startDate: new Date(2025, 4, 3), 
    endDate: new Date(2025, 4, 5),
    dueDate: '05/05/2025',
    type: 'pink',
    status: 'waiting',
    priority: 'medium',
    progress: 30,
    assignee: [{ name: 'Lê Tuấn', avatar: userAvatar }],
    creator: { name: 'Trí Thành', avatar: userAvatar },
    description: 'Thiết kế giao diện người dùng cho ứng dụng web, đảm bảo trải nghiệm người dùng tốt.',
    activities: [
      { user: 'Lê Tuấn', content: 'Đã hoàn thành wireframe', time: '2 ngày trước' }
    ],
    attachments: [
      { name: 'Wireframe.fig', date: '2 ngày trước', type: 'fig' }
    ],
    code: 'TASK-002'
  },
  { 
    id: 3, 
    name: 'Xây dựng Database', 
    assigneeId: 2, 
    startDate: new Date(2025, 4, 12), 
    endDate: new Date(2025, 4, 14),
    dueDate: '14/05/2025',
    type: 'yellow',
    status: 'consider',
    priority: 'high',
    progress: 0,
    assignee: [{ name: 'Tấn Sanh', avatar: userAvatar }],
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description: 'Thiết kế và xây dựng cơ sở dữ liệu cho hệ thống, bao gồm các bảng và mối quan hệ.',
    activities: [],
    attachments: [],
    code: 'TASK-003'
  },
  { 
    id: 4, 
    name: 'API Authentication', 
    assigneeId: 2, 
    startDate: new Date(2025, 4, 5), 
    endDate: new Date(2025, 4, 7),
    dueDate: '07/05/2025',
    type: 'blue',
    status: 'completed',
    priority: 'high',
    progress: 100,
    assignee: [{ name: 'Tấn Sanh', avatar: userAvatar }],
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description: 'Xây dựng hệ thống xác thực API sử dụng JWT và OAuth2.',
    activities: [
      { user: 'Tấn Sanh', content: 'Đã hoàn thành API xác thực', time: '3 ngày trước' },
      { user: 'Minh Tuấn', content: 'Đã kiểm tra và xác nhận hoạt động tốt', time: 'Hôm qua' }
    ],
    attachments: [
      { name: 'AuthDocs.md', date: '3 ngày trước', type: 'md' }
    ],
    code: 'TASK-004'
  },
  { 
    id: 5, 
    name: 'Backend Services', 
    assigneeId: 3, 
    startDate: new Date(2025, 4, 7), 
    endDate: new Date(2025, 4, 9),
    dueDate: '09/05/2025',
    type: 'yellow',
    status: 'in-progress',
    priority: 'medium',
    progress: 75,
    assignee: [{ name: 'Hoài Nam', avatar: userAvatar }],
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description: 'Phát triển các dịch vụ backend cho ứng dụng, bao gồm xử lý logic nghiệp vụ.',
    activities: [
      { user: 'Hoài Nam', content: 'Đã hoàn thành 75% services', time: 'Hôm nay' }
    ],
    attachments: [],
    code: 'TASK-005'
  },
  { 
    id: 6, 
    name: 'REST API Endpoints', 
    assigneeId: 3, 
    startDate: new Date(2025, 4, 11), 
    endDate: new Date(2025, 4, 13),
    dueDate: '13/05/2025',
    type: 'blue',
    status: 'waiting',
    priority: 'normal',
    progress: 0,
    assignee: [{ name: 'Hoài Nam', avatar: userAvatar }],
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description: 'Xây dựng các REST API endpoints cho frontend sử dụng.',
    activities: [],
    attachments: [],
    code: 'TASK-006'
  },
  { 
    id: 7, 
    name: 'Làm giao diện trang chủ', 
    assigneeId: 5, 
    startDate: new Date(2025, 4, 9), 
    endDate: new Date(2025, 4, 10),
    dueDate: '10/05/2025',
    type: 'yellow',
    status: 'in-progress',
    priority: 'high',
    progress: 40,
    assignee: [{ name: 'Nhân', avatar: userAvatar }],
    creator: { name: 'Trí Thành', avatar: userAvatar },
    description: 'Phát triển giao diện trang chủ theo thiết kế đã được phê duyệt.',
    activities: [
      { user: 'Nhân', content: 'Đã hoàn thành header và footer', time: 'Hôm qua' }
    ],
    attachments: [],
    code: 'TASK-007'
  },
  { 
    id: 8, 
    name: 'Fix bug UI Dashboard', 
    assigneeId: 5, 
    startDate: new Date(2025, 4, 15), 
    endDate: new Date(2025, 4, 16),
    dueDate: '16/05/2025',
    type: 'blue',
    status: 'waiting',
    priority: 'normal',
    progress: 0,
    assignee: [{ name: 'Nhân', avatar: userAvatar }],
    creator: { name: 'Trí Thành', avatar: userAvatar },
    description: 'Sửa các lỗi UI trên trang Dashboard.',
    activities: [],
    attachments: [],
    code: 'TASK-008'
  },
  { 
    id: 9, 
    name: 'Integration Testing', 
    assigneeId: 4, 
    startDate: new Date(2025, 4, 13), 
    endDate: new Date(2025, 4, 14),
    dueDate: '14/05/2025',
    type: 'blue',
    status: 'waiting',
    priority: 'medium',
    progress: 0,
    assignee: [{ name: 'Huỳnh', avatar: userAvatar }],
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description: 'Thực hiện kiểm thử tích hợp giữa frontend và backend.',
    activities: [],
    attachments: [],
    code: 'TASK-009'
  },
  { 
    id: 10, 
    name: 'Requirement Analysis', 
    assigneeId: 6, 
    startDate: new Date(2025, 4, 1), 
    endDate: new Date(2025, 4, 3),
    dueDate: '03/05/2025',
    type: 'yellow',
    status: 'completed',
    priority: 'high',
    progress: 100,
    assignee: [{ name: 'Nguyễn Bảo', avatar: userAvatar }],
    creator: { name: 'Trí Thành', avatar: userAvatar },
    description: 'Phân tích yêu cầu dự án từ khách hàng.',
    activities: [
      { user: 'Nguyễn Bảo', content: 'Đã hoàn thành phân tích yêu cầu', time: '5 ngày trước' },
      { user: 'Trí Thành', content: 'Đã xác nhận các yêu cầu', time: '4 ngày trước' }
    ],
    attachments: [
      { name: 'Requirements.docx', date: '5 ngày trước', type: 'docx' }
    ],
    code: 'TASK-010'
  },
  { 
    id: 11, 
    name: 'User Flows', 
    assigneeId: 6, 
    startDate: new Date(2025, 4, 6), 
    endDate: new Date(2025, 4, 8),
    dueDate: '08/05/2025',
    type: 'pink',
    status: 'in-progress',
    priority: 'medium',
    progress: 50,
    assignee: [{ name: 'Nguyễn Bảo', avatar: userAvatar }],
    creator: { name: 'Trí Thành', avatar: userAvatar },
    description: 'Thiết kế các luồng người dùng cho ứng dụng.',
    activities: [
      { user: 'Nguyễn Bảo', content: 'Đã hoàn thành 50% user flows', time: 'Hôm qua' }
    ],
    attachments: [],
    code: 'TASK-011'
  },

  // Team điều hành
  { 
    id: 12, 
    name: 'Design System', 
    assigneeId: 9, 
    startDate: new Date(2025, 4, 4), 
    endDate: new Date(2025, 4, 6),
    dueDate: '06/05/2025',
    type: 'pink',
    status: 'completed',
    priority: 'high',
    progress: 100,
    assignee: [{ name: 'Hữu Trung', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Xây dựng hệ thống thiết kế cho dự án.',
    activities: [
      { user: 'Hữu Trung', content: 'Đã hoàn thành design system', time: '2 ngày trước' },
      { user: 'Anh Tuấn', content: 'Đã xác nhận và áp dụng', time: 'Hôm qua' }
    ],
    attachments: [
      { name: 'DesignSystem.sketch', date: '2 ngày trước', type: 'sketch' }
    ],
    code: 'TASK-012'
  },
  { 
    id: 13, 
    name: 'UI Components', 
    assigneeId: 9, 
    startDate: new Date(2025, 4, 10), 
    endDate: new Date(2025, 4, 12),
    dueDate: '12/05/2025',
    type: 'yellow',
    status: 'in-progress',
    priority: 'medium',
    progress: 70,
    assignee: [{ name: 'Hữu Trung', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Thiết kế các thành phần UI cho dự án.',
    activities: [
      { user: 'Hữu Trung', content: 'Đã hoàn thành 70% UI components', time: 'Hôm nay' }
    ],
    attachments: [],
    code: 'TASK-013'
  },
  { 
    id: 14, 
    name: 'Product Roadmap', 
    assigneeId: 10, 
    startDate: new Date(2025, 4, 2), 
    endDate: new Date(2025, 4, 4),
    dueDate: '04/05/2025',
    type: 'blue',
    status: 'completed',
    priority: 'high',
    progress: 100,
    assignee: [{ name: 'Trí Thành', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Xây dựng lộ trình phát triển sản phẩm.',
    activities: [
      { user: 'Trí Thành', content: 'Đã hoàn thành roadmap', time: '3 ngày trước' },
      { user: 'Anh Tuấn', content: 'Đã xác nhận và chia sẻ với team', time: '2 ngày trước' }
    ],
    attachments: [
      { name: 'Roadmap.pdf', date: '3 ngày trước', type: 'pdf' }
    ],
    code: 'TASK-014'
  },
  { 
    id: 15, 
    name: 'Stakeholder Meeting', 
    assigneeId: 10, 
    startDate: new Date(2025, 4, 8), 
    endDate: new Date(2025, 4, 9),
    dueDate: '09/05/2025',
    type: 'yellow',
    status: 'completed',
    priority: 'high',
    progress: 100,
    assignee: [{ name: 'Trí Thành', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Tổ chức cuộc họp với các bên liên quan.',
    activities: [
      { user: 'Trí Thành', content: 'Đã tổ chức cuộc họp thành công', time: 'Hôm qua' }
    ],
    attachments: [
      { name: 'MeetingNotes.docx', date: 'Hôm qua', type: 'docx' }
    ],
    code: 'TASK-015'
  },
  { 
    id: 16, 
    name: 'User Research', 
    assigneeId: 11, 
    startDate: new Date(2025, 4, 5), 
    endDate: new Date(2025, 4, 7),
    dueDate: '07/05/2025',
    type: 'pink',
    status: 'completed',
    priority: 'medium',
    progress: 100,
    assignee: [{ name: 'Nguyễn Hoàng', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Thực hiện nghiên cứu người dùng để hiểu nhu cầu.',
    activities: [
      { user: 'Nguyễn Hoàng', content: 'Đã hoàn thành nghiên cứu', time: '2 ngày trước' },
      { user: 'Anh Tuấn', content: 'Đã xem xét kết quả', time: 'Hôm qua' }
    ],
    attachments: [
      { name: 'UserResearch.pdf', date: '2 ngày trước', type: 'pdf' }
    ],
    code: 'TASK-016'
  },
  { 
    id: 17, 
    name: 'Prototyping', 
    assigneeId: 11, 
    startDate: new Date(2025, 4, 12), 
    endDate: new Date(2025, 4, 14),
    dueDate: '14/05/2025',
    type: 'blue',
    status: 'in-progress',
    priority: 'high',
    progress: 30,
    assignee: [{ name: 'Nguyễn Hoàng', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Tạo prototype cho ứng dụng.',
    activities: [
      { user: 'Nguyễn Hoàng', content: 'Đã hoàn thành 30% prototype', time: 'Hôm nay' }
    ],
    attachments: [],
    code: 'TASK-017'
  },
  { 
    id: 18, 
    name: 'Component Library', 
    assigneeId: 12, 
    startDate: new Date(2025, 4, 7), 
    endDate: new Date(2025, 4, 9),
    dueDate: '09/05/2025',
    type: 'yellow',
    status: 'in-progress',
    priority: 'medium',
    progress: 80,
    assignee: [{ name: 'Phạm Hương', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Xây dựng thư viện component cho frontend.',
    activities: [
      { user: 'Phạm Hương', content: 'Đã hoàn thành 80% component library', time: 'Hôm qua' }
    ],
    attachments: [],
    code: 'TASK-018'
  },
  { 
    id: 19, 
    name: 'Frontend Testing', 
    assigneeId: 12, 
    startDate: new Date(2025, 4, 14), 
    endDate: new Date(2025, 4, 16),
    dueDate: '16/05/2025',
    type: 'blue',
    status: 'waiting',
    priority: 'normal',
    progress: 0,
    assignee: [{ name: 'Phạm Hương', avatar: userAvatar }],
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description: 'Thực hiện kiểm thử frontend.',
    activities: [],
    attachments: [],
    code: 'TASK-019'
  }
];

export default timelineData; 