@import url('../index.css');
.action-trash {
  position: absolute;
  top: 32px;
  right: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  padding: 16px 20px 10px 20px;
  min-width: 120px;
  z-index: 10;
  animation: fadeIn 0.15s;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
.action-trash-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #5b5b5b;
  text-align: center;
}
.action-trash-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  color: #444;
  font-size: 15px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s;
}
.action-trash-btn.delete {
  color: #e74c3c;
  font-weight: 500;
}
.action-trash-icon {
  width: 16px;
  height: 16px;
  display: block;
}
.action-trash-btn.delete .action-trash-icon {
  filter: brightness(0) saturate(100%) invert(36%) sepia(97%) saturate(749%) hue-rotate(338deg) brightness(97%) contrast(92%);
}
