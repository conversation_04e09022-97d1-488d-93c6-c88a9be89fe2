@import url('../index.css');
/* WorkLayout Styles */
.layout {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  overflow: hidden; /* <PERSON><PERSON><PERSON> không cho scroll ở cấp layout */
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* <PERSON><PERSON><PERSON> không cho scroll ở main-content */
  margin-left: 250px;
  width: calc(100% - 250px);
}

.content-container {
  flex: 1;
  overflow: visible; /* Cho phép popup hiển thị */
  display: flex;
  flex-direction: column;
  min-height: 0; /* Cho phép flex children shrink */
}

.work-navigation {
  margin-bottom: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
  flex-shrink: 0; /* Không cho navigation co lại */
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* <PERSON>ăn không cho header co lại */
  background-color: #ffffff;
  padding: 10px 16px;
  border-radius: 8px 8px 0 0;
}

.work-title {
  display: flex;
  align-items: center;
}

.work-header h1 {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  color: #5B5B5B;
}

.task-count {
  background-color: #007BFF;
  color: #FFFFFF;
  border-radius: 4px;
  padding: 2px;
  font-size: 12px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 22px;
}

.work-tabs {
  display: flex;
  flex-direction: column;
  flex-shrink: 0; /* Ngăn không cho tabs co lại */
  background-color: #ffffff;
  border-radius: 0 0 8px 8px;
}

.main-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 16px;
  border-bottom: 1px solid #e0e0e0;
  min-height: 48px;
  position: relative;
}

.main-tabs-group {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.create-task-container {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.btn-create-task {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: 25px !important;
  color: #ffffff !important;
  background-color: #007BFF !important;
  border: none;
  cursor: pointer;
}

.btn-create-task:hover {
  background-color: #0c69cb !important;
  color: #ffffff !important;
}

.btn-create-task img {
  width: 18px;
  height: 18px;
}

/* Đảm bảo responsive */
@media (max-width: 768px) {
  .main-tabs {
    flex-direction: column;
    align-items: flex-start;
    padding-bottom: 60px;
    position: relative;
  }
  
  .create-task-container {
    position: absolute;
    left: 16px;
    right: 16px;
    top: auto;
    bottom: 10px;
    transform: none;
    width: calc(100% - 32px);
  }
  
  .btn-create-task {
    width: 100%;
    justify-content: center;
  }
}

.work-tab {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #5B5B5B;
  text-decoration: none;
  position: relative;
  margin-right: 16px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.work-tab:hover {
  color: #333;
  background-color: #007BFF1A;
}

.work-tab.active-tab {
  color: #333;
  font-weight: 500;
  background-color: #007BFF1A;
}

.tabs-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 16px;
  background-color: #ffffff;
  position: relative;
  overflow: visible;
}

.sub-tabs {
  display: flex;
  align-items: center;
}

.sub-tab {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #5B5B5B;
  text-decoration: none;
  position: relative;
  margin-right: 16px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.sub-tab:hover {
  color: #333;
  background-color: #007BFF1A;
}

.sub-tab.active-sub-tab {
  color: #333;
  font-weight: 500;
  background-color: #007BFF1A;
}

.view-options {
  display: flex;
  gap: 10px;
  margin-right: 16px;
  position: relative;
  z-index: 1;
}

.btn-sort, .btn-filters {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: 1px solid #ffffff;
  border-radius: 4px;
  font-size: 14px;
  color: #5B5B5B;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 8px;
}

.btn-sort:hover, .btn-filters:hover {
  background-color: #f5f5f5;
}

.option-icon {
  width: 16px;
  height: 16px;
}

/* Remove the old icon classes */
.icon-sort, .icon-filter {
  display: none;
}

.work-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow: auto; /* Chỉ cho phép scroll ở content */
  min-height: 600px; /* Cần thiết để flex child có thể scroll */
  display: flex;
  flex-direction: column;
}

/* Đảm bảo các component con có thể scroll đúng cách */
.work-content > * {
  flex: 1;
  min-height: 0;
  overflow: auto;
}

/* Override cho MyJob component trong WorkLayout */
.work-content .job-list {
  max-height: none !important; /* Loại bỏ max-height cố định */
  height: 100% !important;
  flex: 1;
}

/* Override cho ListProject component trong WorkLayout */
.work-content .task-view {
  height: 100% !important;
  padding-bottom: 20px !important; /* Giảm padding-bottom */
  flex: 1;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden; /* Ngăn không cho task-view scroll */
}

/* Đảm bảo tasks-table-container có thể scroll trong WorkLayout */
.work-content .tasks-table-container {
  flex: 1 !important;
  overflow: auto !important;
  min-height: 600px !important;
  max-height: calc(100vh - 300px) !important;
}

/* Override cho các container khác có thể có vấn đề tương tự */
.work-content .documents-table-container,
.work-content .members-table-container {
  max-height: none !important;
  height: 100% !important;
  flex: 1;
}

/* Responsive styles */
@media (max-width: 768px) {
  .main-content {
    margin-left: 60px; /* Bằng chiều rộng của sidebar trên mobile */
    width: calc(100% - 60px);
  }
}

/* Filter Popup Styles */
/* .sort-container,
.filter-container {
  position: relative;
} */

/* Đảm bảo sort container có đủ không gian cho popup */
/* .sort-container {
  display: inline-block;
} */

/* .filter-popup:not(.filter-popup-custom) {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1000;
  transform: translate(-50%, -50%);
  box-shadow: 0 4px 32px rgba(0,0,0,0.15);
  background: #fff;
  border-radius: 16px;
  min-width: 400px;
  max-width: 90vw;
  padding: 32px 24px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  animation: fadeIn 0.15s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translate(-50%, -40%); }
  to { opacity: 1; transform: translate(-50%, -50%); }
}

@keyframes fadeInBackdrop {
  from { opacity: 0; }
  to { opacity: 1; }
}

.filter-popup-header:not(.filter-popup-header-custom) {
  padding: 12px 16px;
  font-weight: 500;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.filter-options {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.filter-group {
  padding: 8px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.filter-group:last-child {
  border-bottom: none;
}

.filter-group-title {
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 8px;
  font-size: 14px;
}

.filter-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.filter-option input[type="checkbox"] {
  margin-right: 8px;
}

.filter-option label {
  font-size: 14px;
  color: #4b5563;
  cursor: pointer;
}

.filter-date-group {
  margin-bottom: 12px;
}

.filter-date-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
}

.filter-date-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-date {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
}

.filter-date-separator {
  font-size: 13px;
  color: #6b7280;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  gap: 8px;
}
.btn-apply-filter:hover {
  background-color: #2563eb;
} */


/* ---------------------- Lọc ---------------------- */
/* Filter Popup Backdrop */
.filter-popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  z-index: 999998;
  animation: fadeInBackdrop 0.2s ease-out;
  pointer-events: auto;
  /* Đảm bảo backdrop không ảnh hưởng đến scroll */
  touch-action: none;
}

/* Filter Popup Custom Styles */
.filter-popup-custom {
  min-width: 420px;
  max-width: 90vw;
  padding: 0;
  position: fixed !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999999 !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15);
  animation: fadeInUp 0.2s ease-out;
}
.filter-popup-header-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 0 24px;
}
.filter-popup-title {
  font-weight: 600;
  font-size: 18px;
  color: #333;
  text-align: left;
}
.filter-popup-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.filter-popup-close-btn:hover {
  background-color: #f5f5f5;
}
.filter-popup-content {
  padding: 20px 24px 0 24px;
}
.filter-status-group {
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 16px;
  color: #333;
}
.filter-status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}
.filter-status-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: 15px;
  position: relative;
  padding: 8px 0;
  color: #333;
}
.custom-round-checkbox {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: inline-block;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid #e0e0e0;
  background: #fff;
   border: 2px solid #007BFF
}
.custom-round-checkbox .custom-round-inner {
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #3b82f6;
}

.filter-date-group-custom {
  font-weight: 500;
  font-size: 15px;
  margin: 24px 0 12px 0;
  color: #333;
}
.filter-date-row-custom {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}
.filter-action-row-custom {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0 24px 24px 24px;
  margin-top: 24px;
}
.filter-btn-cancel {
  min-width: 80px;
  padding: 12px 24px;
  border: 1px solid #FFFFFF;
  background: #fff;
  color: #666;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 400;
  font-size: 15px;
  transition: all 0.2s ease;
}
.filter-btn-cancel:hover {
  border-color: #FFFFFF;
  background: #f8f9fa;
}
.filter-btn-apply {
  min-width: 160px;
  padding: 12px 24px;
  border: none;
  background: #007bff;
  color: #fff;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.2s ease;
}
.filter-btn-apply:hover {
  background: #0056b3;
}

/* Custom Date Input Group for Filter Popup */
.filter-date-input-group {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1.5px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 12px;
  height: 40px;
  min-width: 180px;
  color: #666;
  font-size: 15px;
  position: relative;
  gap: 8px;
  transition: border-color 0.2s ease;
}
.filter-date-input-group:hover {
  border-color: #bdbdbd;
}
.filter-date-input-group:focus-within {
  border-color: #007bff;
}
.filter-date-input-group .react-datepicker-wrapper {
  width: 100%;
}
.filter-date-input-group .react-datepicker__input-container {
  width: 100%;
}
.filter-date-input-group input {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 15px;
  width: 100%;
  cursor: pointer;
}

.filter-date-input-group input[type="date"] {
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-size: 15px;
  width: 100%;
  cursor: pointer;
  font-family: inherit;
}

.filter-date-input-group input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  cursor: pointer;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
}
.filter-date-input-group input::placeholder {
  color: #bdbdbd;
}
.filter-date-input-group .calendar-icon {
  width: 18px;
  height: 18px;
  opacity: 0.6;
  color: #666;
}
