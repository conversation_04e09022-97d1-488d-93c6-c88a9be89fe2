// Cache và preload để tăng tốc độ loading
let jobTrashCache = null;
let jobCacheTimestamp = 0;
const JOB_CACHE_DURATION = 10000; // 10 giây
let jobPreloadPromise = null;

import ActionTrash from "../../components/TrashAction";
import TrashActionBar from "../../components/TrashActionBar";
import React, { useState, useEffect } from "react";
import "../../../styles/TrashList.css";
import "../../../styles/Trash.css";
import SquareCheckIcon from "../../../assets/square-check-big.svg";
import {
  getDeletedProjectTasks,
  restoreProjectTask,
  permanentDeleteProjectTask,
} from "../../../api/taskManagement";
import {
  getDeletedPersonalTasks,
  restorePersonalTask,
  permanentDeletePersonalTask,
} from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import { showSuccess, showError } from "../../../utils/toastUtils";

const statusColor = (status) => {
  if (status === "Hoàn thành") return { color: "#27ae60" };
  if (status === "Đang chờ") return { color: "#e67e22" };
  return {};
};

const Job = ({ onDataChange }) => {
  const [jobData, setJobData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionIdx, setActionIdx] = useState(null);
  const [selectedIds, setSelectedIds] = useState([]);
  const [lastStats, setLastStats] = useState(null);

  // Gửi thống kê lên cha
  const sendStatsToParent = (jobCount, selectedCount) => {
    if (onDataChange) {
      const stats = {
        totalItems: jobCount,
        projectCount: 0,
        jobCount: jobCount,
        userCount: 0,
        selectedCount: selectedCount,
      };
      if (JSON.stringify(stats) !== JSON.stringify(lastStats)) {
        setLastStats(stats);
        onDataChange(stats);
      }
    }
  };

  // Preload function
  const preloadJobData = async () => {
    if (jobPreloadPromise) return jobPreloadPromise;

    jobPreloadPromise = (async () => {
      try {
        // Lấy tất cả projectId
        let allProjectIds = [];
        try {
          const projectsRes = await getAllProjects();
          const projects = projectsRes.data || projectsRes || [];
          allProjectIds = projects.map((p) => p._id || p.id);
        } catch (err) {
          allProjectIds = [];
        }
        let allTasks = [];
        if (allProjectIds.length > 0) {
          // Lấy task đã xóa cho từng project
          for (const projectId of allProjectIds) {
            try {
              const res = await getDeletedProjectTasks(projectId);
              const data = res.data || res || [];
              // Thêm projectId vào mỗi task
              const tasksWithProjectId = data.map((task) => ({
                ...task,
                projectId: projectId,
              }));
              allTasks = allTasks.concat(tasksWithProjectId);
            } catch (err) {
              // Bỏ qua lỗi từng project
            }
          }
        } else {
          // Nếu không có project nào, lấy task cá nhân đã xóa
          try {
            const res = await getDeletedPersonalTasks();
            const data = res.data || res || [];
            allTasks = allTasks.concat(data);
          } catch (err) {}
        }
        // Lọc theo quyền
        const userRaw = JSON.parse(localStorage.getItem("user") || "{}");
        const role =
          userRaw.user?.role?.toLowerCase() ||
          userRaw.role?.toLowerCase() ||
          "staff";
        const userName =
          userRaw.user?.fullName || userRaw.fullName || userRaw.name;
        let filteredTasks = allTasks;
        if (role !== "admin" && role !== "ceo") {
          filteredTasks = (allTasks || []).filter(
            (item) => item.deletedBy === userName
          );
        }

        jobTrashCache = filteredTasks;
        jobCacheTimestamp = Date.now();

        return filteredTasks;
      } catch (err) {
        console.error("Error in preloadJobData:", err);
        return [];
      }
    })();

    return jobPreloadPromise;
  };

  useEffect(() => {
    const fetchJobs = async () => {
      // Kiểm tra cache trước
      const now = Date.now();
      if (jobTrashCache && now - jobCacheTimestamp < JOB_CACHE_DURATION) {
        setJobData(jobTrashCache);
        sendStatsToParent(jobTrashCache.length, selectedIds.length);
        setLoading(false);
        return;
      }

      // Nếu không có cache, mới set loading=true
      setLoading(true);
      setError(null);

      try {
        let filteredTasks;
        if (jobPreloadPromise) {
          filteredTasks = await jobPreloadPromise;
        } else {
          filteredTasks = await preloadJobData();
        }

        setJobData(filteredTasks);
        sendStatsToParent(filteredTasks.length, selectedIds.length);
      } catch (err) {
        setError("Lỗi khi tải danh sách công việc đã xóa");
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, []);

  // Start preloading when component mounts
  useEffect(() => {
    preloadJobData();
  }, []);

  // Gửi lại stats khi chọn checkbox
  useEffect(() => {
    sendStatsToParent(jobData.length, selectedIds.length);
  }, [jobData.length, selectedIds.length]);

  const handleRestoreItem = async (id) => {
    try {
      const task = jobData.find((item) => (item._id || item.id) === id);
      if (task && task.projectId) {
        await restoreProjectTask(task.projectId, id);
      } else {
        await restorePersonalTask(id);
      }
      setJobData((prev) => prev.filter((item) => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Khôi phục công việc thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục công việc: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleDeleteItem = async (id) => {
    try {
      const task = jobData.find((item) => (item._id || item.id) === id);
      if (task && task.projectId) {
        await permanentDeleteProjectTask(task.projectId, id);
      } else {
        await permanentDeletePersonalTask(id);
      }
      setJobData((prev) => prev.filter((item) => (item._id || item.id) !== id));
      setActionIdx(null);
      showSuccess("Xóa vĩnh viễn công việc thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn công việc: " + (err.message || err));
      setActionIdx(null);
    }
  };
  const handleSelect = (id) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((sid) => sid !== id) : [...prev, id]
    );
  };
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedIds(jobData.map((item) => item._id || item.id));
    } else {
      setSelectedIds([]);
    }
  };
  const handleClearSelection = () => setSelectedIds([]);
  const handleRestoreSelected = async () => {
    const itemsToRestore = selectedIds.map(id => jobData.find(i => (i._id || i.id) === id)).filter(Boolean);
    const promises = itemsToRestore.map(item => {
      if (item.projectId) return restoreProjectTask(item.projectId, item._id || item.id);
      return restorePersonalTask(item._id || item.id);
    });

    try {
      await Promise.all(promises);
      setJobData(prev => prev.filter(item => !selectedIds.includes(item._id || item.id)));
      setSelectedIds([]);
      showSuccess("Khôi phục hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi khôi phục hàng loạt: " + (err.message || err));
    }
  };
  const handleDeleteSelected = async () => {
    const itemsToDelete = selectedIds.map(id => jobData.find(i => (i._id || i.id) === id)).filter(Boolean);
    const promises = itemsToDelete.map(item => {
      if (item.projectId) return permanentDeleteProjectTask(item.projectId, item._id || item.id);
      return permanentDeletePersonalTask(item._id || item.id);
    });

    try {
      await Promise.all(promises);
      setJobData(prev => prev.filter(item => !selectedIds.includes(item._id || item.id)));
      setSelectedIds([]);
      showSuccess("Xóa vĩnh viễn hàng loạt thành công!");
    } catch (err) {
      showError("Lỗi khi xóa vĩnh viễn hàng loạt: " + (err.message || err));
    }
  };

  // Skeleton loading component
  const JobSkeleton = () => (
    <div className="trash-skeleton-container">
      {[1, 2, 3, 4, 5, 6].map((idx) => (
        <div key={idx} className="trash-skeleton-item">
          <div className="trash-skeleton-checkbox"></div>
          <div className="trash-skeleton-main">
            <div className="trash-skeleton-title-row">
              <div className="trash-skeleton-icon"></div>
              <div className="trash-skeleton-title"></div>
              <div className="trash-skeleton-type"></div>
            </div>
            <div className="trash-skeleton-info">
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
              <div className="trash-skeleton-text"></div>
            </div>
            <div className="trash-skeleton-members">
              <div className="trash-skeleton-member"></div>
              <div className="trash-skeleton-member"></div>
            </div>
          </div>
          <div className="trash-skeleton-actions">
            <div className="trash-skeleton-action-type"></div>
            <div className="trash-skeleton-more-btn"></div>
          </div>
        </div>
      ))}
    </div>
  );

  if (loading && jobData.length === 0) {
    return (
      <div className="trash-list-container">
        <div className="trash-list-header">
          <div className="trash-list-header-left">
            <input type="checkbox" disabled style={{ opacity: 0.5 }} />
            <span style={{ color: "#666" }}>Đang tải công việc...</span>
          </div>
          <div className="trash-list-header-right" style={{ color: "#666" }}>
            Đang tải...
          </div>
        </div>
        <JobSkeleton />
      </div>
    );
  }

  if (error)
    return (
      <div
        style={{
          color: "red",
          textAlign: "center",
          padding: "40px",
          background: "#fff",
          borderRadius: "8px",
          margin: "20px 0",
        }}
      >
        <div>❌ {error}</div>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: "10px",
            padding: "8px 16px",
            background: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Thử lại
        </button>
      </div>
    );

  return (
    <div className="trash-list-container">
      {selectedIds.length > 0 && (
        <TrashActionBar
          selectedCount={selectedIds.length}
          onRestore={handleRestoreSelected}
          onDelete={handleDeleteSelected}
          onClearSelection={handleClearSelection}
        />
      )}
      <div className="trash-list-header">
        <div className="trash-list-header-left">
          <input
            type="checkbox"
            checked={selectedIds.length === jobData.length}
            onChange={handleSelectAll}
          />
          <span>Chọn tất cả ({jobData.length} mục)</span>
        </div>
        <div className="trash-list-header-right">
          Hiển thị {jobData.length}/{jobData.length} mục
        </div>
      </div>
      <div className="trash-list-content">
        {jobData.map((item, idx) => (
          <div className="trash-list-item" key={item._id || item.id}>
            <input
              type="checkbox"
              className="trash-list-checkbox"
              checked={selectedIds.includes(item._id || item.id)}
              onChange={() => handleSelect(item._id || item.id)}
            />
            <div className="trash-list-main">
              <div className="trash-list-title">
                <span className="trash-list-icon">
                  <img src={SquareCheckIcon} alt={item.type || 'Công việc'} />
                </span>
                <span>{item.title || item.name}</span>
                <span className="trash-list-type">Công việc</span>
              </div>
              <div className="trash-list-info">
                {item.code && (
                  <div>
                    Mã dự án: <b>{item.code}</b>
                  </div>
                )}
                {item.desc && <div>{item.desc}</div>}
              </div>
              <div className="trash-list-info trash-list-info-2">
                {item.deletedDate && (
                  <div>
                    Ngày xóa <b>{item.deletedDate}</b>
                  </div>
                )}
                {item.deletedBy && (
                  <div>
                    Người xóa{" "}
                    <b>
                      {typeof item.deletedBy === "object"
                        ? item.deletedBy.fullName
                        : item.deletedBy}
                    </b>
                  </div>
                )}
                {item.deadline && (
                  <div>
                    Deadline <b>{item.deadline}</b>
                  </div>
                )}
                {item.status && (
                  <div>
                    Trạng thái{" "}
                    <span
                      className="trash-list-status"
                      style={statusColor(item.status)}
                    >
                      {item.status}
                    </span>
                  </div>
                )}
              </div>
              {item.members && (
                <div className="trash-list-members">
                  {item.members.map((m, i) => (
                    <span className="trash-list-member" key={i}>
                      {m}
                    </span>
                  ))}
                </div>
              )}
            </div>
            <div className="trash-list-actions">
              <span className="trash-list-type">Công việc</span>
              <button
                className="trash-list-more"
                onClick={() => setActionIdx(idx)}
              >
                ⋯
              </button>
              {actionIdx === idx && (
                <ActionTrash
                  onRestore={() => handleRestoreItem(item._id || item.id)}
                  onDelete={() => handleDeleteItem(item._id || item.id)}
                  onClose={() => setActionIdx(null)}
                />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Job;
