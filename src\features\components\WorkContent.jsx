// Import các thư viện và component cần thiết
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { NavLink, Outlet, useLocation, useNavigate, useParams } from 'react-router-dom';
import '../../styles/WorkContent.css'; // Import file CSS cho layout
import SortPopup from './SortPopup'; // Import component SortPopup
// Import các icon sử dụng trong giao diện
import {
    createProjectTask,
    getProjectTasks,
    transformTaskData,
    transformTaskListData,
    uploadTaskAttachment
} from '../../api/taskManagement';
import { getProjectMembers, getAllUsers, transformUserData } from '../../api/userManagement';
import addIcon from '../../assets/add.svg';
import arrangeIcon from '../../assets/arrange.svg';
import closeLocIcon from '../../assets/closeLoc.svg';
import completeIcon from '../../assets/complete.svg';
import considerIcon from '../../assets/consider.svg';
import startDateIcon from '../../assets/creationdate.svg';
import deploymentIcon from '../../assets/deployment.svg';
import endDateIcon from '../../assets/enddate.svg';
import filterIcon from '../../assets/filter.svg';
import TaskIcon from '../../assets/icon-sidebar/congviec.svg';
import overdueIcon from '../../assets/triangle-alert.svg';
import waitingIcon from '../../assets/waiting.svg';
import JobCreate from './JobCreate';

const WorkContent = () => {
  // Tự động xác định role của user để quyết định hiển thị các action của leader
  // const userRole = getUserRole();
  // const showLeaderActions = ['leader', 'departmenthead', 'hr', 'ceo', 'admin'].includes(userRole);
  const showLeaderActions = true;
  
  // Lấy projectId từ URL params
  const { projectId } = useParams();
  // Lấy thông tin về đường dẫn hiện tại
  const location = useLocation();
  // Hook điều hướng trang
  const navigate = useNavigate();
  
  // State hiển thị popup sắp xếp và lọc
  const [showSortPopup, setShowSortPopup] = useState(false);
  const [showFilterPopup, setShowFilterPopup] = useState(false);
  const [showJobCreate, setShowJobCreate] = useState(false);
  
  // State lưu tuỳ chọn sắp xếp và lọc thực tế
  const [sortBy, setSortBy] = useState(null);
  const [filters, setFilters] = useState({
    status: [], // Lọc theo trạng thái công việc
    dateRange: {
      startDate: null, // Ngày bắt đầu
      endDate: null, // Ngày kết thúc
      dueStartDate: null, // Ngày bắt đầu hạn chót
      dueEndDate: null // Ngày kết thúc hạn chót
    }
  });
  
  const [taskList, setTaskList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [projectMembers, setProjectMembers] = useState([]);
  const [loadingMembers, setLoadingMembers] = useState(false);
  const [allUsers, setAllUsers] = useState([]);
  const [loadingAllUsers, setLoadingAllUsers] = useState(false);
  
  // Kiểm tra xem có phải route công việc đội hay không
  const isTeamTasksRoute = location.pathname.startsWith('/team/tasks') ||
                           location.pathname.startsWith('/staff/tasks') ||
                           location.pathname.startsWith('/leader/tasks');

  // Đặt basePath cho các đường dẫn điều hướng
  const basePath = location.pathname.startsWith('/team/tasks') ? '/team/tasks' :
                   location.pathname.startsWith('/staff/tasks') ? '/staff/tasks' :
                   location.pathname.startsWith('/leader/tasks') ? '/leader/tasks' :
                   `/projects/${projectId}/work`;

  // Tự động chuyển hướng sang trang công việc nếu đang ở basePath
  useEffect(() => {
    if (location.pathname === '/team/tasks' ||
        location.pathname === '/staff/tasks' ||
        location.pathname === '/leader/tasks' ||
        location.pathname === `/projects/${projectId}/work`) {
      navigate(`${basePath}/list`, { replace: true });
    }
  }, [location.pathname, basePath, navigate, projectId]);

  // Load project members từ API
  useEffect(() => {
    const loadMembers = async () => {
      if (!projectId) return;

      setLoadingMembers(true);
      try {
        const response = await getProjectMembers(projectId);
        setProjectMembers(response.data || []);
      } catch (err) {
        console.error('Error loading project members:', err);
        setProjectMembers([]);
      } finally {
        setLoadingMembers(false);
      }
    };

    loadMembers();
  }, [projectId]);

  // Load tất cả users trong hệ thống cho người theo dõi
  useEffect(() => {
    const loadAllUsers = async () => {
      setLoadingAllUsers(true);
      try {
        const response = await getAllUsers({
          populate: 'department,departmentId',
          include: 'department'
        });
        if (response.success) {
          const transformedUsers = (response.data || []).map(backendUser =>
            transformUserData(backendUser)
          );
          setAllUsers(transformedUsers);
        }
      } catch (err) {
        console.error('Error loading all users:', err);
        setAllUsers([]);
      } finally {
        setLoadingAllUsers(false);
      }
    };

    loadAllUsers();
  }, []);

  // Load tasks từ API khi component mount hoặc projectId thay đổi
  useEffect(() => {
    const loadTasks = async () => {
      if (!projectId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await getProjectTasks(projectId);
        const transformedTasks = transformTaskListData(response.data || response);
        setTaskList(transformedTasks);
      } catch (err) {
        console.error('Error loading tasks:', err);
        setError(err.message);
        setTaskList([]);
      } finally {
        setLoading(false);
      }
    };

    loadTasks();
  }, [projectId]);

  // Kiểm tra xem có đang ở trang công việc không
  const isInTasks = location.pathname.includes('/work');

  // Kiểm tra xem có đang ở tab Danh sách không (chỉ hiển thị sắp xếp/lọc ở tab này)
  const isInListTab = location.pathname.includes('/work/list') || location.pathname.endsWith('/work');

  // Hàm bật/tắt popup sắp xếp
  const toggleSortPopup = () => {
    setShowSortPopup(!showSortPopup);
    if (showFilterPopup) setShowFilterPopup(false);
  };
  
  // Hàm bật/tắt popup lọc
  const toggleFilterPopup = () => {
    setShowFilterPopup(!showFilterPopup);
    if (showSortPopup) setShowSortPopup(false);
  };
  
  // Xử lý chọn tuỳ chọn sắp xếp
  const handleSortOptionSelect = (sortOption) => {
    if (sortBy === sortOption) {
      setSortBy(null); // Bỏ chọn nếu nhấn lại mục đang chọn
    } else {
      setSortBy(sortOption);
    }
    // Sorting by option
  };
  
  // Xử lý chọn trạng thái lọc
  const handleStatusFilterChange = (status, isChecked) => {
    setFilters(prevFilters => {
      const updatedStatus = isChecked 
        ? [...prevFilters.status, status]
        : prevFilters.status.filter(s => s !== status);
      
      return {
        ...prevFilters,
        status: updatedStatus
      };
    });
  };
  
  // Áp dụng bộ lọc và đóng popup
  const applyFilters = () => {
    // Applying filters
    setShowFilterPopup(false);
  };

  // Đóng popup lọc khi click ra ngoài hoặc nhấn ESC
  useEffect(() => {
    if (!showFilterPopup) return;

    const handleClickOutside = (event) => {
      const popup = document.getElementById('filter-popup');
      const btn = document.querySelector('.btn-filters');

      // Đóng popup nếu click ra ngoài popup (nhưng không phải nút)
      if (popup && !popup.contains(event.target) && btn && !btn.contains(event.target)) {
        setShowFilterPopup(false);
      }
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setShowFilterPopup(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showFilterPopup]);

  // Context truyền xuống các component con qua Outlet
  const outletContext = {
    sortBy,
    filters,
    taskList,
    setTaskList,
    projectMembers,
    loadingMembers
  };

  // Hàm thêm task lớn
  const handleAddMainTask = async (jobData) => {
    if (!jobData || !jobData.title?.trim()) return;

    // Debug log để kiểm tra dữ liệu được gửi
    console.log('WorkContent - handleAddMainTask jobData:', jobData);
    console.log('WorkContent - followersData:', jobData.followersData);
    console.log('WorkContent - followerIds:', jobData.followerIds);

    try {
      const response = await createProjectTask(projectId, jobData);
      console.log('WorkContent - createProjectTask response:', response);

      let newTask = transformTaskData(response.data || response);
      console.log('WorkContent - transformTaskData result:', newTask);
      console.log('WorkContent - newTask followers:', newTask.followers);

      // Lưu thông tin followers vào localStorage để có thể sử dụng sau này
      if (jobData.followersData && jobData.followersData.length > 0) {
        const taskId = newTask.id || newTask._id;
        const followersKey = `task_followers_${taskId}`;
        localStorage.setItem(followersKey, JSON.stringify(jobData.followersData));
        console.log('WorkContent - Saved followers to localStorage:', followersKey);
      }

      // Đảm bảo status được hiển thị đúng theo dữ liệu đã chọn trong form
      // Map từ backend status về frontend status để hiển thị đúng
      const backendToFrontendStatusMap = {
        "pending": "waiting",
        "in_progress": "in_progress",
        "review": "review",
        "completed": "completed",
        "overdue": "overdue"
      };

      // Sử dụng originalStatus từ form nếu có, nếu không thì map từ backend status
      if (jobData.originalStatus) {
        newTask.status = jobData.originalStatus;
      } else if (newTask.status && backendToFrontendStatusMap[newTask.status]) {
        newTask.status = backendToFrontendStatusMap[newTask.status];
      }

      // Đảm bảo priority được hiển thị đúng theo dữ liệu đã chọn trong form
      // Sử dụng originalPriority từ form nếu có
      if (jobData.originalPriority) {
        newTask.priority = jobData.originalPriority;
      }

      // Cập nhật avatar từ jobData.membersData hoặc projectMembers để đảm bảo hiển thị đúng ngay lập tức
      if (newTask.assignee && newTask.assignee.length > 0) {
        newTask.assignee = newTask.assignee.map(assignee => {
          // Ưu tiên lấy từ jobData.membersData (dữ liệu từ form)
          let memberData = null;
          if (jobData.membersData && jobData.membersData.length > 0) {
            memberData = jobData.membersData.find(member =>
              (member.id === assignee.userId) ||
              (member.userId === assignee.userId) ||
              (member._id === assignee.userId)
            );
          }

          // Fallback về projectMembers nếu không tìm thấy trong membersData
          if (!memberData && projectMembers && projectMembers.length > 0) {
            memberData = projectMembers.find(member =>
              (member.id === assignee.userId) ||
              (member.userId === assignee.userId) ||
              (member._id === assignee.userId)
            );
          }

          if (memberData) {
            return {
              ...assignee,
              name: memberData.name || memberData.fullName || assignee.name,
              avatar: memberData.avatar || memberData.profilePicture || assignee.avatar
            };
          }
          return assignee;
        });
      }

      // Cập nhật thông tin followers từ jobData.followersData
      // Luôn ưu tiên dữ liệu từ form (jobData.followersData) vì backend có thể không trả về
      if (jobData.followersData && jobData.followersData.length > 0) {
        console.log('WorkContent - Setting followers from jobData.followersData');
        newTask.followers = jobData.followersData.map(follower => ({
          userId: follower.id || follower.userId || follower._id,
          name: follower.name || follower.fullName || "Chưa có tên",
          fullName: follower.fullName || follower.name || "Chưa có tên",
          avatar: follower.avatar || follower.profilePicture || "",
        }));
        // Cũng set followersData để JobDetail có thể sử dụng
        newTask.followersData = jobData.followersData;
      } else if (newTask.followers && newTask.followers.length > 0) {
        console.log('WorkContent - Updating existing followers with allUsers data');
        // Cập nhật avatar cho followers từ allUsers nếu có
        newTask.followers = newTask.followers.map(follower => {
          let followerData = null;
          if (allUsers && allUsers.length > 0) {
            followerData = allUsers.find(user =>
              (user.id === follower.userId) ||
              (user.userId === follower.userId) ||
              (user._id === follower.userId)
            );
          }

          if (followerData) {
            return {
              ...follower,
              name: followerData.name || followerData.fullName || follower.name,
              fullName: followerData.fullName || followerData.name || follower.fullName,
              avatar: followerData.avatar || followerData.profilePicture || follower.avatar
            };
          }
          return follower;
        });
      } else {
        console.log('WorkContent - No followers data found');
      }

      // Upload tệp đính kèm nếu có
      if (jobData.attachments && jobData.attachments.length > 0) {
        const taskId = newTask.id || newTask._id;
        for (const file of jobData.attachments) {
          try {
            await uploadTaskAttachment(projectId, taskId, file);
          } catch (error) {
            console.error(`Error uploading file ${file.name}:`, error);
            // Có thể hiển thị thông báo lỗi cho từng file nếu cần
          }
        }
      }

      setTaskList(prev => [newTask, ...prev]);
      setShowJobCreate(false);
      // Phát sự kiện đồng bộ
      window.dispatchEvent(new Event("projectTasksUpdated"));

    } catch (err) {
      console.error('Error creating new task:', err);
      // Có thể hiển thị thông báo lỗi cho user
    }
  };

  return (
    <div className="content-container">
      <div className="work-navigation">
        <div className="work-header">
          <div className="work-title">
            <img src={TaskIcon} alt="Tasks" className="sidebar-icon" />
            <h1>Công việc</h1>
          </div>
        </div>
        <div className="work-tabs">
          <div className="main-tabs">
            {/* Nhóm tab Công việc */}
            <div className="main-tabs-group">
              {/* Tab Công việc */}
              <NavLink
                to={`${basePath}/list`}
                className={({ isActive }) => isActive || location.pathname.includes('/work/') ? 'work-tab active-tab' : 'work-tab'}
              >
                Công việc <span className="task-count">{taskList ? taskList.length : 0}</span>
              </NavLink>
            </div>
             {/* Nút tạo công việc chỉ cho leader */}
             {showLeaderActions && (
              <div className="create-task-container">
                <button
                  className="work-tab btn-create-task"
                  onClick={() => setShowJobCreate(true)}
                >
                  <img src={addIcon} alt="Tạo" />
                  <span>Tạo công việc</span>
                </button>
              </div>
            )}
          </div>
          {/* Nếu đang ở trang công việc thì hiển thị các sub-tab */}
          {isInTasks && (
            <div className="tabs-row">
              <div className="sub-tabs">
                {/* Sub-tab Danh sách */}
                <NavLink
                  to={`${basePath}/list`}
                  className={({ isActive }) => isActive || location.pathname.endsWith('/work') ? 'sub-tab active-sub-tab' : 'sub-tab'}
                >
                  Danh sách
                </NavLink>
                {/* Sub-tab Kanban */}
                <NavLink
                  to={`${basePath}/kanban`}
                  className={({ isActive }) => isActive ? 'sub-tab active-sub-tab' : 'sub-tab'}
                >
                  Kanban
                </NavLink>
                {/* Sub-tab Dòng thời gian */}
                <NavLink
                  to={`${basePath}/timeline`}
                  className={({ isActive }) => isActive ? 'sub-tab active-sub-tab' : 'sub-tab'}
                >
                  Dòng thời gian
                </NavLink>
                {/* Sub-tab Biểu đồ */}
                <NavLink
                  to={`${basePath}/chart`}
                  className={({ isActive }) => isActive ? 'sub-tab active-sub-tab' : 'sub-tab'}
                >
                  Biểu đồ
                </NavLink>
              </div>

              {/* Chỉ hiển thị tuỳ chọn sắp xếp/lọc khi ở tab Danh sách */}
              {isInListTab && (
                <div className="view-options">
                {/* Nút sắp xếp */}
                <div className="sort-container">
                  <button className="btn-sort" onClick={toggleSortPopup}>
                    <img src={arrangeIcon} alt="Sort" className="option-icon" />
                    <span>Sắp xếp</span>
                  </button>
                  {/* Popup sắp xếp sử dụng component SortPopup */}
                  {showSortPopup && (
                    <SortPopup
                      sortBy={sortBy}
                      onSortOptionSelect={handleSortOptionSelect}
                      onClose={() => setShowSortPopup(false)}
                    />
                  )}
                </div>
                
                {/* Nút lọc */}
                <div className="filter-container">
                  <button className="btn-filters" onClick={toggleFilterPopup}>
                    <img src={filterIcon} alt="Filter" className="option-icon" />
                    <span>Lọc</span>
                  </button>
                  {/* Popup lọc sử dụng Portal */}
                  {showFilterPopup && createPortal(
                    <>
                      <div className="filter-popup-backdrop" onClick={() => setShowFilterPopup(false)}></div>
                      <div className="filter-popup filter-popup-custom" id="filter-popup">
                        <div className="filter-popup-header-custom">
                          <span className="filter-popup-title">Lọc công việc</span>
                          <button className="filter-popup-close-btn" onClick={() => setShowFilterPopup(false)}>
                            <img src={closeLocIcon} alt="Đóng" style={{ width: 20, height: 20 }} />
                          </button>
                        </div>
                        <div className="filter-popup-content">
                          <div className="filter-status-group">Trạng thái</div>
                          <div className="filter-status-list">
                            {/* Các tuỳ chọn lọc trạng thái công việc */}
                            <label className="filter-status-label">
                              <img src={waitingIcon} alt="Đang chờ" style={{ width: 20, height: 20 }} />
                              <span style={{ flex: 1 }}>Đang chờ</span>
                              <input type="checkbox" checked={filters.status.includes('pending')} onChange={e => handleStatusFilterChange('pending', e.target.checked)} style={{ width: 0, height: 0, opacity: 0, position: 'absolute' }} />
                              <span className={`custom-round-checkbox waiting${filters.status.includes('pending') ? ' checked' : ''}`}>
                                {filters.status.includes('pending') && <span className="custom-round-inner"></span>}
                              </span>
                            </label>
                            <label className="filter-status-label">
                              <img src={deploymentIcon} alt="Đang triển khai" style={{ width: 20, height: 20 }} />
                              <span style={{ flex: 1 }}>Đang triển khai</span>
                              <input type="checkbox" checked={filters.status.includes('in_progress')} onChange={e => handleStatusFilterChange('in_progress', e.target.checked)} style={{ width: 0, height: 0, opacity: 0, position: 'absolute' }} />
                              <span className={`custom-round-checkbox in-progress${filters.status.includes('in_progress') ? ' checked' : ''}`}>
                                {filters.status.includes('in_progress') && <span className="custom-round-inner"></span>}
                              </span>
                            </label>
                            <label className="filter-status-label">
                              <img src={completeIcon} alt="Hoàn thành" style={{ width: 20, height: 20 }} />
                              <span style={{ flex: 1 }}>Hoàn thành</span>
                              <input type="checkbox" checked={filters.status.includes('completed')} onChange={e => handleStatusFilterChange('completed', e.target.checked)} style={{ width: 0, height: 0, opacity: 0, position: 'absolute' }} />
                              <span className={`custom-round-checkbox completed${filters.status.includes('completed') ? ' checked' : ''}`}>
                                {filters.status.includes('completed') && <span className="custom-round-inner"></span>}
                              </span>
                            </label>
                            <label className="filter-status-label">
                              <img src={overdueIcon} alt="Quá hạn" style={{ width: 20, height: 20 }} />
                              <span style={{ flex: 1 }}>Quá hạn</span>
                              <input type="checkbox" checked={filters.status.includes('overdue')} onChange={e => handleStatusFilterChange('overdue', e.target.checked)} style={{ width: 0, height: 0, opacity: 0, position: 'absolute' }} />
                              <span className={`custom-round-checkbox overdue${filters.status.includes('overdue') ? ' checked' : ''}`}>
                                {filters.status.includes('overdue') && <span className="custom-round-inner"></span>}
                              </span>
                            </label>
                            <label className="filter-status-label">
                              <img src={considerIcon} alt="Xem xét" style={{ width: 20, height: 20 }} />
                              <span style={{ flex: 1 }}>Xem xét</span>
                              <input type="checkbox" checked={filters.status.includes('review')} onChange={e => handleStatusFilterChange('review', e.target.checked)} style={{ width: 0, height: 0, opacity: 0, position: 'absolute' }} />
                              <span className={`custom-round-checkbox consider${filters.status.includes('review') ? ' checked' : ''}`}>
                                {filters.status.includes('review') && <span className="custom-round-inner"></span>}
                              </span>
                            </label>
                          </div>
                          <div className="filter-date-group-custom">Khoảng thời gian</div>
                          <div className="filter-date-row-custom">
                            {/* Chọn ngày bắt đầu */}
                            <div className="filter-date-input-group">
                              <img src={startDateIcon} alt="calendar" className="calendar-icon" />
                              <input
                                type="date"
                                value={filters.dateRange.startDate || ''}
                                onChange={(e) => setFilters(f => ({
                                  ...f,
                                  dateRange: { ...f.dateRange, startDate: e.target.value || null }
                                }))}
                                className="filter-date"
                                placeholder="Chọn ngày bắt đầu"
                              />
                            </div>
                            {/* Chọn ngày kết thúc */}
                            <div className="filter-date-input-group">
                              <img src={endDateIcon} alt="calendar" className="calendar-icon" />
                              <input
                                type="date"
                                value={filters.dateRange.endDate || ''}
                                onChange={(e) => setFilters(f => ({
                                  ...f,
                                  dateRange: { ...f.dateRange, endDate: e.target.value || null }
                                }))}
                                className="filter-date"
                                placeholder="Chọn ngày kết thúc"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="filter-action-row-custom">
                          <button className="btn-reset-filter filter-btn-cancel" onClick={() => setShowFilterPopup(false)}>Huỷ</button>
                          <button className="btn-apply-filter filter-btn-apply" onClick={applyFilters}>Lọc</button>
                        </div>
                      </div>
                    </>,
                    document.body
                  )}
                </div>
              </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Khu vực render nội dung các trang con */}
      <div className="work-content">
        <Outlet context={outletContext} />
      </div>
      
      {/* Hiện form tạo công việc khi bấm nút tạo công việc */}
      {showJobCreate && (
        <JobCreate
          onCancel={() => setShowJobCreate(false)}
          onSubmit={handleAddMainTask}
          projectId={projectId}
          disableDepartmentFilter={true}
          projectMembers={projectMembers}
          loadingProjectMembers={loadingMembers}
          allUsers={allUsers}
          loadingAllUsers={loadingAllUsers}
        />
      )}
    </div>
  );
};

export default WorkContent;
