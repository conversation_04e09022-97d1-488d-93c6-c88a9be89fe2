/* Chart.css - Styles for Charts component */
.chart-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
  background-color: transparent;
  width: 100%;
  height: calc(100vh - 120px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar styling for chart container */
.chart-container::-webkit-scrollbar {
  width: 6px;
}

.chart-container::-webkit-scrollbar-track {
  background: transparent;
}

.chart-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
}

.chart-container:hover::-webkit-scrollbar-thumb {
  background: #ccc;
}

.chart-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.chart-content {
  min-height: 100%;
  display: flex;
  gap: 20px;
  align-items: flex-start;
  width: 100%;
  padding-bottom: 20px;
}

.chart-card {
  background-color: #fafbfc;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 10px;
  width: 500px;
  flex-shrink: 0;
  height: fit-content;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding-bottom: 20px;
}

.chart-header-icon {
  margin-right: 8px;
  width: 24px;
  height: 24px;
}

.chart-header-title {
  color: #333;
  font-weight: 600;
  margin-left: 4px;
}

.chart-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.chart-canvas {
  width: 280px;
  height: 280px;
  flex-shrink: 0;
}

.chart-tooltip {
  position: absolute;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 200px;
  z-index: 10;
  pointer-events: none;
  font-size: 13px;
  border: 1px solid #e9ecef;
}

.tooltip-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 6px;
}

.tooltip-title {
  font-weight: 600;
  color: #5b5b5b;
  font-size: 14px;
}

.tooltip-color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.tooltip-status-label {
  font-weight: 500;
  color: #5b5b5b;
  font-size: 13px;
}

.tooltip-members {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.tooltip-avatars {
  display: flex;
  gap: 4px;
}

.tooltip-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tooltip-tasks {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tooltip-task-count {
  font-weight: 600;
  color: #5b5b5b;
  font-size: 13px;
}

.chart-legend {
  padding-top: 20px;
  margin-left: 25px;
  min-width: 280px;
  width: 450px;
  background: #fff;
  border-radius: 8px;
}

.legend-header {
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 16px;
  color: #5b5b5b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-header-icon {
  margin-right: 8px;
  width: 18px;
  height: 18px;
}

.legend-header-title {
  color: #007bff;
  font-weight: 600;
  margin-left: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 12px 0 0 32px;
  font-size: 14px;
  width: 140px;
}

.legend-color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
}

.legend-label {
  color: #333;
  font-weight: 500;
}

/* Members Table Styles */
.member-table-card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0;
  flex: 1;
  height: 600px;
  overflow: hidden;
  margin-right: 34px;
}

.member-table-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.member-table-container::-webkit-scrollbar {
  width: 6px;
}

.member-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.member-table-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
}

.member-table-container:hover::-webkit-scrollbar-thumb {
  background: #ccc;
}

.member-table-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.member-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.member-table thead {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 1;
}

.member-table th {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #5b5b5b;
  font-size: 14px;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e9ecef;
  white-space: nowrap;
}

.member-table th:first-child {
  width: 35%;
  text-align: left;
}

.member-table th:nth-child(2) {
  width: 15%;
}

.member-table th:nth-child(3) {
  width: 15%;
}

.member-table th:nth-child(4) {
  width: 20%;
}

.member-table th:nth-child(5) {
  width: 15%;
}

.member-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.member-table td:first-child {
  white-space: normal;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-details {
  flex: 1;
}

.member-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 1px;
  font-size: 13px;
}

.member-email {
  font-size: 11px;
  color: #666;
  margin-bottom: 1px;
}

.member-position {
  font-size: 11px;
  color: #999;
}

.member-role {
  text-align: center;
  color: #666;
  font-weight: 500;
}

.member-department {
  text-align: center;
}

.department-badge {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.member-tasks {
  text-align: center;
}

.task-indicators {
  display: flex;
  justify-content: center;
  gap: 4px;
  flex-wrap: wrap;
}

.task-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.task-indicator.waiting {
  color: #666;
}

.task-indicator.progress {
  color: #00838f;
  background: none !important;
}

.task-indicator.review {
  color: #3f51b5;
}

.task-indicator.completed {
  color: #0277bd;
}

.task-icon {
  font-size: 8px;
  width: 14px;
  height: 14px;
}

.member-efficiency {
  text-align: center;
}

.efficiency-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column-reverse;
  margin-bottom: 15px;
}

.efficiency-bar {
  width: 50px;
  height: 5px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.efficiency-fill {
  height: 100%;
  background-color: #2196f3;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.efficiency-text {
  font-size: 11px;
  font-weight: 600;
  color: #2196f3;
}

.member-total {
  text-align: center;
}

.total-tasks {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.total-number {
  font-size: 16px;
  font-weight: 700;
  color: #333;
}

.total-label {
  font-size: 10px;
  color: #666;
}
