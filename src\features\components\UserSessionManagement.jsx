import React, { useState, useEffect } from 'react';
import { ADMIN_ENDPOINTS } from '../../api/endpoints';
import ConfirmDialog from '../../components/ConfirmDialog';
import NotificationToast from '../../components/NotificationToast';
import '../../styles/UserSessionManagement.css';

const UserSessionManagement = ({ userId, onClose }) => {
  const [sessionInfo, setSessionInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [reason, setReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  
  // Dialog states
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [showInvalidateConfirm, setShowInvalidateConfirm] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });

  const getToken = () => localStorage.getItem('token');

  useEffect(() => {
    fetchSessionInfo();
  }, [userId]);

  const fetchSessionInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.GET_USER_SESSION_INFO(userId), {
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Không thể lấy thông tin session');
      }

      const data = await response.json();
      setSessionInfo(data.data);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const showNotification = (message, type = 'success') => {
    setNotification({ show: true, message, type });
  };

  const hideNotification = () => {
    setNotification({ show: false, message: '', type: 'success' });
  };

  const handleResetIp = async () => {
    try {
      setActionLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.RESET_IP(userId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Không thể reset IP');
      }

      const data = await response.json();
      showNotification(data.message, 'success');
      setReason('');
      setShowResetConfirm(false);
      fetchSessionInfo();
    } catch (error) {
      showNotification('Lỗi: ' + error.message, 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleInvalidateSession = async () => {
    try {
      setActionLoading(true);
      const response = await fetch(ADMIN_ENDPOINTS.INVALIDATE_USER_SESSION(userId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Không thể vô hiệu hóa session');
      }

      const data = await response.json();
      showNotification(data.message, 'success');
      setReason('');
      setShowInvalidateConfirm(false);
      fetchSessionInfo();
    } catch (error) {
      showNotification('Lỗi: ' + error.message, 'error');
    } finally {
      setActionLoading(false);
    }
  };



  const formatDate = (dateString) => {
    if (!dateString) return 'Không có';
    return new Date(dateString).toLocaleString('vi-VN');
  };

  if (loading) {
    return (
      <div className="session-management-modal">
        <div className="session-management-content">
          <div className="loading">Đang tải...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="session-management-modal">
        <div className="session-management-content">
          <div className="error">Lỗi: {error}</div>
          <button onClick={onClose}>Đóng</button>
        </div>
      </div>
    );
  }

  return (
    <div className="session-management-modal">
      <div className="session-management-content">
        <div className="modal-header">
          <h2>Quản lý Session & IP - {sessionInfo?.fullName}</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>

        <div className="session-info">
          <h3>Thông tin Session hiện tại</h3>
          <div className="info-grid">
            <div className="info-item">
              <label>Session ID:</label>
              <span>{sessionInfo?.activeSessionId || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Thời gian tạo:</label>
              <span>{formatDate(sessionInfo?.sessionCreatedAt)}</span>
            </div>
            <div className="info-item">
              <label>IP hiện tại:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.ip || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Browser:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.browser || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>OS:</label>
              <span>{sessionInfo?.sessionDeviceInfo?.os || 'Không có'}</span>
            </div>
            <div className="info-item">
              <label>Tổng số session đang hoạt động:</label>
              <span>{sessionInfo?.totalActiveSessions || 0}</span>
            </div>
          </div>
        </div>

        {sessionInfo?.activeSessions && sessionInfo.activeSessions.length > 0 && (
          <div className="active-sessions">
            <h3>Danh sách Session đang hoạt động</h3>
            <div className="sessions-list">
              {sessionInfo.activeSessions.filter(s => s.isActive).map((session, index) => (
                <div key={index} className="session-item">
                  <div className="session-info">
                    <span className="session-id">Session ID: {session.sessionId}</span>
                    <span className="browser">{session.deviceInfo?.browser} trên {session.deviceInfo?.os}</span>
                    <span className="ip">IP: {session.deviceInfo?.ip}</span>
                    <span className="time">{formatDate(session.createdAt)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="ip-restriction-info">
          <h3>Thông tin IP Multi-Device</h3>
          <div className="info-grid">
            <div className="info-item">
              <label>IP Web:</label>
              <span>{sessionInfo?.allowedDevices?.web?.isRegistered 
                ? sessionInfo.allowedDevices.web.ip 
                : 'Chưa đăng ký'}</span>
            </div>
            <div className="info-item">
              <label>IP Mobile:</label>
              <span>{sessionInfo?.allowedDevices?.mobile?.isRegistered 
                ? sessionInfo.allowedDevices.mobile.ip 
                : 'Chưa đăng ký'}</span>
            </div>
            <div className="info-item">
              <label>IP Legacy (cũ):</label>
              <span>{sessionInfo?.allowedIp || 'Không có'}</span>
            </div>
          </div>
          
          {sessionInfo?.allowedDevices && (
            <div className="device-details">
              <h4>Chi tiết thiết bị:</h4>
              {sessionInfo.allowedDevices.web?.isRegistered && (
                <div className="device-info">
                  <strong>🖥️ Web:</strong> {sessionInfo.allowedDevices.web.ip}
                  <small> (Đăng ký: {formatDate(sessionInfo.allowedDevices.web.registeredAt)})</small>
                </div>
              )}
              {sessionInfo.allowedDevices.mobile?.isRegistered && (
                <div className="device-info">
                  <strong>📱 Mobile:</strong> {sessionInfo.allowedDevices.mobile.ip}
                  <small> (Đăng ký: {formatDate(sessionInfo.allowedDevices.mobile.registeredAt)})</small>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="action-section">
          <h3>Thao tác</h3>
          
          <div className="action-group">
            <h4>Reset IP</h4>
            <div className="input-group">
              <p>Đưa user về trạng thái chưa đăng nhập lần đầu (xóa IP được phép)</p>
              <input
                type="text"
                placeholder="Lý do reset IP (tùy chọn)"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
              <button 
                onClick={() => setShowResetConfirm(true)}
                disabled={actionLoading}
                className="warning"
              >
                {actionLoading ? 'Đang xử lý...' : 'Reset IP'}
              </button>
            </div>
          </div>

          <div className="action-group">
            <h4>Quản lý Session</h4>
            <div className="button-group">
              <button 
                onClick={() => setShowInvalidateConfirm(true)}
                disabled={actionLoading}
                className="danger"
              >
                {actionLoading ? 'Đang xử lý...' : 'Vô hiệu hóa Session'}
              </button>
            </div>
          </div>


        </div>

        {sessionInfo?.ipLoginHistory && sessionInfo.ipLoginHistory.length > 0 && (
          <div className="login-history">
            <h3>Lịch sử đăng nhập</h3>
            <div className="history-list">
              {sessionInfo.ipLoginHistory.slice(-10).reverse().map((login, index) => (
                <div key={index} className="history-item">
                  <div className="history-info">
                    <span className="device-type">{login.deviceType === 'mobile' ? '📱' : '🖥️'} {login.deviceType === 'mobile' ? 'Mobile' : 'Web'}</span>
                    <span className="ip">{login.ip}</span>
                    <span className="browser">{login.browser} trên {login.os}</span>
                    <span className="time">{formatDate(login.loginTime)}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="modal-footer">
          <button onClick={onClose}>Đóng</button>
        </div>

        {/* Confirm Dialogs */}
        <ConfirmDialog
          show={showResetConfirm}
          title="Xác nhận Reset IP"
          message="Bạn có chắc chắn muốn reset IP của user này? Tất cả session hiện tại sẽ bị xóa và user có thể đăng nhập từ bất kỳ IP nào trong lần đăng nhập tiếp theo."
          confirmText="Reset IP"
          cancelText="Hủy"
          type="warning"
          onConfirm={handleResetIp}
          onCancel={() => setShowResetConfirm(false)}
        />

        <ConfirmDialog
          show={showInvalidateConfirm}
          title="Xác nhận Vô hiệu hóa Session"
          message="Bạn có chắc chắn muốn vô hiệu hóa tất cả session của user này? User sẽ bị đăng xuất khỏi tất cả thiết bị."
          confirmText="Vô hiệu hóa"
          cancelText="Hủy"
          type="danger"
          onConfirm={handleInvalidateSession}
          onCancel={() => setShowInvalidateConfirm(false)}
        />

        {/* Notification Toast */}
        <NotificationToast
          show={notification.show}
          message={notification.message}
          type={notification.type}
          onClose={hideNotification}
        />
      </div>
    </div>
  );
};

export default UserSessionManagement; 