.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 300px;
  max-width: 500px;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.notification-toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border-left: 4px solid;
}

.notification-toast.success .toast-content {
  border-left-color: #28a745;
}

.notification-toast.error .toast-content {
  border-left-color: #dc3545;
}

.notification-toast.warning .toast-content {
  border-left-color: #ffc107;
}

.notification-toast.info .toast-content {
  border-left-color: #17a2b8;
}

.toast-icon {
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  margin-left: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.toast-close:hover {
  background: #f5f5f5;
  color: #666;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .notification-toast {
    left: 20px;
    right: 20px;
    min-width: auto;
    max-width: none;
  }
}