@import url('../index.css');
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.hr-toolbar-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 16px;
}
.hr-toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  flex: 1;
}
.hr-toolbar-select {
  padding: 8px 32px 8px 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 15px;
  min-width: 160px;
}
.hr-toolbar-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
}

.hr-toolbar-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #fff;
  border: 1px solid #7C7C7C1A;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 15px;
  cursor: pointer;
  height: 40px;
  min-width: 140px;
}
.hr-toolbar-btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #007BFF;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 15px;
  cursor: pointer;
  height: 40px;
  min-width: 140px;
}
.hr-toolbar-btn:disabled {
  background: #ccc;
  color: #888;
  cursor: not-allowed;
  opacity: 0.6;
}
.hr-toolbar-btn-primary:disabled {
  background: #ccc;
  color: #888;
}
.hr-layout-container {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px #f3f3f3;
}
.hr-layout-title-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 4px;
}
.hr-layout-title {
  font-weight: 600;
  font-size: 22px;
  color: #5D5D5D;
}
.hr-layout-desc {
  color: #5d5d5d;
  font-size: 15px;
  margin-top: 2px;
}
.hr-layout-search {
  margin-top: 18px;
  margin-bottom: 12px;
}
.hr-layout-search-input-wrap {
  position: relative;
}
.hr-layout-search-input {
  width: 100%;
  box-sizing: border-box;
  padding: 12px 44px 12px 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 15px;
  outline: none;
}
.hr-layout-search-icon {
  position: absolute;
  right: 16px;
  top: 55%;
  transform: translateY(-50%);
  color: #bdbdbd;
  font-size: 20px;
  opacity: 0.6;
}
.hr-dropdown {
  position: relative;
  display: inline-block;
}
.hr-dropdown-btn {
  padding: 8px 12px 8px 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 15px;
  min-width: 160px;
  background: #fff;
  cursor: pointer;
  text-align: left;
  font-weight: 400;
  color: #000;
  box-shadow: none;
  outline: none;
  transition: border 0.2s;
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: space-between;
}
.hr-dropdown-btn .dropdown-icon {
  width: 16px;
  height: 16px;
  opacity: 1;
  pointer-events: none;
  flex-shrink: 0;
}
.hr-dropdown-menu {
  display: none;
  position: absolute;
  left: 0;
  top: 110%;
  min-width: 180px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
  z-index: 10;
  padding: 8px 0;
  margin-top: 4px;
}
.hr-dropdown:hover .hr-dropdown-menu,
.hr-dropdown:focus-within .hr-dropdown-menu {
  display: block;
}
.hr-dropdown-item {
  padding: 10px 20px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
  background: #fff;
  transition: background 0.15s;
  white-space: nowrap;
}
.hr-dropdown-item:hover {
  background: #f5f7fa;
}
.hr-dropdown-item:first-child {
  font-weight: 600;
  color: #888;
  cursor: default;
  background: #fff;
}
