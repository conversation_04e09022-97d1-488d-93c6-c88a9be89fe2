@import url('../index.css');

.profile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.08);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-modal-content {
  position: relative;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.15);
  padding: 0;
  min-width: 400px;
  max-width: 95vw;
  max-height: 95vh;
  overflow-y: auto;
}

.profile-modal-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: none;
  font-size: 2rem;
  color: #888;
  cursor: pointer;
  z-index: 10;
}

.profile-container {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 32px 24px 24px 24px;
  min-width: 350px;
  max-width: 400px;
  margin: 0 auto;
}

.profile-sidepanel {
  position: fixed;
  top: 0;
  right: 0;
  width: 350px;
  height: 100vh;
  background: #fff;
  border-left: 2px solid #e0e0e0;
  box-shadow: -2px 0 8px rgba(0,0,0,0.04);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.profile-header {
  background: linear-gradient(to right, #E6F0FA 0%, #F5F9FD 50%, #FFFFFF 100%);
  padding: 24px 24px 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.profile-header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.profile-title-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.profile-title-icon {
  width: 24px;
  height: 24px;
}

.profile-title {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  line-height: 1.2;
}

.profile-subtitle {
  font-size: 14px;
  color: #000;
  font-weight: 400;
}

.profile-close-icon {
  width: 22px;
  height: 22px;
  cursor: pointer;
  opacity: 1;
  transition: opacity 0.2s ease;
  margin-top: 2px;
}

.profile-close-icon:hover {
  opacity: 0.7;
}

.profile-action-buttons {
  display: flex;
  gap: 12px;
  margin-top: auto;
  padding-top: 24px;
}

.profile-password-btn,
.profile-update-avatar-btn {
  display: flex;
  align-items: center;
  justify-content: center;  
  gap: 8px;
  padding: 10px;
  background: #fff;
  color: #5D5D5D;
  border: 1px solid #DCDCDC;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  font-weight: 500;
  transition: all 0.2s ease;
  flex: 1;
  text-decoration: none;
}

.profile-password-btn:hover,
.profile-update-avatar-btn:hover {
  background: #f8f9fa;
  border-color: #d0d0d0;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.profile-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.profile-close {
  display: none; /* Đã có nút close ở modal */
}

.profile-user-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.profile-avatar-wrapper {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
}

.profile-user-info {
  flex: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.profile-email {
  font-size: 16px;
  color: #666;
  font-weight: 400;
}

.profile-form {
  margin-top: 10px;
}

.profile-form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.profile-form-group label {
  font-size: 14px;
  color: #555;
  margin-bottom: 4px;
}

.profile-form-group input {
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  font-size: 13px;
  background: #ffffff;
  color: #333;
  transition: all 0.2s ease;
}

.profile-form-group input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.profile-form-group input[readonly] {
  background: #f8f9fa;
  color: #6c757d;
  cursor: default;
}

.profile-label-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.form-icon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.profile-info-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.profile-info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #F9FAFB;
  padding: 16px;
  border-radius: 8px;
}

.profile-info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.profile-info-value {
  font-size: 14px;
  color: #333;
  font-weight: 300;
}

.profile-info-icon {
  width: 30px;
  height: 30px;
  opacity: 1;
  flex-shrink: 0;
}

.profile-info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Password Change Form Styles */
.profile-password-form-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.profile-password-submit-btn {
  padding: 10px;
  background: #007BFF;
  color: #FFF;
  border: 1px solid #fff;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
  font-size: 13px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  font-weight: 500;
  transition: background 0.2s ease;
}
.profile-password-submit-btn:hover {
  background: #0056b3;
  border-color: #fff;
}
.profile-password-cancel-btn:hover {
  background: #f8f9fa;
  border-color: #d0d0d0;
}

.profile-password-submit-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.profile-password-cancel-btn {
  padding: 10px;
  background: #fff;
  color: #5D5D5D;
  border: 1px solid #DCDCDC;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
  font-size: 13px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  font-weight: 500;
  transition: background 0.2s ease;
}

/* Profile Edit Form Styles */
.profile-edit-form-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.profile-edit-submit-btn {
  padding: 10px 20px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.profile-edit-submit-btn:hover {
  background: #45a049;
}

.profile-edit-submit-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.profile-edit-cancel-btn {
  padding: 10px 20px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.profile-edit-cancel-btn:hover {
  background: #d32f2f;
}

@media (max-width: 500px) {
  .profile-modal-content, .profile-container {
    min-width: 90vw;
    max-width: 98vw;
    padding: 12px 4vw 16px 4vw;
  }

  .profile-sidepanel {
    width: 100vw;
    min-width: unset;
    max-width: unset;
    padding: 12px 4vw 16px 4vw;
  }
}

/* Password input wrapper styles */
.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-wrapper input {
  padding-right: 40px !important;
  width: 100%;
}

.password-toggle-icon {
  position: absolute;
  right: 12px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.password-toggle-icon:hover {
  opacity: 1;
}

/* Error styling for validation */
.profile-form-group input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.profile-validation-error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}
