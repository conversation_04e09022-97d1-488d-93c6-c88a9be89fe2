// Mock backend for testing when real backend is down
export const MOCK_PROJECT_INFO = {
  project: {
    _id: "6573cde3e3d8b75b1a04",
    name: "Test Project",
    projectCode: "TP001",
    description: "Test project description",
    status: "active"
  },
  members: [
    {
      id: "1",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      email: "nguy<PERSON><PERSON>@gmail.com",
      avatar: null,
      department: "IT",
      position: "Frontend Developer",
      tasks: { waiting: 2, inProgress: 3, completed: 5, review: 1 },
      efficiency: 75,
      totalTasks: 11
    },
    {
      id: "2", 
      name: "<PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      avatar: null,
      department: "IT",
      position: "Backend Developer", 
      tasks: { waiting: 1, inProgress: 2, completed: 8, review: 0 },
      efficiency: 85,
      totalTasks: 11
    },
    {
      id: "3",
      name: "<PERSON><PERSON>", 
      email: "<EMAIL>",
      avatar: null,
      department: "IT",
      position: "Full Stack Developer",
      tasks: { waiting: 0, inProgress: 4, completed: 6, review: 2 },
      efficiency: 80,
      totalTasks: 12
    }
  ]
};

export const MOCK_TASK_STATS = {
  pending: 3,      // Đang chờ
  in_progress: 9,  // Đang tiến hành  
  completed: 19,   // Hoàn thành
  overdue: 2,      // Quá hạn
  review: 3        // Xem xét
};

// Mock API functions
export const mockGetProjectInfo = async (projectId) => {
  console.log("Mock: Getting project info for", projectId);
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return MOCK_PROJECT_INFO;
};

export const mockGetTaskStatusStats = async (projectId) => {
  console.log("Mock: Getting task stats for", projectId);
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));
  return { data: MOCK_TASK_STATS };
};
