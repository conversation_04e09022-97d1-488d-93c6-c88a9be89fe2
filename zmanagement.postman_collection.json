{"info": {"_postman_id": "9ca33d94-e900-4418-9502-c3adcbb4dc44", "name": "zmanagement", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40699722", "_collection_link": "https://aa2222-1213.postman.co/workspace/API-DEV~1012fe5b-7e16-4eb0-8362-3e49859440bd/collection/40699722-9ca33d94-e900-4418-9502-c3adcbb4dc44?action=share&source=collection_link&creator=40699722"}, "item": [{"name": "Admin", "item": [{"name": "auth admin", "item": [{"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Admin123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "đăng nhập 1", "request": {"method": "POST", "header": [{"key": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Admin123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"sanh<PERSON><PERSON><PERSON><PERSON>@gmail.com\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/forgot-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "forgot-password"]}}, "response": []}, {"name": "đặt lại mk", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"password\":\"Sanh123@\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/reset-password/55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "reset-password", "55d0f390e5e9f7c7c9b69ff043a03a25c15f47ea76b215da0ed05591a37b6a39"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t trang cá nhân", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.2H9L19Sbn9fdlx3TKzU-BWjv88lP0bE1bf6KgOjA_zE", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "\r\n   {\r\n  \"fullName\": \"<PERSON><PERSON> Tấn Sanh Update\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t avatar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "avatar", "type": "file", "src": "postman-cloud:///1f053f9b-f77d-48b0-ba0d-cd5ab4868003"}]}, "url": {"raw": "http://localhost:3000/api/auth/update-avatar", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "update-avatar"]}}, "response": []}, {"name": "Đổi mk", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.FMNdQEzG7NyRy1884ZDpKkeEWxClsR2emTOdIQQGG-U", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n\r\n  \"currentPassword\": \"Admin123!!!\",\r\n  \"newPassword\": \"Admin123!\",\r\n  \"confirmPassword\": \"Admin123!\"\r\n\r\n}\r\n\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/change-password", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "change-password"]}}, "response": []}, {"name": "Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.b8TujLh4-Qkypk2Nvc6Kdp0sC-tLLPX6ABnPBe5TU3w", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile"]}}, "response": []}, {"name": "Profile id", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/profile/687070664ab99ac8eabf3e68", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "profile", "687070664ab99ac8eabf3e68"]}}, "response": []}, {"name": "dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "dashboard"]}}, "response": []}, {"name": "b<PERSON>o trì", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"enabled\": false,\r\n  \"message\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/admin/maintenance/toggle", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "toggle"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> tra", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/maintenance/status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "maintenance", "status"]}}, "response": []}, {"name": "thống kê hệ thống", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/system-stats", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "system-stats"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> động", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "activities"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> xu<PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiODBjOGM1MjUzMmQ5MmZhN2UzNjYwYTFlOGEzZjJhNjIiLCJpYXQiOjE3NTI0Nzk0OTUsImV4cCI6MTc1MjUwODI5NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.5pUmHNH_AE_rBHUTmshcHe2nNfsKUYgDKbp4NB0bfBo", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"sanh<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\r\n    \"password\": \"Hts12345@\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/auth/logout", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "logout"]}}, "response": []}, {"name": "check session", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiYmY2YWNiMjRkMWQzMzRiZDAwZWJiNTE5MDY4NjU0ZDciLCJpYXQiOjE3NTI0ODAyOTksImV4cCI6MTc1MjUwOTA5OSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.EPH1qWyhdWAhH6gBp31m0xLiiDN9v7qN6APFjMlCPvM", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/auth/session-monitor", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "session-monitor"]}}, "response": []}, {"name": "status session", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiYmY2YWNiMjRkMWQzMzRiZDAwZWJiNTE5MDY4NjU0ZDciLCJpYXQiOjE3NTI0ODAyOTksImV4cCI6MTc1MjUwOTA5OSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.EPH1qWyhdWAhH6gBp31m0xLiiDN9v7qN6APFjMlCPvM", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/auth/session-status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "session-status"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiZGE0OTNkMDE3YjgyNjgwOWVkMjIyZTE1YjM4OWUwYzMiLCJpYXQiOjE3NTI4NDkxODcsImV4cCI6MTc1Mjg3Nzk4NywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.v26_XAA4NTiHeMZWKcjFUBYMexp5s5ydYMTR07VF4qk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiY2NmZGY4NjRkMWVlOGZhNTFkY2Y4ZDc4MDk4ODA3MjMiLCJpYXQiOjE3NTI4NDgxMzAsImV4cCI6MTc1Mjg3NjkzMCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.TvRmnojJa6RQCv5MxqHEskjGJVBgsKZLjdDNr0IJ4u4", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON><PERSON><PERSON> ngh<PERSON>\",\r\n  \"code\": \"IT01\",\r\n  \"headEmployeeCode\": \"NV022\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> nhật phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiM2JjY2YyNDA4Y2U1YjRjNWZmNTg2ZjljNjFiMTYwNTUiLCJpYXQiOjE3NTMyNjY5NjIsImV4cCI6MTc1MzI5NTc2MiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.eMs1dbMe03Mp46iKQl2m-cNYCBRzr54Y9xanjmwM01U", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n//   \"name\": \"Thực tập 3 Update\",\r\n//   \"code\": \"TT3\",\r\n  \"headEmployeeCode\": \"ADM001\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/departments/687bb20ebe83755210ffd54f", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "687bb20ebe83755210ffd54f"]}}, "response": []}, {"name": "xóa phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/departments/686f7ae627d52aadcb6c8cf8", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "departments", "686f7ae627d52aadcb6c8cf8"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "item": [{"name": "lấy ds user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.X4rJbUMF0X7kCbBro5bU3dFh5qMKR-MTCdeTCBTmE54", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiZGE0OTNkMDE3YjgyNjgwOWVkMjIyZTE1YjM4OWUwYzMiLCJpYXQiOjE3NTI4NDkxODcsImV4cCI6MTc1Mjg3Nzk4NywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.v26_XAA4NTiHeMZWKcjFUBYMexp5s5ydYMTR07VF4qk", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"fullName\": \"Sanh đẹp trai\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"role\": \"staff\",\r\n  \"position\": \"BE \",\r\n  \"departmentCode\": \"IT01\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiN2ZlY2U5Njg2YmQ3MzkzNmU2MmRiNTViMzQ2NGVhNDQiLCJpYXQiOjE3NTMyNjY2NjcsImV4cCI6MTc1MzI5NTQ2NywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.d5rK0UObhNYtbq0j-MbnqBm9OxbFpuiyQeIHYPQkULw", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"fullName\": \"Trưởng nhóm TT\",\r\n  \"role\": \"staff\",\r\n  \"position\": \"Test 22\",\r\n  \"password\":\"Sanh12345@\"\r\n  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/686f9643b5a8083cb5fbad89", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "686f9643b5a8083cb5fbad89"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> ho<PERSON> động của user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTQzNjc2NTA1LCJpYXQiOjE3NTIxNDM2NzYsImV4cCI6MTc1MjE3MjQ3NiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/activities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "activities"]}}, "response": []}, {"name": "<PERSON><PERSON> t<PERSON> hàng lo<PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.FMNdQEzG7NyRy1884ZDpKkeEWxClsR2emTOdIQQGG-U", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"action\": \"block\",\r\n  \"userIds\": [\"6875df2ed40b23f72426f3cc\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/bulk", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "bulk"]}}, "response": []}, {"name": "Khóa/Mở Khóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************.FMNdQEzG7NyRy1884ZDpKkeEWxClsR2emTOdIQQGG-U", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"reason\":\"hihi\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/6875df2ed40b23f72426f3cc/toggle-block", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "6875df2ed40b23f72426f3cc", "toggle-block"]}}, "response": []}, {"name": "Thay role User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"newRole\": \"hr\"\r\n\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/687070664ab99ac8eabf3e68/change-role", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "687070664ab99ac8eabf3e68", "change-role"]}}, "response": []}, {"name": "Xóa user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.40daTmFoFNcwI674ZRfB_fAS0ZLcAe9r8OqSx0WsC54", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/68760eb7f870a5988ecf1272", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "68760eb7f870a5988ecf1272"]}}, "response": []}, {"name": "lấy ds user đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************.40daTmFoFNcwI674ZRfB_fAS0ZLcAe9r8OqSx0WsC54", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/users/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTQzNjc2NTA1LCJpYXQiOjE3NTIxNDM2NzYsImV4cCI6MTc1MjE3MjQ3NiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "restore"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> v<PERSON>nh vi<PERSON><PERSON>(admin có thể khôi phục)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTQzNjc2NTA1LCJpYXQiOjE3NTIxNDM2NzYsImV4cCI6MTc1MjE3MjQ3NiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/users/users/686f9a4a664433e623c806fe/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "users", "686f9a4a664433e623c806fe", "permanent"]}}, "response": []}, {"name": "xem ds user x<PERSON><PERSON> v<PERSON><PERSON> v<PERSON><PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTQzNjc2NTA1LCJpYXQiOjE3NTIxNDM2NzYsImV4cCI6MTc1MjE3MjQ3NiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "permanently-deleted"]}}, "response": []}, {"name": "kh<PERSON>i phục user đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTQzNjc2NTA1LCJpYXQiOjE3NTIxNDM2NzYsImV4cCI6MTc1MjE3MjQ3NiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.CtpjN7IUZbbqK_dBgDEeToP_E5dhFHTjIIdb8tN0oT0", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/users/686f9a4a664433e623c806fe/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "users", "686f9a4a664433e623c806fe", "restore-permanent"]}}, "response": []}, {"name": "lấy ds thành viên cũng phòng ban", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.rNfB7YawPdo9_Rm9MC66u9ktEixg7BfFChjPOMjS4XE", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/department/6868a859ba2f43313f4e26b9/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "department", "6868a859ba2f43313f4e26b9", "members"]}}, "response": []}, {"name": "lấy ds thành viên cùng dự án", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/users/projects/686fec1862ead3ae888c0c87/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "users", "projects", "686fec1862ead3ae888c0c87", "members"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON>n", "item": [{"name": "<PERSON><PERSON><PERSON> danh sách d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "l<PERSON>y chi tiết dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87"]}}, "response": []}, {"name": "tạo dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiZTJkMGUwOWFjY2MzMDVkZjIzMDA3MDhlZDYxOGMwYzkiLCJpYXQiOjE3NTMyNTU5MDYsImV4cCI6MTc1MzI4NDcwNiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.dm0cxlUEG7tZwIuGvK9HEiT_X5oKlxPtX0Nl8ZdyjUg", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON>ự án xóa, khôi phục\",\r\n  \"description\": \"test Dự án \",\r\n  \"departmentId\": \"687a57544e3a480b780b146e\",\r\n  \"leaderId\": \"68760de9f870a5988ecf0f1c\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-02-01\",\r\n  \"endDate\": \"2025-08-31\",\r\n//   \"members\": [\r\n//     {\r\n//       \"userId\": \"687a5bdc4e3a480b780b1488\"\r\n//     }\r\n//   ],\r\n  \"followers\": [\"68760dc2f870a5988ecf0eff\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects"]}}, "response": []}, {"name": "sửa d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"<PERSON><PERSON> thống quản lý nhân sự\",\r\n  \"description\": \"update\",\r\n  \"departmentId\": \"686f9a4a664433e623c806fe\",\r\n  \"leaderId\": \"686f9a4a664433e623c806fe\",\r\n  \"status\": \"waiting\",\r\n  \"priority\": \"high\",\r\n  \"startDate\": \"2025-02-01\",\r\n  \"endDate\": \"2025-08-31\",\r\n  \"members\": [\r\n    {\r\n      \"userId\": \"686fce5d2d2e14696319d49b\"\r\n    }\r\n  ],\r\n  \"followers\": [\"686f98f295971bf7bf2e62ff\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fd0e7a3461d8d61e9bc12", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fd0e7a3461d8d61e9bc12"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON> d<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiYTQ0MTc1M2MyZWU2MjQ3ODBiNzliZDI0MTUyNGI3ZGMiLCJpYXQiOjE3NTMwOTU5MjMsImV4cCI6MTc1MzEyNDcyMywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0._JGafvaBG0vmonG054idFlEq3Eru0z724Hze1cVh80Q", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"reason\": \"Dự án bị hủy\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/687e1f21c978a83f571a1a91", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687e1f21c978a83f571a1a91"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiYTQ0MTc1M2MyZWU2MjQ3ODBiNzliZDI0MTUyNGI3ZGMiLCJpYXQiOjE3NTMwOTU5MjMsImV4cCI6MTc1MzEyNDcyMywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0._JGafvaBG0vmonG054idFlEq3Eru0z724Hze1cVh80Q", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/687e1f21c978a83f571a1a91/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687e1f21c978a83f571a1a91", "permanent"]}}, "response": []}, {"name": "xem ds đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiMjBkNWJhYmE5ZjExZTM2ZjYxYTc4YjJiZTE1YTQ2NjgiLCJpYXQiOjE3NTMwOTQ1NDUsImV4cCI6MTc1MzEyMzM0NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0._9qpgYIt2OzuDuv33vLWKBfbl0B3vhrsSc0DRu2iVZM", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/projects/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phục dự án đã xóa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiMjBkNWJhYmE5ZjExZTM2ZjYxYTc4YjJiZTE1YTQ2NjgiLCJpYXQiOjE3NTMwOTQ1NDUsImV4cCI6MTc1MzEyMzM0NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0._9qpgYIt2OzuDuv33vLWKBfbl0B3vhrsSc0DRu2iVZM", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/687e13e3cee82bf8e89c0f70/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687e13e3cee82bf8e89c0f70", "restore"]}}, "response": []}, {"name": "xem ds project xó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiMjBkNWJhYmE5ZjExZTM2ZjYxYTc4YjJiZTE1YTQ2NjgiLCJpYXQiOjE3NTMwOTQ1NDUsImV4cCI6MTc1MzEyMzM0NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0._9qpgYIt2OzuDuv33vLWKBfbl0B3vhrsSc0DRu2iVZM", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/permanently-deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "permanently-deleted"]}}, "response": []}, {"name": "khôi phục project đã xóa vĩnh viễn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/admin/projects/686fe81eefc48f312ece3bfb/restore-permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "projects", "686fe81eefc48f312ece3bfb", "restore-permanent"]}}, "response": []}, {"name": "tải file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "postman-cloud:///1f048bf1-168f-4550-b70d-91f87674bf0d"}]}, "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "upload"]}}, "response": []}, {"name": "Xem file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiOTk4M2NjZTE1NmNlNTViMzJjNWFmMmQ3NDNkNWU0ZDMiLCJpYXQiOjE3NTI3NDM0NzMsImV4cCI6MTc1Mjc3MjI3MywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.k2M-sLILuZFAKujzYdqwdbafWlxd6Vk5YMWfwURFxwA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/6876767066ade300fce8f452/files", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6876767066ade300fce8f452", "files"]}}, "response": []}, {"name": "down file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiOTk4M2NjZTE1NmNlNTViMzJjNWFmMmQ3NDNkNWU0ZDMiLCJpYXQiOjE3NTI3NDM0NzMsImV4cCI6MTc1Mjc3MjI3MywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.k2M-sLILuZFAKujzYdqwdbafWlxd6Vk5YMWfwURFxwA", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/6876767066ade300fce8f452/files/6878c3aa15f93f332c6384ab/download", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6876767066ade300fce8f452", "files", "6878c3aa15f93f332c6384ab", "download"]}}, "response": []}, {"name": "xóa file", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/projects/686fe84eefc48f312ece3c22/files/6870a9e9766d80861a6191d9", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fe84eefc48f312ece3c22", "files", "6870a9e9766d80861a6191d9"]}}, "response": []}, {"name": "thêm thành viên vào dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiZTJkMGUwOWFjY2MzMDVkZjIzMDA3MDhlZDYxOGMwYzkiLCJpYXQiOjE3NTMyNTU5MDYsImV4cCI6MTc1MzI4NDcwNiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.dm0cxlUEG7tZwIuGvK9HEiT_X5oKlxPtX0Nl8ZdyjUg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userId\": \"687a5bdc4e3a480b780b1488\"\r\n  \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6880900cc9e344c0fc81f238/members", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6880900cc9e344c0fc81f238", "members"]}}, "response": []}, {"name": "x<PERSON>a thành viên", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/members/686fce5d2d2e14696319d49b", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "members", "686fce5d2d2e14696319d49b"]}}, "response": []}, {"name": "lấy ds công việc trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks"]}}, "response": []}, {"name": "tạo cv trong dự án", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiZTJkMGUwOWFjY2MzMDVkZjIzMDA3MDhlZDYxOGMwYzkiLCJpYXQiOjE3NTMyNTU5MDYsImV4cCI6MTc1MzI4NDcwNiwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.dm0cxlUEG7tZwIuGvK9HEiT_X5oKlxPtX0Nl8ZdyjUg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \r\n  \"title\": \"Task 111\",\r\n  \"description\": \"<PERSON><PERSON> tả task111111\",\r\n  \"assignedToIds\": [\"68774a42a07c78b06129f09c\",\"687a5bdc4e3a480b780b1488\"],\r\n  \"priority\": \"high\",\r\n  \"status\": \"pending\",\r\n  \"dueDate\": \"2025-07-10\",\r\n  \"startDate\": \"2025-07-15\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/6880900cc9e344c0fc81f238/tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "6880900cc9e344c0fc81f238", "tasks"]}}, "response": []}, {"name": "cậ<PERSON> nh<PERSON>t tiến độ cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.XXB6vivx3TJVrTcHHQs2qgjuEZusF6Hek85HxKUwP_M", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"status\": \"in_progress\",\r\n  \"progress\": 50,\r\n  \"description\": \"<PERSON><PERSON><PERSON> nhật mô tả công việc\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/687a5cd64e3a480b780b14bf/tasks/687a5d644e3a480b780b14d5", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687a5cd64e3a480b780b14bf", "tasks", "687a5d644e3a480b780b14d5"]}}, "response": []}, {"name": "b<PERSON><PERSON> lu<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.jRpGY33jarSn9Iqddd2-uvfQ7wJma4YNTHuFDrQQfCc", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \"content\": \"<PERSON><PERSON> hoàn thành 100% công việc này\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/687a5cd64e3a480b780b14bf/tasks/687a5d644e3a480b780b14d5/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687a5cd64e3a480b780b14bf", "tasks", "687a5d644e3a480b780b14d5", "comments"]}}, "response": []}, {"name": "l<PERSON>y ds bình luận", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708fd3036b9c5a2280410c/comments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708fd3036b9c5a2280410c", "comments"]}}, "response": []}, {"name": "x<PERSON><PERSON> bl", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.jRpGY33jarSn9Iqddd2-uvfQ7wJma4YNTHuFDrQQfCc", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/projects/687a5cd64e3a480b780b14bf/tasks/687a5d644e3a480b780b14d5/comments/687a62bf77e15439f92b0b6e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "687a5cd64e3a480b780b14bf", "tasks", "687a5d644e3a480b780b14d5", "comments", "687a62bf77e15439f92b0b6e"]}}, "response": []}, {"name": "xóa task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18"]}}, "response": []}, {"name": "l<PERSON><PERSON> danh s<PERSON>ch task đã xóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/deleted", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "deleted"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> phụ<PERSON> task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "restore"]}}, "response": []}, {"name": "xóa vv task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMjA1NzY4NzIyLCJpYXQiOjE3NTIyMDU3NjgsImV4cCI6MTc1MjIzNDU2OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.8lRpJ1T5jj6w7BToMWRsOj9uCzMuZeA_MfQsio2fI_s", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/projects/686fec1862ead3ae888c0c87/tasks/68708dcbbd222034828f4c18/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "686fec1862ead3ae888c0c87", "tasks", "68708dcbbd222034828f4c18", "permanent"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTUyODY1MTAyLCJpYXQiOjE3NTIxNTI4NjUsImV4cCI6MTc1MjE4MTY2NSwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.D26NEwLmlAvWfZrcuf4Agga84BF8mfqZ5QHUbeG5k-4", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/tasks/686fed6662ead3ae888c0cbf/stats/overview", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "tasks", "686fed6662ead3ae888c0cbf", "stats", "overview"]}}, "response": []}, {"name": "l<PERSON><PERSON> công việ<PERSON> trong tất cả da", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.jRpGY33jarSn9Iqddd2-uvfQ7wJma4YNTHuFDrQQfCc", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/projects/my-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "my-tasks"]}}, "response": []}, {"name": "tải file task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "postman-cloud:///1f070e45-f17b-4910-993e-a6a925bb3a13"}]}, "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks/6890246bb91a54371e2bbccb/attachments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks", "6890246bb91a54371e2bbccb", "attachments"]}}, "response": []}, {"name": "lấy file task", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/<PERSON><PERSON>o cáo công vi<PERSON>c đầu ngày 5-7.docx"}]}, "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks/6890246bb91a54371e2bbccb/attachments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks", "6890246bb91a54371e2bbccb", "attachments"]}}, "response": []}, {"name": "xóa file task", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "DELETE", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/<PERSON><PERSON>o cáo công vi<PERSON>c đầu ngày 5-7.docx"}]}, "url": {"raw": "http://localhost:3000/api/projects/688ad167a5b4df27c9622c82/tasks/6890246bb91a54371e2bbccb/attachments/6890296cf26823cfe9418b13", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "projects", "688ad167a5b4df27c9622c82", "tasks", "6890246bb91a54371e2bbccb", "attachments", "6890296cf26823cfe9418b13"]}}, "response": []}, {"name": "thống kê cv trong da", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.T6r9KEUjULQnTmCQhoFx3H-Fe0yewJ5LNmJWhRX_74A", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/C:/Users/<USER>/Downloads/<PERSON><PERSON>o cáo công vi<PERSON>c đầu ngày 5-7.docx"}]}, "url": {"raw": "http://localhost:3001/api/projects/687a5cd64e3a480b780b14bf/tasks/stats", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "projects", "687a5cd64e3a480b780b14bf", "tasks", "stats"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON><PERSON> lý công vi<PERSON>c cá nhân", "item": [{"name": "Tạo cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.nG1SPiYAXB654yCL9UkWwyx0AEUUklfc_yDPD0iZmws", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Công việc 1\",\r\n  \"description\": \"test 1\"\r\n  \r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "tải file cvcn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "postman-cloud:///1f05985c-d396-4ae0-a1e0-ca48ff46857c"}]}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/689015b3bb86f9303751befc/upload", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "689015b3bb86f9303751befc", "upload"]}}, "response": []}, {"name": "Xem file cvcn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/common/personal-tasks/689015b3bb86f9303751befc/files", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "689015b3bb86f9303751befc", "files"]}}, "response": []}, {"name": "down file cvcn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/common/personal-tasks/689015b3bb86f9303751befc/files/6890273777834052e83e0d28/download", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "689015b3bb86f9303751befc", "files", "6890273777834052e83e0d28", "download"]}}, "response": []}, {"name": "xóa file cvcn", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.F8T6g9S6ZpXuC36jRu_KTEoH497Uq75NN0QbuMkY5L8", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/689015b3bb86f9303751befc/files/689026399d37f17b43f3bb52", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "689015b3bb86f9303751befc", "files", "689026399d37f17b43f3bb52"]}}, "response": []}, {"name": "lấy cv", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.nG1SPiYAXB654yCL9UkWwyx0AEUUklfc_yDPD0iZmws", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks"]}}, "response": []}, {"name": "l<PERSON>y cv chi tiết", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "sửa cv", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"title\":\"Update\",\r\n  \"description\": \"<PERSON><PERSON>n thành báo cáo tháng - Updated\"\r\n\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "Xóa cv cá nhân vào thùng rác", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d"]}}, "response": []}, {"name": "xem ds thùng r<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/common/personal-tasks/trashed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "trashed"]}}, "response": []}, {"name": "kh<PERSON><PERSON> phục", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************.Ek1RN1-usrKnVyQ-8BiEy4VUpBEuWbOtUfn-2BfcTqU", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/staff/personal-tasks/6868bac3d6aff09c1d5d01c8/restore", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "staff", "personal-tasks", "6868bac3d6aff09c1d5d01c8", "restore"]}}, "response": []}, {"name": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "http://localhost:3000/api/common/personal-tasks/68706e274ab99ac8eabf3e2d/permanent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "personal-tasks", "68706e274ab99ac8eabf3e2d", "permanent"]}}, "response": []}]}, {"name": "qu<PERSON><PERSON> lý email", "item": [{"name": "trạng thái email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/admin/email-status", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "email-status"]}}, "response": []}, {"name": "retry thủ công", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "http://localhost:3000/api/admin/retry-emails", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "retry-emails"]}}, "response": []}, {"name": "d<PERSON><PERSON> log email", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OYu8MKYxT3kGVxDKp3N09b1FVt3fBE2btcABa93N_xs", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/admin/clean-email-logs", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "admin", "clean-email-logs"]}}, "response": []}]}, {"name": "t<PERSON><PERSON> k<PERSON>m", "item": [{"name": "tìm kiếm với từ khóa", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiMjZiMWEyMzg3NDc1MjA4MTVjODEzZGMwYmEwOTVmMWEiLCJpYXQiOjE3NTI2NDY2NjcsImV4cCI6MTc1MjY3NTQ2NywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.1O4DMTKpVLNb1R3gcbZ9LSX7fk3HlBt5_BxCnwUnWwU", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search?query=Test", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search"], "query": [{"key": "query", "value": "Test"}]}}, "response": []}, {"name": "l<PERSON><PERSON> sử tìm kiếm", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}, {"name": "tìm kiếm nâng cao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoiMjZiMWEyMzg3NDc1MjA4MTVjODEzZGMwYmEwOTVmMWEiLCJpYXQiOjE3NTI2NDY2NjcsImV4cCI6MTc1MjY3NTQ2NywiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.1O4DMTKpVLNb1R3gcbZ9LSX7fk3HlBt5_BxCnwUnWwU", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"query\": \"test thông báo\",\r\n//   \"filters\": {\r\n//     \"type\": \"users\",\r\n//     \"departmentId\": \"6868a859ba2f43313f4e26b9\" \r\n//   },\r\n  \"limit\": 10,\r\n  \"page\": 1,\r\n  \"sortBy\": \"relevance\"\r\n}"}, "url": {"raw": "http://localhost:3000/api/common/search/advanced", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "advanced"]}}, "response": []}, {"name": "x<PERSON><PERSON> lịch sử tìm kiếm", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NmY5NjQzYjVhODA4M2NiNWZiYWQ4OSIsInJvbGUiOiJhZG1pbiIsImVtYWlsIjoiYWRtaW5AZ21haWwuY29tIiwic2Vzc2lvbklkIjoxNzUyMTk3Mzk4NTM4LCJpYXQiOjE3NTIxOTczOTgsImV4cCI6MTc1MjIyNjE5OCwiYXVkIjoiYWRtaW4tcGFuZWwiLCJpc3MiOiJwcm9qZWN0LW1hbmFnZW1lbnQtYXBpIn0.jN_jxHfkzPrv65_KtlyTRO1PFxNRaQm339siUlt4eAI", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\r\n{\r\n  \"description\": \"<PERSON><PERSON><PERSON> thành báo cáo tháng - Updated\",\r\n  \"notes\": \"<PERSON><PERSON> thu thập xong số li<PERSON>u, đang tổng hợp\"\r\n}\r\n"}, "url": {"raw": "http://localhost:3000/api/common/search/history", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "common", "search", "history"]}}, "response": []}]}, {"name": "thống kê", "item": [{"name": "<PERSON>h<PERSON><PERSON> kê phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/departments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "departments"]}}, "response": []}, {"name": "<PERSON>h<PERSON><PERSON> kê người dùng", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "users"]}}, "response": []}, {"name": "Thống kê project phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/projects-by-department", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "projects-by-department"]}}, "response": []}, {"name": "Thống kê project tất cả phòng ban", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/all-departments-projects", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "all-departments-projects"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> kê <PERSON> đ<PERSON><PERSON><PERSON> giao", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************.c4MHEGYUUmUTu8GjKywJjTNLzQW3WFpsIHnaFhBejyk", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/stats/staff-task-assignments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "stats", "staff-task-assignments"]}}, "response": []}]}, {"name": "thông báo", "item": [{"name": "all thông báo", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification"]}}, "response": []}, {"name": "thông b<PERSON>o ch<PERSON><PERSON> đ<PERSON>c", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/notification/unread-count", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "unread-count"]}}, "response": []}, {"name": "thông báo theo lo<PERSON>i", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{// \"task_assigned\",\r\n//     \"task_completed\",\r\n//     \"task_overdue\",\r\n//     \"project_created\",\r\n//     \"project_updated\",\r\n//     \"project_completed\",\r\n//     \"system\",\r\n//     \"reminder\",\r\n//     \"announcement\",\r\n//     \"other\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/notification/type/task_completed", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "type", "task_completed"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-read/687654c866ade300fce8c81e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-read", "687654c866ade300fce8c81e"]}}, "response": []}, {"name": "đ<PERSON><PERSON> dấu đã đọc tất cả", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "PUT", "header": [], "url": {"raw": "http://localhost:3000/api/notification/mark-all-read", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "mark-all-read"]}}, "response": []}, {"name": "xóa all thông báo đã đọc", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************.Cz1T4fAeyUT8Ul_R9t16s1ciRDDqwnnONDnbUoq0Vno", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/delete-read/all", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "delete-read", "all"]}}, "response": []}, {"name": "x<PERSON>a thông b<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.OikkFAMAjpx2iaKTcsD9NqXxKib6OYj73dwkdazbbMI", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:3000/api/notification/68709cd0a34e47cafe63f03e", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "notification", "68709cd0a34e47cafe63f03e"]}}, "response": []}]}]}, {"name": "<PERSON><PERSON><PERSON>p", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Admin123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3001/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3001", "path": ["api", "auth", "login"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"Leader123!\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "auth", "login"]}}, "response": []}]}