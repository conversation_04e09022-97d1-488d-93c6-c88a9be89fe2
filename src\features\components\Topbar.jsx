import React, { useState, useEffect, useRef } from 'react';
import '../../styles/Topbar.css';
import searchIcon from '../../assets/search.svg';
import accountIcon from '../../assets/profile.svg';
import lockIcon from '../../assets/lock.svg';
import logoutIcon from '../../assets/logout.svg';
import notiIcon from '../../assets/Noti.svg';
import Profile from './Profile';
import Notification from './Notification';
import Search from './Search';
import LogoutConfirmation from '../../components/LogoutConfirmation.jsx';
import user1 from '../../assets/user1.png';

const DashboardTopbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showSearchResult, setShowSearchResult] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const menuRef = useRef(null);
  const avatarRef = useRef(null);
  const notificationRef = useRef(null);
  const notificationBtnRef = useRef(null);

  // Đóng menu khi click ra ngoài
  useEffect(() => {
    function handleClickOutside(event) {
      // Handle user menu dropdown
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        avatarRef.current &&
        !avatarRef.current.contains(event.target)
      ) {
        setMenuOpen(false);
      }
      
      // Handle notification dropdown - only close if click is outside both the dropdown and its trigger button
      if (
        showNotification &&
        notificationRef.current &&
        !notificationRef.current.contains(event.target) &&
        notificationBtnRef.current &&
        !notificationBtnRef.current.contains(event.target)
      ) {
        setShowNotification(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotification]);

  // Hiển thị kết quả tìm kiếm ngay khi nhập
  useEffect(() => {
    if (searchValue && searchValue.trim() !== "") {
      setShowSearchResult(true);
    } else {
      setShowSearchResult(false);
    }
  }, [searchValue]);

  // Xử lý đăng xuất với xác nhận
  const handleLogout = () => {
    setShowLogoutConfirm(true);
  };

  const handleLogoutConfirm = () => {
    setShowLogoutConfirm(false);
    // Cleanup sẽ được xử lý bởi LogoutConfirmation component
  };

  // Hiện form Profile
  const handleShowProfile = () => {
    setShowProfile(true);
    setMenuOpen(false);
  };

  // Đóng form Profile
  const handleCloseProfile = () => {
    setShowProfile(false);
  };

  // Xử lý submit tìm kiếm
  const handleSearchSubmit = (e) => {
    e.preventDefault();
  };

  // Đóng form Search
  const handleCloseSearch = () => {
    setShowSearchResult(false);
    setSearchValue("");
  };

  // Toggle notification dropdown
  const toggleNotification = (e) => {
    e.stopPropagation();
    setShowNotification(prev => !prev);
    // Close other dropdowns if open
    if (menuOpen) {
      setMenuOpen(false);
    }
  };

  // Get current user avatar from localStorage
  const getCurrentUserAvatar = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      // Support both { user: {...} } and direct user object
      const user = userRaw.user || userRaw;
      return user.avatar && user.avatar !== '' ? user.avatar : user1;
    } catch {
      return user1;
    }
  };

  return (
    <div className="dashboard-topbar">
      {/* Form tìm kiếm trên topbar */}
      <form className="dashboard-search-form" onSubmit={handleSearchSubmit}>
        <div className="dashboard-search-input-wrapper">
          <input
            className="dashboard-search-input"
            type="text"
            placeholder="Tìm kiếm"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            onFocus={() => setShowSearchResult(false)}
          />
          {/* Icon tìm kiếm */}
          <img
            src={searchIcon}
            alt="search"
            className="dashboard-search-icon"
            onClick={handleSearchSubmit}
          />
        </div>
      </form>
      {/* Notification icon */}
      <div
        className="dashboard-notification-wrapper"
        ref={notificationRef}
        style={{ position: 'relative' }}
      >
        <img
          ref={notificationBtnRef}
          src={notiIcon}
          alt="notifications"
          className={`dashboard-notification-icon ${showNotification ? 'active' : ''}`}
          onClick={toggleNotification}
        />
        {/* Notification dropdown */}
        {showNotification && (
          <div className="notification-dropdown-wrapper" onClick={(e) => e.stopPropagation()}>
            <Notification />
          </div>
        )}
      </div>
      {/* Khu vực hiển thị avatar người dùng */}
      <div
        className="dashboard-user-avatar-wrapper"
        ref={avatarRef}
        onClick={() => setMenuOpen((open) => !open)}
        style={{ cursor: 'pointer', position: 'relative' }}
      >
        <div className="dashboard-user-avatar">
          <img
            src={getCurrentUserAvatar()}
            alt="User Avatar"
            className="dashboard-user-avatar-img"
          />
        </div>
        {/* Icon mũi tên chỉ xuống để mở rộng menu người dùng */}
        <span className="dashboard-user-avatar-arrow material-icons">arrow_drop_down</span>
        {/* Dropdown menu */}
        {menuOpen && (
          <div
            ref={menuRef}
            className="dashboard-user-menu"
          >
            <div className="dashboard-user-menu-item" onClick={handleShowProfile}>
              <img src={accountIcon} alt="account" className="dashboard-user-menu-icon account" />
              Hồ sơ cá nhân
            </div>
            <div className="dashboard-user-menu-item"
              onClick={handleLogout}
            >
              <img src={logoutIcon} alt="logout" className="dashboard-user-menu-icon logout" />
              Đăng xuất
            </div>
          </div>
        )}
      </div>
      {/* Form Profile với overlay */}
      {showProfile && (
        <div className="profile-modal-overlay" onClick={handleCloseProfile}>
          <div className="profile-modal-content" onClick={(e) => e.stopPropagation()}>
            <Profile onClose={handleCloseProfile} />
            <button className="profile-modal-close" onClick={handleCloseProfile}>
              &times;
            </button>
          </div>
        </div>
      )}
      {/* Hiển thị form Search khi có kết quả */}
      {showSearchResult && (
        <div style={{
          position: "absolute",
          top: "60px", // điều chỉnh theo chiều cao topbar
          left: 0,
          right: 0,
          zIndex: 100,
          padding: "0 24px"
        }}>
          <Search onClose={handleCloseSearch} searchValue={searchValue} />
        </div>
      )}

      <LogoutConfirmation
        show={showLogoutConfirm}
        message="Bạn có chắc chắn muốn đăng xuất khỏi hệ thống?"
        title="Xác nhận đăng xuất"
        onConfirm={handleLogoutConfirm}
        onClose={() => setShowLogoutConfirm(false)}
      />
    </div>
  );
};

// Export component để sử dụng ở nơi khác
export default DashboardTopbar;