const PersonalTask = require("../models/PersonalTaskModel");
const asyncHandler = require("express-async-handler");
const { createError } = require("../middleware/errorMiddleware");
const { formatVietnamTime, addLocalTime } = require("../utils/timeUtils");
const {
  formatResponse,
  successResponse,
  errorResponse,
} = require("../utils/responseHelper");
const cloudinary = require("cloudinary").v2;
const crypto = require("crypto");

// Cấu hình Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Helper function để tạo signed URL cho Cloudinary
const createSignedUrl = (publicId, options = {}) => {
  const timestamp = Math.round(new Date().getTime() / 1000);
  const expiresAt = options.expires_at || timestamp + 3600;

  // Tạo signature
  const stringToSign = `public_id=${publicId}&timestamp=${timestamp}${process.env.CLOUDINARY_API_SECRET}`;
  const signature = crypto
    .createHash("sha1")
    .update(stringToSign)
    .digest("hex");

  // Tạo URL đúng định dạng
  return `https://res.cloudinary.com/${process.env.CLOUDINARY_CLOUD_NAME}/raw/upload/${publicId}?expires_at=${expiresAt}&timestamp=${timestamp}&signature=${signature}`;
};

// Helper function để format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Đã bỏ notification cho personal task vì không cần thiết (chỉ thông báo cho chính người tạo)

// Tạo task cá nhân mới
exports.createPersonalTask = asyncHandler(async (req, res) => {
  const { title, description } = req.body;

  // Kiểm tra các trường bắt buộc
  if (!title) {
    return res.status(400).json(errorResponse("Tiêu đề là bắt buộc"));
  }

  const task = await PersonalTask.create({
    userId: req.user._id,
    title,
    description: description || "",
  });

  // Gửi thông báo cho chính người tạo
  // Không cần notification cho personal task (chỉ thông báo cho chính người tạo)

  res
    .status(201)
    .json(
      successResponse(
        addLocalTime(task.toObject(), ["createdAt", "updatedAt"]),
        "Tạo công việc cá nhân thành công"
      )
    );
});

// Lấy danh sách task cá nhân
exports.getPersonalTasks = asyncHandler(async (req, res) => {
  const { search, page = 1, limit = 10, sort = "-createdAt" } = req.query;

  // Xây dựng điều kiện query
  const query = {
    userId: req.user._id,
    isDeleted: false,
  };

  if (search) {
    query.$or = [
      { title: { $regex: search, $options: "i" } },
      { description: { $regex: search, $options: "i" } },
    ];
  }

  const skip = (page - 1) * limit;
  const sortObj = {};

  // Parse sort parameter
  if (sort.startsWith("-")) {
    sortObj[sort.substring(1)] = -1;
  } else {
    sortObj[sort] = 1;
  }

  const [tasks, count] = await Promise.all([
    PersonalTask.find(query)
      .sort(sortObj)
      .skip(parseInt(skip))
      .limit(parseInt(limit)),
    PersonalTask.countDocuments(query),
  ]);

  res.status(200).json(
    successResponse(
      {
        tasks:
          addLocalTime(
            tasks.map((task) => task.toObject()),
            ["createdAt", "updatedAt"]
          ) || [],
      },
      "Lấy danh sách công việc cá nhân thành công",
      {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      }
    )
  );
});

// Lấy chi tiết task
exports.getPersonalTaskById = asyncHandler(async (req, res) => {
  const task = await PersonalTask.findOne({
    _id: req.params.id,
    userId: req.user._id,
    isDeleted: false,
  });

  if (!task) {
    return res.status(404).json(errorResponse("Không tìm thấy công việc"));
  }

  res
    .status(200)
    .json(
      successResponse(
        addLocalTime(task.toObject(), ["createdAt", "updatedAt"]),
        "Lấy chi tiết công việc thành công"
      )
    );
});

// Cập nhật task
exports.updatePersonalTask = asyncHandler(async (req, res) => {
  const { title, description } = req.body;

  const task = await PersonalTask.findOne({
    _id: req.params.id,
    userId: req.user._id,
    isDeleted: false,
  });

  if (!task) {
    return res.status(404).json({
      success: false,
      message: "Không tìm thấy công việc",
      data: null,
    });
  }

  const updateData = {};
  if (title !== undefined) updateData.title = title;
  if (description !== undefined) updateData.description = description;

  const updatedTask = await PersonalTask.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  );

  // Không cần notification cho personal task (chỉ thông báo cho chính người cập nhật)

  res.status(200).json({
    success: true,
    message: "Cập nhật công việc thành công",
    data: addLocalTime(updatedTask.toObject(), ["createdAt", "updatedAt"]),
  });
});

// Xóa task (soft delete)
exports.deletePersonalTask = asyncHandler(async (req, res) => {
  const task = await PersonalTask.findOne({
    _id: req.params.id,
    userId: req.user._id,
    isDeleted: false,
  });

  if (!task) {
    return res.status(404).json({
      success: false,
      message: "Không tìm thấy công việc",
    });
  }

  await task.softDelete(req.user._id);

  // Không cần notification cho personal task (chỉ thông báo cho chính người xóa)

  res.status(200).json({
    success: true,
    message: "Xóa công việc thành công",
  });
});

// Thống kê task cá nhân đơn giản
exports.getPersonalTaskStats = asyncHandler(async (req, res) => {
  const userId = req.user._id;

  const totalTasks = await PersonalTask.countDocuments({
    userId,
    isDeleted: false,
  });

  res.status(200).json({
    success: true,
    data: {
      total: totalTasks,
    },
  });
});

// Lấy danh sách công việc đã xóa (thùng rác)
exports.getTrashedPersonalTasks = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, sort = "-deletedAt" } = req.query;

  const query = {
    isDeleted: true,
  };

  if (req.user.role !== "admin") {
    query.userId = req.user._id;
  }

  const skip = (page - 1) * limit;
  const sortObj = {};

  // Parse sort parameter
  if (sort.startsWith("-")) {
    sortObj[sort.substring(1)] = -1;
  } else {
    sortObj[sort] = 1;
  }

  const [tasks, count] = await Promise.all([
    PersonalTask.find(query)
      .sort(sortObj)
      .skip(parseInt(skip))
      .limit(parseInt(limit)),
    PersonalTask.countDocuments(query),
  ]);

  res.status(200).json({
    success: true,
    data: {
      tasks:
        addLocalTime(
          tasks.map((task) => task.toObject()),
          ["createdAt", "updatedAt"]
        ) || [],
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / limit),
      },
    },
  });
});

// Khôi phục task từ thùng rác
exports.restorePersonalTask = asyncHandler(async (req, res) => {
  const task = await PersonalTask.findOne({
    _id: req.params.id,
    isDeleted: true,
  });

  if (!task) {
    return res.status(404).json({
      success: false,
      message: "Không tìm thấy công việc trong thùng rác",
    });
  }

  // Kiểm tra quyền (admin hoặc chủ sở hữu)
  if (
    req.user.role !== "admin" &&
    (!task.userId || task.userId.toString() !== req.user._id.toString())
  ) {
    return res.status(403).json({
      success: false,
      message: "Không có quyền khôi phục công việc này",
    });
  }

  await PersonalTask.findByIdAndUpdate(req.params.id, {
    isDeleted: false,
    deletedAt: null,
  });

  res.status(200).json({
    success: true,
    message: "Khôi phục công việc thành công",
  });
});

// Xóa vĩnh viễn task từ thùng rác
exports.permanentlyDeletePersonalTask = asyncHandler(async (req, res) => {
  const task = await PersonalTask.findOne({
    _id: req.params.id,
    isDeleted: true,
  });

  if (!task) {
    return res.status(404).json({
      success: false,
      message: "Không tìm thấy công việc trong thùng rác",
    });
  }

  // Kiểm tra quyền (admin hoặc chủ sở hữu)
  if (
    req.user.role !== "admin" &&
    (!task.userId || task.userId.toString() !== req.user._id.toString())
  ) {
    return res.status(403).json({
      success: false,
      message: "Không có quyền xóa vĩnh viễn công việc này",
    });
  }

  await PersonalTask.findByIdAndDelete(req.params.id);

  res.status(200).json({
    success: true,
    message: "Xóa vĩnh viễn công việc thành công",
  });
});

// ========== FILE MANAGEMENT FUNCTIONS ========== //

// Upload file cho personal task
exports.uploadPersonalTaskFiles = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const task = await PersonalTask.findById(id);

  if (!task || task.isDeleted) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy công việc cá nhân" });
  }

  // Kiểm tra quyền (chỉ chủ sở hữu mới được upload file)
  if (!task.userId || task.userId.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      message: "Bạn không có quyền upload file cho công việc này",
    });
  }

  if (!req.files || req.files.length === 0) {
    return res
      .status(400)
      .json({ success: false, message: "Vui lòng chọn file để upload" });
  }

  // Với Cloudinary, file.path chính là URL của file đã upload
  const uploadedFiles = req.files.map((file) => ({
    name: file.originalname,
    url: file.path, // Cloudinary trả về URL trong file.path
    cloudinaryId: file.filename, // Public ID từ Cloudinary
    size: file.size,
    type: file.mimetype,
    uploadedBy: req.user._id,
    uploadedAt: new Date(),
  }));

  const currentFiles = task.files || [];
  const validatedFiles = [...currentFiles, ...uploadedFiles].map((file) => ({
    name: String(file.name || ""),
    url: String(file.url || ""),
    cloudinaryId: String(file.cloudinaryId || ""), // Lưu Cloudinary ID để có thể xóa sau
    size: Number(file.size || 0),
    type: String(file.type || ""),
    uploadedBy: file.uploadedBy,
    uploadedAt: file.uploadedAt || new Date(),
  }));

  try {
    const updatedTask = await PersonalTask.findByIdAndUpdate(
      id,
      { $set: { files: validatedFiles } },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      message: "Upload file thành công",
      data: {
        uploadedFiles: uploadedFiles.map(file => ({
          name: file.name,
          url: file.url,
          size: file.size,
          type: file.type,
          sizeFormatted: formatFileSize(file.size),
          isImage: file.type.startsWith("image/"),
          isDocument: file.type.includes("document") || file.type.includes("pdf") || file.type.includes("excel") || file.type.includes("word"),
        })),
        totalFiles: updatedTask.files.length,
      },
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: "Lỗi khi lưu file",
      error: error.message,
    });
  }
});

// Lấy danh sách file của personal task
exports.getPersonalTaskFiles = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const task = await PersonalTask.findById(id)
    .populate("files.uploadedBy", "fullName employeeCode email")
    .select("title files userId");

  if (!task || task.isDeleted) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy công việc cá nhân" });
  }

  // Kiểm tra quyền (chỉ chủ sở hữu mới được xem file)
  if (!task.userId || task.userId.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      message: "Bạn không có quyền xem file của công việc này",
    });
  }

  const filesWithDetails = task.files.map((file) => {
    // Tạo signed URL cho file (đặc biệt quan trọng cho PDF và file raw)
    const timestamp = Math.round(new Date().getTime() / 1000);
    const expiresAt = timestamp + 3600; // URL hết hạn sau 1 giờ

    let downloadUrl = file.url;

    // Nếu là file raw (PDF, DOC, Excel, etc.) từ Cloudinary, tạo signed URL
    if (
      file.url.includes("cloudinary.com") &&
      (file.type.includes("pdf") ||
        file.type.includes("document") ||
        file.type.includes("application") ||
        file.type.includes("msword") ||
        file.type.includes("spreadsheet") ||
        file.type.includes("presentation"))
    ) {
      try {
        // Extract public_id từ Cloudinary URL
        // URL format: https://res.cloudinary.com/cloud_name/raw/upload/v1234567890/public_id.ext
        const urlMatch = file.url.match(/\/upload\/(?:v\d+\/)?(.+)$/);
        if (urlMatch) {
          const publicId = urlMatch[1]; // Bao gồm cả extension
          downloadUrl = createSignedUrl(publicId, { expires_at: expiresAt });
        }
      } catch (error) {
        console.error("Error creating signed URL:", error);
        // Fallback to original URL if signing fails
        downloadUrl = file.url;
      }
    }

    // Xác định loại file để hiển thị icon phù hợp
    const isImage = file.type.startsWith("image/");
    const isDocument = file.type.includes("document") || 
                      file.type.includes("pdf") || 
                      file.type.includes("msword") || 
                      file.type.includes("spreadsheet") || 
                      file.type.includes("presentation") ||
                      file.type.includes("text/plain");
    const isVideo = file.type.startsWith("video/");
    const isExcel = file.type.includes("spreadsheet") || file.type.includes("excel");
    const isWord = file.type.includes("msword") || file.type.includes("wordprocessing");
    const isPDF = file.type.includes("pdf");

    return {
      _id: file._id,
      name: file.name,
      url: file.url,
      size: file.size,
      type: file.type,
      uploadedBy: file.uploadedBy,
      uploadedAt: file.uploadedAt,
      sizeFormatted: formatFileSize(file.size),
      isImage,
      isDocument,
      isVideo,
      isExcel,
      isWord,
      isPDF,
      downloadUrl: downloadUrl,
      expiresAt: file.url.includes("cloudinary.com") ? expiresAt : null,
    };
  });

  res.status(200).json({
    success: true,
    message: "Lấy danh sách file thành công",
    data: {
      taskTitle: task.title,
      totalFiles: filesWithDetails.length,
      files: filesWithDetails,
    },
  });
});

// Download file từ personal task (với signed URL cho Cloudinary)
exports.downloadPersonalTaskFile = asyncHandler(async (req, res) => {
  const { id, fileId } = req.params;
  const task = await PersonalTask.findById(id);

  if (!task || task.isDeleted) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy công việc cá nhân" });
  }

  // Kiểm tra quyền (chỉ chủ sở hữu mới được download file)
  if (!task.userId || task.userId.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      message: "Bạn không có quyền download file của công việc này",
    });
  }

  const file = task.files.id(fileId);
  if (!file) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy file" });
  }

  // Tạo signed URL cho file
  const timestamp = Math.round(new Date().getTime() / 1000);
  const expiresAt = timestamp + 3600; // URL hết hạn sau 1 giờ

  let downloadUrl = file.url;

  // Nếu là file từ Cloudinary, tạo signed URL
  if (file.url.includes("cloudinary.com")) {
    try {
      // Extract public_id từ Cloudinary URL
      // URL format: https://res.cloudinary.com/cloud_name/raw/upload/v1234567890/public_id.ext
      const urlMatch = file.url.match(/\/upload\/(?:v\d+\/)?(.+)$/);
      if (urlMatch) {
        const publicId = urlMatch[1]; // Bao gồm cả extension
        downloadUrl = createSignedUrl(publicId, { expires_at: expiresAt });
      }
    } catch (error) {
      console.error("Error creating signed URL:", error);
      return res.status(500).json({
        success: false,
        message: "Không thể tạo link download",
      });
    }
  }

  // Xác định loại file để hiển thị thông tin phù hợp
  const isImage = file.type.startsWith("image/");
  const isDocument = file.type.includes("document") || 
                    file.type.includes("pdf") || 
                    file.type.includes("msword") || 
                    file.type.includes("spreadsheet") || 
                    file.type.includes("presentation") ||
                    file.type.includes("text/plain");
  const isVideo = file.type.startsWith("video/");
  const isExcel = file.type.includes("spreadsheet") || file.type.includes("excel");
  const isWord = file.type.includes("msword") || file.type.includes("wordprocessing");
  const isPDF = file.type.includes("pdf");

  res.json({
    success: true,
    message: "Tạo link download thành công",
    data: {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      sizeFormatted: formatFileSize(file.size),
      isImage,
      isDocument,
      isVideo,
      isExcel,
      isWord,
      isPDF,
      downloadUrl: downloadUrl,
      expiresAt: expiresAt,
      expiresIn: "1 giờ",
    },
  });
});

// Xóa file khỏi personal task
exports.deletePersonalTaskFile = asyncHandler(async (req, res) => {
  const { id, fileId } = req.params;
  const task = await PersonalTask.findById(id);

  if (!task || task.isDeleted) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy công việc cá nhân" });
  }

  // Kiểm tra quyền (chỉ chủ sở hữu mới được xóa file)
  if (!task.userId || task.userId.toString() !== req.user._id.toString()) {
    return res
      .status(403)
      .json({ success: false, message: "Bạn không có quyền xóa file" });
  }

  // Tìm file cần xóa
  const fileToDelete = task.files.id(fileId);
  if (!fileToDelete) {
    return res
      .status(404)
      .json({ success: false, message: "Không tìm thấy file" });
  }

  try {
    // Xóa file từ Cloudinary nếu có cloudinaryId
    if (fileToDelete.cloudinaryId) {
      const { deleteFromCloudinary } = require("../utils/cloudinaryUtils");
      await deleteFromCloudinary(fileToDelete.cloudinaryId);
    }

    // Xóa file khỏi array
    task.files.pull(fileId);
    await task.save();

    res.status(200).json({
      success: true,
      message: "Xóa file thành công",
      data: { deletedFile: fileToDelete.name },
    });
  } catch (error) {
    console.error("Lỗi khi xóa file:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi khi xóa file từ Cloudinary",
      error: error.message,
    });
  }
});