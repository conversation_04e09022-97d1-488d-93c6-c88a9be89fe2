.session-management-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.session-management-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #f0f0f0;
}

.session-info,
.ip-restriction-info,
.action-section,
.login-history {
  margin-bottom: 25px;
}

.session-info h3,
.ip-restriction-info h3,
.action-section h3,
.login-history h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.info-item span {
  color: #333;
  font-size: 0.95rem;
  word-break: break-all;
}

.info-item span.enabled {
  color: #28a745;
  font-weight: 600;
}

.info-item span.disabled {
  color: #dc3545;
  font-weight: 600;
}

.active-sessions {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.active-sessions h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.session-item {
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s;
}

.session-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.session-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.session-info .session-id {
  font-weight: 600;
  color: #495057;
  font-family: monospace;
  font-size: 0.9rem;
}

.session-info .browser {
  color: #6c757d;
  font-size: 0.9rem;
}

.session-info .ip {
  color: #007bff;
  font-weight: 500;
  font-size: 0.9rem;
}

.session-info .time {
  color: #6c757d;
  font-size: 0.8rem;
}

.device-details {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.device-details h4 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  color: #495057;
}

.device-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.device-info strong {
  color: #495057;
}

.device-info small {
  color: #6c757d;
  font-size: 0.8rem;
  margin-left: 8px;
}

.history-info .device-type {
  display: inline-block;
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-right: 8px;
  color: #495057;
}

.action-group {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.action-group h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.input-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button,
.input-group button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 120px;
}

.button-group button:disabled,
.input-group button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-group button.danger,
.input-group button.danger {
  background-color: #dc3545;
  color: white;
}

.button-group button.danger:hover:not(:disabled),
.input-group button.danger:hover:not(:disabled) {
  background-color: #c82333;
}

.button-group button.warning {
  background-color: #ffc107;
  color: #212529;
}

.button-group button.warning:hover:not(:disabled) {
  background-color: #e0a800;
}

.button-group button.success {
  background-color: #28a745;
  color: white;
}

.button-group button.success:hover:not(:disabled) {
  background-color: #218838;
}

.input-group button {
  background-color: #007bff;
  color: white;
}

.input-group button:hover:not(:disabled) {
  background-color: #0056b3;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.history-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.history-info .ip {
  font-weight: 600;
  color: #333;
  font-family: monospace;
}

.history-info .browser {
  color: #666;
  font-size: 0.9rem;
}

.history-info .time {
  color: #888;
  font-size: 0.8rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.modal-footer button {
  padding: 10px 20px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.modal-footer button:hover {
  background-color: #5a6268;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1rem;
}

/* .error {
  text-align: center;
  padding: 40px;
  color: #dc3545;
  font-size: 16px;
} */

/* Responsive design */
@media (max-width: 768px) {
  .session-management-content {
    width: 95%;
    padding: 15px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .button-group button,
  .input-group button {
    width: 100%;
  }
} 