@import url('../index.css');

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.modal-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.15);
  padding: 32px 28px 24px 28px;
  min-width: 480px;
  max-width: 540px;
  width: 100%;
  position: relative;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #888;
  transition: color 0.2s;
}
.close-btn:hover {
  color: #333;
}

.project-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.form-row {
  display: flex;
  gap: 18px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
}

.icon-calendar {
  position: absolute;
  right: 8px;
  font-size: 1.1rem;
  color: #888;
}

.members {
  display: flex;
  align-items: center;
  gap: 6px;
}
.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
}
.add-member {
  background: #e3eafc;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  font-size: 1.2rem;
  color: #1976d2;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.add-member:hover {
  background: #d0dbf7;
}

.attach-btn {
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 1.2rem;
  color: #1976d2;
  cursor: pointer;
  transition: background 0.2s;
}
.attach-btn:hover {
  background: #e3eafc;
}

.progress-section {
  margin-top: 8px;
}
.progress-bar {
  background: #e0e0e0;
  border-radius: 8px;
  height: 8px;
  width: 100%;
  margin: 6px 0;
  overflow: hidden;
}
.progress {
  background: #1976d2;
  height: 100%;
  border-radius: 8px;
  transition: width 0.3s;
}
.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.95rem;
  color: #555;
  margin-top: 2px;
}

.update-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 0;
  font-size: 1.08rem;
  font-weight: 500;
  margin-top: 10px;
  cursor: pointer;
  transition: background 0.2s;
}
.update-btn:hover {
  background: #1251a3;
}
