@import url('../index.css');
/* PHÂN TRANG */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
  gap: 4px;
}
.pagination-btn {
  margin: 0 2px;
  font-weight: 500;
  background: #fff;
  border: 1px solid #bfc6d1;
  border-radius: 6px;
  padding: 4px 16px;
  cursor: pointer;
  color: #222;
  transition: background 0.15s, color 0.15s, border 0.15s;
  outline: none;
}
.pagination-btn.active {
  background: #3b82f6;
  color: #fff;
  border: 1.5px solid #2563eb;
  font-weight: bold;
}
.pagination-btn.disabled {
  background: #f3f4f6;
  color: #bfc6d1;
  border: 1px solid #e5e7eb;
  cursor: not-allowed;
}
.pagination-btn:not(.disabled):hover {
  background: #e0e7ff;
  color: #1d4ed8;
}

.hr-listuser-container {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
}
.hr-listuser-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}
.hr-listuser-table thead tr {
  background: #eaf3fb;
  color: #5B5B5B;
  font-weight: 600;
}
.hr-listuser-table th {
  padding: 12px 8px;
  text-align: left;
}

/* Căn giữa header các cột phòng ban, chức vụ, trạng thái, ngày tạo tk, hành động */
.hr-listuser-table th:nth-child(2),
.hr-listuser-table th:nth-child(3),
.hr-listuser-table th:nth-child(4),
.hr-listuser-table th:nth-child(5),
.hr-listuser-table th:nth-child(6) {
  text-align: center;
}
.hr-listuser-table th:first-child {
  border-radius: 8px 0 0 0;
}
.hr-listuser-table th:last-child {
  border-radius: 0 8px 0 0;
}
.hr-listuser-table tbody tr {
  border-bottom: 1px solid #f0f0f0;
}
.hr-listuser-td-user {
  padding: 12px 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.hr-listuser-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #eaf3fb;
}
.hr-listuser-username {
  font-weight: 500;
}
.hr-listuser-email {
  color: #888;
  font-size: 13px;
}
.hr-listuser-id {
  color: #bdbdbd;
  font-size: 12px;
}
.hr-listuser-code {
  color: #bdbdbd;
  font-size: 12px;
}
.hr-listuser-td {
  padding: 12px 8px;
}

/* Căn giữa nội dung các cột phòng ban, chức vụ, trạng thái, ngày tạo tk, hành động */
.hr-listuser-table td:nth-child(2),
.hr-listuser-table td:nth-child(3),
.hr-listuser-table td:nth-child(4),
.hr-listuser-table td:nth-child(5),
.hr-listuser-table td:nth-child(6) {
  text-align: center;
}
.hr-listuser-department {
  background: #A3BFFA;
  color: #000000;
  border-radius: 12px;
  padding: 3px 12px;
  font-size: 12px;
  font-weight: 400;
}
.hr-listuser-status-active {
  background: #C6E8D3;
  color: #006400;
  border-radius: 12px;
  padding: 3px 12px;
  font-size: 12px;
  font-weight: 400;
}
.hr-listuser-status-locked {
  background: #F8B4B4;
  color: #8B0000;
  border-radius: 8px;
  padding: 3px 12px;
  font-size: 12px;
  font-weight: 400;
}
.hr-listuser-action {
  font-size: 15px;
  color: #5d5d5d;
  cursor: pointer;
  text-align: center;
}
.hr-listuser-action-menu {
  position: absolute;
  top: 50px;
  right: 0;
  min-width: 180px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 8px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.15s;
}
.hr-listuser-action-menu-title {
  font-weight: 600;
  font-size: 15px;
  color: #222;
  padding: 8px 20px 6px 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 2px;
}
.hr-listuser-action-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #000;
  padding: 8px;
  cursor: pointer;
  transition: background 0.15s;
}
.hr-listuser-action-menu-item:hover {
  background: #f5f7fa;
}
.hr-listuser-action-menu-icon {
  margin-left: 12px;
  display: flex;
  align-items: center;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px); }
  to { opacity: 1; transform: translateY(0); }
}
