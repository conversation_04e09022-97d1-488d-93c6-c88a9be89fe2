.logout-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.logout-confirmation-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 420px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.logout-confirmation-header {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 20px 24px;
  text-align: center;
}

.logout-confirmation-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.logout-confirmation-body {
  padding: 24px;
  text-align: center;
}

.logout-confirmation-body p {
  margin: 0;
  color: #444;
  line-height: 1.6;
  font-size: 1rem;
}

.logout-confirmation-actions {
  padding: 0 24px 24px;
  display: flex;
  justify-content: center;
}

.logout-confirmation-actions button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.btn-confirm {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

.btn-confirm:hover {
  background: linear-gradient(135deg, #ee5a52, #dc4c41);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(238, 90, 82, 0.3);
}

.btn-cancel {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #e9ecef;
}

.btn-cancel:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@media (max-width: 768px) {
  .logout-confirmation-modal {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .logout-confirmation-actions button {
    width: 100%;
    max-width: 200px;
  }
} 