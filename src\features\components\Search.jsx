import React, { useState } from "react";
import { Tabs, Tab, <PERSON><PERSON> } from "@mui/material";
import All from "../pages/Search/SearchAll";
import Project from "../pages/Search/SearchProject";
import Job from "../pages/Search/SearchJob";
import Member from "../pages/Search/SearchMember";
import Note from "../pages/Search/SearchNote";

const tabList = [
  { label: "Tất cả", count: 4 },
  { label: "Dự án", count: 1 },
  { label: "Công việc", count: 1 },
  { label: "Thành viên", count: 1 },
  { label: "Ghi chú", count: 1 },
];

export default function Search({ onClose, searchValue }) {
  const [tab, setTab] = useState(0);
  const [counts, setCounts] = useState({
    project: 0,
    job: 0,
    member: 0,
    note: 0,
  });

  const handleTabChange = (event, newValue) => {
    setTab(newValue);
  };

  // Tổng tất cả các loại
  const allCount = counts.project + counts.job + counts.member + counts.note;

  const tabList = [
    { label: "Tất cả", count: allCount },
    { label: "Dự án", count: counts.project },
    // { label: "Công việc", count: counts.job },
    { label: "Thành viên", count: counts.member },
    { label: "Ghi chú", count: counts.note },
  ];

  // Callback nhận số lượng từ các component con
  const handleCountChange = (type, count) => {
    setCounts((prev) => ({ ...prev, [type]: count }));
  };

  return (
    <>
      {/* Overlay để đóng form khi click ra ngoài */}
      <div
        style={{
          position: "fixed",
          inset: 0,
          background: "rgba(0,0,0,0.08)",
          zIndex: 99,
        }}
        onClick={onClose}
      />
      <div
        style={{
          background: "#fff",
          padding: 16,
          borderRadius: 8,
          boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
          position: "relative",
          zIndex: 100,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <Tabs
          value={tab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ borderBottom: 1, borderColor: "divider" }}
        >
          {tabList.map((t, idx) => (
            <Tab
              key={t.label}
              label={
                <span>
                  {t.label}{" "}
                  <span style={{ color: "#888" }}>({t.count})</span>
                </span>
              }
              sx={{ minWidth: 100, textTransform: "none" }}
            />
          ))}
        </Tabs>
        <div
          style={{
            marginTop: 16,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <span style={{ fontWeight: 500 }}>
            Kết quả tìm kiếm&nbsp;
            <span style={{ fontWeight: 400 }}>
              {/* Hiển thị số lượng và từ khóa tìm kiếm */}
              {allCount} kết quả cho “ {searchValue ? searchValue : ""} ”
            </span>
          </span>
          <Button variant="outlined" size="small" onClick={onClose}>
            Xóa tìm kiếm
          </Button>
        </div>
        {/* Hiển thị nội dung tab */}
        {tab === 0 && (
          <div style={{ marginTop: 16 }}>
            <Project searchValue={searchValue} onCountChange={(count) => handleCountChange("project", count)} />
            {/* <Job searchValue={searchValue} onCountChange={(count) => handleCountChange("job", count)} /> */}
            <Member searchValue={searchValue} onCountChange={(count) => handleCountChange("member", count)} />
            <Note searchValue={searchValue} onCountChange={(count) => handleCountChange("note", count)} />
          </div>
        )}
        {tab === 1 && (
          <div style={{ marginTop: 16 }}>
            <Project searchValue={searchValue} onCountChange={(count) => handleCountChange("project", count)} />
          </div>
        )}
        {/* {tab === 2 && (
          <div style={{ marginTop: 16 }}>
            <Job searchValue={searchValue} onCountChange={(count) => handleCountChange("job", count)} />
          </div>
        )} */}
        {tab === 2 && (
          <div style={{ marginTop: 16 }}>
            <Member searchValue={searchValue} onCountChange={(count) => handleCountChange("member", count)} />
          </div>
        )}
        {tab === 3 && (
          <div style={{ marginTop: 16 }}>
            <Note searchValue={searchValue} onCountChange={(count) => handleCountChange("note", count)} />
          </div>
        )}
        {/* Có thể thêm các tab khác ở đây */}
      </div>
    </>
  );
}