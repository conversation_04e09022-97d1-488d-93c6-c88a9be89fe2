import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import AccessDenied from './AccessDenied';
import { getProfile } from '../api/profile';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  redirectTo?: string;
  showAccessDenied?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredRoles = [], redirectTo = '/', showAccessDenied = true }) => {
  // Lấy thông tin user hiện tại từ localStorage
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const getToken = () => {
    // Kiểm tra token từ nhiều nguồn khác nhau
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const userToken = getCurrentUser().token;

    return tokenFromStorage || authToken || userToken;
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();
  const token = getToken();

  const [checking, setChecking] = useState(true);
  const [shouldLogout, setShouldLogout] = useState(false);

  useEffect(() => {
    let isMounted = true;
    getProfile()
      .then(profileRes => {
        // Nếu backend trả về user bị khóa
        const user = profileRes.data || profileRes;
        localStorage.setItem('user', JSON.stringify(user));
        if (user.status === 'locked' || user.isBlocked === true) {
          localStorage.clear();
          if (isMounted) setShouldLogout(true);
        } else {
          if (isMounted) setChecking(false);
        }
      })
      .catch(() => {
        localStorage.clear();
        if (isMounted) setShouldLogout(true);
      });
    return () => { isMounted = false; };
  }, []);

  if (shouldLogout) return <Navigate to="/login" replace />;
  if (checking) return null;

  // Nếu tài khoản bị khóa thì xóa localStorage và chuyển hướng về login
  if (currentUser.status === 'locked' || currentUser.isBlocked === true) {
    localStorage.clear();
    return <Navigate to="/login" replace />;
  }

  // Kiểm tra xem user có đăng nhập không - chỉ cần có token hoặc user info
  if (!token && !currentUser.email) {
    return <Navigate to="/login" replace />;
  }

  // Kiểm tra quyền truy cập
  if (requiredRoles.length > 0 && !requiredRoles.includes(userRole)) {
    // Nếu không có quyền, hiển thị trang AccessDenied hoặc chuyển hướng
    if (showAccessDenied) {
      return <AccessDenied message={`Chỉ có ${requiredRoles.join(', ').toUpperCase()} mới có quyền truy cập trang này.`} />;
    } else {
      return <Navigate to={redirectTo} replace />;
    }
  }

  // Nếu có quyền, render component con
  return children;
};

export default ProtectedRoute;
