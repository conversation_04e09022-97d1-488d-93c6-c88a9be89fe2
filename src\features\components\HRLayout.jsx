import React, { useState, useRef, useEffect, useCallback } from "react";
import '../../styles/HRLayout.css';
import searchIcon from '../../assets/search.svg';
import addIcon from '../../assets/add.svg';
import downloadIcon from '../../assets/download.svg';
import usersIcon from '../../assets/users.svg';
import dropdownIcon from '../../assets/icon-sidebar/dropdown.svg';
import { statuses } from '../../storage/hrOptions';
import UserCreate from './UserCreate';
import * as XLSX from "xlsx";
import { createUser } from '../../api/userManagement';
import { showSuccess, showError } from '../../components/Toastify';
import { getAllDepartments } from '../../api/departmentManagement';

const HRLayout = ({ users, filteredUsers, onAddUser, onFilterChange, roles }) => {
  const [selectedDepartment, setSelectedDepartment] = useState('Tất cả phòng ban');
  const [selectedRole, setSelectedRole] = useState('Tất cả chức vụ');
  const [selectedStatus, setSelectedStatus] = useState('Tất cả trạng thái');
  const [openDropdown, setOpenDropdown] = useState(null); // 'department' | 'role' | 'status' | null
  const [showCreate, setShowCreate] = useState(false);
  const [search, setSearch] = useState("");
  const [searchInput, setSearchInput] = useState(""); // Input value for debouncing
  const [isSearching, setIsSearching] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState(null);
  const [createSuccess, setCreateSuccess] = useState(false);
  const dropdownRef = useRef(null);
  const [departments, setDepartments] = useState([]);

  // Lấy thông tin user hiện tại để kiểm tra quyền
  const getCurrentUser = () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      return userRaw.user || userRaw;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const userRole = currentUser.role?.toLowerCase();

  // Kiểm tra quyền thêm nhân sự - admin, CEO và HR có quyền
  const canAddUser = ['admin', 'ceo', 'hr'].includes(userRole);

  // Debounce search input
  const debouncedSearch = useCallback(
    (() => {
      let timeoutId;
      return (value) => {
        clearTimeout(timeoutId);
        setIsSearching(true);
        timeoutId = setTimeout(() => {
          setSearch(value);
          setIsSearching(false);
        }, 300);
      };
    })(),
    []
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchInput(value);
    debouncedSearch(value);
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchInput("");
    setSearch("");
  };

  // Reset all filters
  const handleResetFilters = () => {
    setSelectedDepartment('Tất cả phòng ban');
    setSelectedRole('Tất cả chức vụ');
    setSelectedStatus('Tất cả trạng thái');
    setSearchInput("");
    setSearch("");
  };

  // Check if any filter is active
  const hasActiveFilters = selectedDepartment !== 'Tất cả phòng ban' || 
                          selectedRole !== 'Tất cả chức vụ' || 
                          selectedStatus !== 'Tất cả trạng thái' || 
                          searchInput.trim() !== '';

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (selectedDepartment !== 'Tất cả phòng ban') count++;
    if (selectedRole !== 'Tất cả chức vụ') count++;
    if (selectedStatus !== 'Tất cả trạng thái') count++;
    if (searchInput.trim() !== '') count++;
    return count;
  };

  // Lấy danh sách phòng ban động khi mount
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const res = await getAllDepartments({ includeInactive: true });
        setDepartments(res.data || []);
      } catch (err) {
        setDepartments([]);
      }
    };
    fetchDepartments();
  }, []);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdown(null);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Lấy phòng ban đang chọn (object)
  const selectedDeptObj = selectedDepartment === 'Tất cả phòng ban'
    ? null
    : departments.find(dep => dep.name === selectedDepartment || dep.id === selectedDepartment || dep._id === selectedDepartment);

  // Gửi filter lên cha mỗi khi thay đổi
  useEffect(() => {
    if (onFilterChange) {
      onFilterChange({
        department: selectedDepartment,
        role: selectedRole,
        status: selectedStatus,
        search,
      });
    }
  }, [selectedDepartment, selectedRole, selectedStatus, search, onFilterChange]);

  const handleCreate = () => setShowCreate(true);
  const handleCloseCreate = () => {
    setShowCreate(false);
    setCreateError(null);
    setCreateSuccess(false);
  };
  
  const handleCreateSuccess = (newUser) => {
    // Gọi callback để cập nhật danh sách user
    if (onAddUser) {
      onAddUser(newUser);
    }
    
    // Reset state
    setShowCreate(false);
    setCreateError(null);
    setCreateSuccess(false);
  };
  const handleSubmitCreate = async (data) => {
    setIsCreating(true);
    setCreateError(null);
    setCreateSuccess(false);
    
    try {
      // Validation
      if (!data.name || !data.email || !data.department || !data.position || !data.role) {
        throw new Error('Vui lòng điền đầy đủ thông tin bắt buộc');
      }
      
      if (data.password !== data.confirmPassword) {
        throw new Error('Mật khẩu xác nhận không khớp');
      }
      
      if (data.password.length < 8) {
        throw new Error('Mật khẩu phải có ít nhất 8 ký tự');
      }
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        throw new Error('Email không đúng định dạng');
      }
      
      // Chuyển đổi dữ liệu sang format API
      const userData = {
        fullName: data.name.trim(), // Đổi từ name sang fullName
        email: data.email.trim().toLowerCase(),
        department: data.department,
        position: data.position.trim(),
        role: data.role.toLowerCase(), // Chuyển về lowercase để phù hợp với backend
        password: data.password,
        status: 'active' // Mặc định là active
      };
      
      const result = await createUser(userData);
      setCreateSuccess(true);
      showSuccess('Tạo tài khoản thành công!');
      
      // Đóng modal sau 1 giây để user thấy thông báo thành công
      setTimeout(() => {
        setShowCreate(false);
        setCreateSuccess(false);
      }, 1000);
      
      // Gọi callback để cập nhật UI nếu có
      if (onAddUser) {
        onAddUser(result.user || userData);
      }
      
    } catch (error) {
      setCreateError(error.message);
      if (error.message !== 'Email đã được sử dụng') {
        showError(error.message);
      }
    } finally {
      setIsCreating(false);
    }
  };

  // Hàm chuyển role sang tiếng Việt
  const getRoleText = (role) => {
    if (!role) return '';
    const map = {
      'admin': 'Quản trị viên',
      'ceo': 'Giám đốc',
      'hr': 'Nhân sự',
      'departmenthead': 'Trưởng phòng',
      'leader': 'Leader',
      'staff': 'Nhân viên',
      'hcns': 'Hành chính nhân sự',
      'admin sys': 'Quản trị hệ thống',
    };
    const lower = role.toLowerCase();
    return map[lower] || role;
  };

  const handleExportExcel = () => {
    const ws = XLSX.utils.json_to_sheet(
      users.map((user, idx) => ({
        STT: idx + 1,
        "Họ tên": user.name,
        Email: user.email,
        "Phòng ban": user.department?.name || user.department || '',
        "Chức vụ": getRoleText(user.role),
        "Trạng thái": user.status === 'active' ? 'Hoạt động' : 'Đã khoá',
        "Ngày tạo": user.createdAt,
      }))
    );
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "DanhSachNhanSu");
    XLSX.writeFile(wb, "danh_sach_nhan_su.xlsx");
  };

  return (
    <div className="hr-layout-container">
      {showCreate && (
        <UserCreate 
          onClose={handleCloseCreate}
          onSuccess={handleCreateSuccess}
        />
      )}
      <div className="hr-layout-title-row">
        <img src={usersIcon} alt="icon" style={{ width: 24, height: 24, color: '#2d5be3' }} />
        <div>
          <div className="hr-layout-title">Hành chính nhân sự</div>
          <div className="hr-layout-desc">Hệ thống quản lý toàn diện về nhân sự</div>
        </div>
      </div>
      <div className="hr-layout-search">
        <div className="hr-layout-search-input-wrap">
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, email, ID nhân sự..."
            className="hr-layout-search-input"
            value={searchInput}
            onChange={handleSearchChange}
          />
          <span className="hr-layout-search-icon">
            {isSearching ? (
              <div style={{
                width: 16,
                height: 16,
                border: '2px solid #f3f3f3',
                borderTop: '2px solid #2d5be3',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            ) : (
              <img src={searchIcon} alt="search" style={{ width: 16, height: 16 }} />
            )}
          </span>
          {searchInput && (
            <button
              onClick={handleClearSearch}
              style={{
                position: 'absolute',
                right: '40px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '16px',
                color: '#999',
                padding: '4px',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              title="Xóa tìm kiếm"
            >
              ×
            </button>
          )}
        </div>
      </div>
      <div className="hr-toolbar-row">
        <div className="hr-toolbar-left" ref={dropdownRef}>
          <div className="hr-dropdown">
            <button className="hr-dropdown-btn" onClick={() => setOpenDropdown(openDropdown === 'department' ? null : 'department')}>
              <span>{selectedDeptObj ? `${selectedDeptObj.name} (${selectedDeptObj.code})` : selectedDepartment}</span>
              <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
            </button>
            {openDropdown === 'department' && (
              <div className="hr-dropdown-menu">
                <div className="hr-dropdown-item" onClick={() => { setSelectedDepartment('Tất cả phòng ban'); setOpenDropdown(null); }}>Tất cả phòng ban</div>
                {departments.map(dep => (
                  <div key={dep.id || dep._id || dep.name} className="hr-dropdown-item" onClick={() => { setSelectedDepartment(dep.name); setOpenDropdown(null); }}>
                    {dep.name}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="hr-dropdown">
            <button className="hr-dropdown-btn" onClick={() => setOpenDropdown(openDropdown === 'role' ? null : 'role')}>
              <span>{selectedRole === 'Tất cả chức vụ' ? 'Tất cả chức vụ' : getRoleText(selectedRole)}</span>
              <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
            </button>
            {openDropdown === 'role' && (
              <div className="hr-dropdown-menu">
                <div className="hr-dropdown-item" onClick={() => { setSelectedRole('Tất cả chức vụ'); setOpenDropdown(null); }}>Tất cả chức vụ</div>
                {roles && roles.slice(1).map((role) => (
                  <div key={role} className="hr-dropdown-item" onClick={() => { setSelectedRole(role); setOpenDropdown(null); }}>
                    {getRoleText(role)}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="hr-dropdown">
            <button className="hr-dropdown-btn" onClick={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}>
              <span>{selectedStatus}</span>
              <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
            </button>
            {openDropdown === 'status' && (
              <div className="hr-dropdown-menu">
                <div className="hr-dropdown-item" onClick={() => { setSelectedStatus('Tất cả trạng thái'); setOpenDropdown(null); }}>Tất cả trạng thái</div>
                {statuses.slice(1).map((status) => (
                  <div key={status} className="hr-dropdown-item" onClick={() => { setSelectedStatus(status); setOpenDropdown(null); }}>{status}</div>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="hr-toolbar-actions">
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <span className="hr-toolbar-count">
              Hiển thị {filteredUsers.length}/{users.length}
              {hasActiveFilters && (
                <span style={{ 
                  marginLeft: '8px', 
                  fontSize: '12px', 
                  color: '#666',
                  backgroundColor: '#f0f0f0',
                  padding: '2px 6px',
                  borderRadius: '10px'
                }}>
                  {getActiveFilterCount()} bộ lọc
                </span>
              )}
            </span>
            {hasActiveFilters && (
              <button
                onClick={handleResetFilters}
                style={{
                  background: 'none',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  padding: '4px 8px',
                  fontSize: '12px',
                  color: '#666',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
                title="Xóa tất cả bộ lọc"
              >
                <span>×</span>
                Xóa lọc
              </button>
            )}
          </div>
          <button className="hr-toolbar-btn" onClick={handleExportExcel}>
            <img src={downloadIcon} alt="excel" style={{ width: 18, height: 18 }} /> Xuất Excel
          </button>
          {canAddUser && (
            <button className="hr-toolbar-btn hr-toolbar-btn-primary" onClick={handleCreate} disabled={isCreating}>
              <img src={addIcon} alt="add" style={{ width: 18, height: 18 }} /> 
              {isCreating ? 'Đang tạo...' : 'Thêm nhân sự'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default HRLayout;
