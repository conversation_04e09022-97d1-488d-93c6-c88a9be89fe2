import React from "react";
import DeploymentIcon from "../../../assets/deployment.svg";

const badgeStyle = (bg, color) => ({
  background: bg,
  color,
  borderRadius: 12,
  fontWeight: 500,
  fontSize: 15,
  padding: "4px 16px",
  display: "inline-block",
  minWidth: 70,
  textAlign: "center",
});

const labelStyle = {
  fontWeight: 600,
  fontSize: 17,
  marginBottom: 2,
};

const subLabelStyle = {
  color: "#888",
  fontSize: 14,
  marginBottom: 2,
};

const cellTitle = {
  fontWeight: 500,
  fontSize: 15,
  marginBottom: 2,
};

const cellValue = {
  color: "#888",
  fontSize: 14,
};

const avatarList = [
  "https://randomuser.me/api/portraits/women/1.jpg",
  "https://randomuser.me/api/portraits/men/2.jpg",
  "https://randomuser.me/api/portraits/men/3.jpg",
];

const Job = () => {
  return (
    <div style={{ background: "#fff", padding: 0, borderRadius: 12 }}>
      <div
        style={{
          border: "1px solid #f3f3f3",
          borderRadius: 12,
          marginBottom: 12,
          padding: 0,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "stretch",
            padding: "20px 24px",
            gap: 0,
          }}
        >
          <>
            <div style={{ flex: 2 }}>
              <div style={labelStyle}>Hệ thống quản lí bán hàng</div>
              <div style={subLabelStyle}>Mã dự án: SD-01</div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Thiết kế giao diện người dùng</div>
              <div style={cellValue}>Mã công việc: TSK-01</div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Thành viên</div>
              <div>
                {avatarList.map((src, idx) => (
                  <img
                    key={idx}
                    src={src}
                    alt="avatar"
                    style={{
                      width: 28,
                      height: 28,
                      borderRadius: "50%",
                      border: "2px solid #fff",
                      marginLeft: idx === 0 ? 0 : -8,
                      boxShadow: "0 0 0 1px #e0e0e0",
                      background: "#fff",
                    }}
                  />
                ))}
              </div>
            </div>
            <div style={{ flex: 2 }}>
              <div style={cellTitle}>Trạng thái</div>
              <div
                style={{
                  color: "#4a90e2",
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <img
                  src={DeploymentIcon}
                  alt="Đang tiến hành"
                  style={{ width: 18, height: 18, marginRight: 4 }}
                />
                Đang tiến hành
              </div>
            </div>
            <div
              style={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-end",
              }}
            >
              <span style={badgeStyle("#eafaf3", "#27ae60")}>Công việc</span>
            </div>
          </>
        </div>
      </div>
    </div>
  );
};

export default Job;
