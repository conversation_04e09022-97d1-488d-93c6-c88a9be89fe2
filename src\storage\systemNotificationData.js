// <PERSON><PERSON> liệu thông bá<PERSON> hệ thống
export const systemNotificationData = [
  {
    id: 1,
    title: "<PERSON><PERSON><PERSON> nhật hệ thống",
    message: "<PERSON><PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "maintenance",
    priority: "high",
    icon: "settings",
    category: "system"
  },
  {
    id: 2,
    title: "<PERSON><PERSON>h năng mới",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "feature",
    priority: "medium",
    icon: "star",
    category: "update"
  },
  {
    id: 3,
    title: "Cảnh báo bảo mật",
    message: "Đ<PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: true,
    type: "security",
    priority: "high",
    icon: "shield",
    category: "security",
    ipAddress: "*************",
    location: "<PERSON><PERSON>, Việt Nam"
  },
  {
    id: 4,
    title: "<PERSON>o lưu dữ liệu",
    message: "<PERSON>u<PERSON> trình sao lưu dữ liệu hàng tuần đã hoàn thành thành công",
    time: "6 giờ trước",
    isRead: true,
    type: "backup",
    priority: "low",
    icon: "database",
    category: "system",
    backupSize: "2.5 GB"
  },
  {
    id: 5,
    title: "Lỗi hệ thống",
    message: "Đã khắc phục lỗi hiển thị biểu đồ trong trang thống kê",
    time: "1 ngày trước",
    isRead: true,
    type: "bug-fix",
    priority: "medium",
    icon: "bug",
    category: "fix",
    affectedUsers: 15
  },
  {
    id: 6,
    title: "Cập nhật chính sách",
    message: "Chính sách bảo mật đã được cập nhật, vui lòng xem lại",
    time: "2 ngày trước",
    isRead: true,
    type: "policy",
    priority: "medium",
    icon: "document",
    category: "policy",
    documentLink: "/policies/security-policy-v2.pdf"
  },
  {
    id: 7,
    title: "Thông báo bảo trì",
    message: "Bảo trì server database đã hoàn thành, hệ thống hoạt động bình thường",
    time: "3 ngày trước",
    isRead: true,
    type: "maintenance-complete",
    priority: "low",
    icon: "check-circle",
    category: "system",
    downtime: "45 phút"
  }
];

// Hàm lọc thông báo hệ thống
export const filterSystemNotifications = (notifications, filter) => {
  switch (filter) {
    case 'unread':
      return notifications.filter(notif => !notif.isRead);
    case 'read':
      return notifications.filter(notif => notif.isRead);
    case 'all':
    default:
      return notifications;
  }
};

// Hàm đánh dấu đã đọc
export const markSystemNotificationAsRead = (notificationId) => {
  const notification = systemNotificationData.find(notif => notif.id === notificationId);
  if (notification) {
    notification.isRead = true;
  }
};

// Hàm đánh dấu tất cả đã đọc
export const markAllSystemNotificationsAsRead = () => {
  systemNotificationData.forEach(notification => {
    notification.isRead = true;
  });
};

// Hàm đánh dấu tất cả chưa đọc
export const markAllSystemNotificationsAsUnread = () => {
  systemNotificationData.forEach(notification => {
    notification.isRead = false;
  });
};
