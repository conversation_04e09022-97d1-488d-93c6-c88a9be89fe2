// Thêm cache và preload ở đầu file
let activityCache = null;
let activityCacheTimestamp = 0;
const ACTIVITY_CACHE_DURATION = 10000; // 10s
let activityPreloadPromise = null;

import React, { useEffect, useState } from "react";
import user1 from "../../../assets/user1.png";
import { getAllProjects } from "../../../api/projectManagement";
import { getMyProjectTasks, getProjectTasks } from "../../../api/taskManagement";
import { getAllUsers } from "../../../api/userManagement";
import { getProfile } from "../../../api/profile";

const preloadActivities = async () => {
  if (activityPreloadPromise) return activityPreloadPromise;
  activityPreloadPromise = (async () => {
    try {
      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = userRaw._id || userRaw.id || (userRaw.user && (userRaw.user._id || userRaw.user.id));
      const role = (userRaw.role || userRaw.user?.role || "").toLowerCase();
      let activities = [];
      if (role === "admin" || role === "ceo") {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        // Tối ưu: lấy tasks song song
        const allTasksArr = await Promise.all(projects.map(project => getProjectTasks(project._id || project.id).then(taskRes => taskRes.data || taskRes).catch(() => [])));
        let allTasks = allTasksArr.flat();
        activities = allTasks;
      } else if (role === "departmenthead") {
        const profile = await getProfile();
        const myDeptId = profile.data?.departmentId?._id || profile.data?.departmentId || profile.data?.department?._id;
        const usersRes = await getAllUsers();
        const users = usersRes.data || usersRes;
        const deptUserIds = users.filter(u => u.departmentId?._id === myDeptId || u.departmentId === myDeptId).map(u => u._id || u.id);
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        const allTasksArr = await Promise.all(projects.map(project => getProjectTasks(project._id || project.id).then(taskRes => taskRes.data || taskRes).catch(() => [])));
        let allTasks = allTasksArr.flat();
        activities = allTasks.filter(t =>
          deptUserIds.includes(t.assignedToId?._id || t.assignedToId?.id || t.assignedTo?._id || t.assignedTo?.id) ||
          deptUserIds.includes(t.createdBy?._id || t.createdBy?.id)
        );
      } else if (role === "leader") {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        const myProjects = projects.filter(p => p.leaderId?._id === userId || p.leaderId?.id === userId);
        let staffIds = [];
        myProjects.forEach(p => {
          if (Array.isArray(p.members)) {
            staffIds = staffIds.concat(p.members.map(m => m.userId || m._id));
          }
        });
        const allTasksArr = await Promise.all(myProjects.map(project => getProjectTasks(project._id || project.id).then(taskRes => taskRes.data || taskRes).catch(() => [])));
        let allTasks = allTasksArr.flat();
        activities = allTasks.filter(t =>
          staffIds.includes(t.assignedToId?._id || t.assignedToId?.id || t.assignedTo?._id || t.assignedTo?.id) ||
          staffIds.includes(t.createdBy?._id || t.createdBy?.id)
        );
      } else {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes;
        const myProjects = projects.filter(p =>
          Array.isArray(p.members) && p.members.some(m => m.userId === userId || m._id === userId)
        );
        let memberIds = [];
        myProjects.forEach(p => {
          if (Array.isArray(p.members)) {
            memberIds = memberIds.concat(p.members.map(m => m.userId || m._id));
          }
        });
        const allTasksArr = await Promise.all(myProjects.map(project => getProjectTasks(project._id || project.id).then(taskRes => taskRes.data || taskRes).catch(() => [])));
        let allTasks = allTasksArr.flat();
        activities = allTasks.filter(t =>
          memberIds.includes(t.assignedToId?._id || t.assignedToId?.id || t.assignedTo?._id || t.assignedTo?.id) ||
          memberIds.includes(t.createdBy?._id || t.createdBy?.id)
        );
      }
      const recent = activities
        .sort((a, b) => new Date(b.updatedAt || b.updated_at || b.updated) - new Date(a.updatedAt || a.updated_at || a.updated))
        .slice(0, 10)
        .map(t => ({
          user: t.assignedToId?.fullName || t.assignedTo?.fullName || t.createdBy?.fullName || "Thành viên",
          avatar: t.assignedToId?.avatar || t.assignedTo?.avatar || t.createdBy?.avatar || user1,
          content: t.progress === 100 || t.status === "completed"
            ? `đã hoàn thành công việc \"${t.title || t.name || t.taskCode || "Công việc"}\"`
            : `đã cập nhật tiến độ công việc \"${t.title || t.name || t.taskCode || "Công việc"}\" (${t.progress || 0}%)`,
          time: t.updatedAt ? new Date(t.updatedAt).toLocaleString("vi-VN") : ""
        }));
      activityCache = recent;
      activityCacheTimestamp = Date.now();
      return recent;
    } catch {
      return [];
    }
  })();
  return activityPreloadPromise;
};

const RecentActivity = () => {
  const [activities, setActivities] = useState(activityCache || []);
  const [loading, setLoading] = useState(!activityCache);
  const [error, setError] = useState(null);

  useEffect(() => {
    let ignore = false;
    const fetchActivities = async () => {
      const now = Date.now();
      if (activityCache && (now - activityCacheTimestamp) < ACTIVITY_CACHE_DURATION) {
        setActivities(activityCache);
        setLoading(false);
        // Preload in background
        preloadActivities().then(newActs => {
          if (!ignore && newActs) {
            setActivities(newActs);
            setLoading(false);
          }
        });
        return;
      }
      setLoading(!activityCache);
      setError(null);
      try {
        const newActs = await preloadActivities();
        if (!ignore && newActs) {
          setActivities(newActs);
          setLoading(false);
        }
      } catch (err) {
        if (!ignore) {
          setError(err.message || "Lỗi tải hoạt động");
          setLoading(false);
        }
      }
    };
    fetchActivities();
    return () => { ignore = true; };
  }, []);

  useEffect(() => { preloadActivities(); }, []);

  return (
    <div style={{ background: "#fff", borderRadius: 12, padding: 20, maxWidth: 490, minWidth: 320, boxShadow: "0 2px 8px #eee", width: '100%' }}>
      <h3 style={{ marginBottom: 16 }}>Hoạt động gần đây</h3>
      <div>
        {error ? (
          <div style={{ color: 'red' }}>{error}</div>
        ) : loading ? (
          Array.from({ length: 6 }).map((_, idx) => (
            <div key={idx} style={{ display: "flex", alignItems: "flex-start", marginBottom: 12, opacity: 0.7, width: '100%', minWidth: 0 }}>
              <div style={{ width: 28, height: 28, background: '#f0f0f0', borderRadius: '50%', marginRight: 10, marginTop: 2, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
              <div style={{ flex: 1 }}>
                <div style={{ width: '80%', height: 16, background: '#f0f0f0', borderRadius: 4, marginBottom: 6, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
                <div style={{ width: 100, height: 13, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
              </div>
            </div>
          ))
        ) : activities.length === 0 ? (
          <div>Không có hoạt động nào gần đây.</div>
        ) : (
          activities.map((act, idx) => (
            <div key={idx} style={{ display: "flex", alignItems: "flex-start", marginBottom: 12 }}>
              <img
                src={act.avatar}
                alt="avatar"
                style={{
                  width: 28,
                  height: 28,
                  borderRadius: "50%",
                  marginRight: 10,
                  marginTop: 2,
                  objectFit: "cover",
                  flexShrink: 0
                }}
              />
              <div>
                <div style={{ fontSize: 15 }}>
                  <b>{act.user}</b> {act.content}
                </div>
                <div style={{ fontSize: 13, color: "#888" }}>{act.time}</div>
              </div>
            </div>
          ))
        )}
      </div>
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default RecentActivity;
