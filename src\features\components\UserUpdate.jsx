import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../../styles/UserUpdate.css';
import { getDepartmentsList } from '../../api/departmentManagement';
import { updateUser, checkDepartmentHead } from '../../api/userManagement';
import usersIcon from '../../assets/users.svg';
import documentIcon from '../../assets/document.svg';
import dropdownIcon from '../../assets/icon-sidebar/dropdown.svg';
import { validateUserUpdateForm } from '../../utils/validation';
import { showSuccess, showError } from '../../components/Toastify';

const roleHierarchy = ['staff', 'leader', 'departmentHead', 'hr', 'admin'];

const fetchRoles = async (currentUserRole) => {
  const allRoles = [
    { value: 'staff', label: 'Nhân viên' },
    { value: 'leader', label: 'Trưởng nhóm' },
    { value: 'departmentHead', label: 'Trưởng phòng' },
    { value: 'hr', label: 'Nhân sự' },
  ];
  if (!currentUserRole) return allRoles;
  const currentIndex = roleHierarchy.indexOf(currentUserRole);
  // Chỉ lấy các role thấp hơn role hiện tại, không bao giờ có admin
  return allRoles.filter(role => roleHierarchy.indexOf(role.value) < currentIndex);
};

const UserUpdate = ({ user = {}, onClose, onSubmit }) => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    department: '',
    position: '',
    role: '',
    employeeCode: '',
  });
  const [departments, setDepartments] = useState([]);
  const [roleOptions, setRoleOptions] = useState([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null); // 'department' | 'role' | null
  const dropdownRef = useRef(null);
  const modalRef = useRef(null);
  const [validationErrors, setValidationErrors] = useState({});
  const timeoutRef = useRef(null);

  // Lấy thông tin user hiện tại để kiểm tra quyền
  const getCurrentUser = () => {
    try {
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      return currentUser;
    } catch {
      return {};
    }
  };

  const currentUser = getCurrentUser();
  const currentUserRole = currentUser.role?.toLowerCase();

  const handleClose = useCallback(() => {
    if (!isUpdating) {
      // Clear timeout nếu có
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      onClose();
    }
  }, [isUpdating, onClose]);

  useEffect(() => {
    setError('');
    setSuccess(false);
    
    const departmentValue = user.department?._id || user.department?.id || user.departmentId || user.department || '';
    
    setForm({
      id: user._id || user.id || '',
      employeeCode: user.employeeCode || '',
      name: user.fullName || user.name || '',
      email: user.email || '',
      // Department có thể là object hoặc string ID hoặc departmentId
      department: departmentValue,
      position: user.position || '',
      role: user.role || '',
    });
  }, [user, onClose]);
  
  useEffect(() => {
    fetchRoles(currentUserRole).then(setRoleOptions);
    getDepartmentsList().then(res => {
      if (res && res.data) {
        setDepartments(res.data);
      }
    });
  }, [currentUserRole]);

  // Handle click outside dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdown(null);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle click outside modal to close
  useEffect(() => {
    function handleModalClickOutside(event) {
      if (modalRef.current && event.target === modalRef.current) {
        if (!isUpdating) {
          handleClose();
        }
      }
    }
    document.addEventListener("mousedown", handleModalClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleModalClickOutside);
    };
  }, [isUpdating, handleClose]);

  // Cleanup timeout khi component unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Kiểm tra quyền của departmentHead
  const isDepartmentHead = currentUserRole === 'departmenthead';

  // departmentHead chỉ có thể sửa user có role là leader hoặc staff
  const canEditThisUser = !isDepartmentHead || ['leader', 'staff'].includes(user.role?.toLowerCase());

  // departmentHead chỉ có thể sửa position và role
  const canEditField = (fieldName) => {
    if (!isDepartmentHead) return true; // Admin, CEO, HR có thể sửa tất cả
    return ['position', 'role'].includes(fieldName);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess(false);
    setValidationErrors({});

    if (!canEditThisUser) {
      setError('Bạn không có quyền sửa thông tin nhân viên này!');
      return;
    }

    // Validate form
    const errors = validateUserUpdateForm({
      name: form.name,
      email: form.email,
      department: form.department,
      position: form.position,
      role: form.role,
    });

    // Check if there are any errors
    const hasErrors = Object.values(errors).some(error => error !== '');
    if (hasErrors) {
      setValidationErrors(errors);
      return;
    }

    // Kiểm tra nếu role thay đổi thành departmentHead, phòng ban đó không được có trưởng phòng khác
    if (form.role === 'departmentHead' && form.department && user.role !== 'departmentHead') {
      try {
        const checkResult = await checkDepartmentHead(
          // Tìm department code từ form.department (id)
          departments.find(dept => dept.id === form.department)?.code,
          form.id // exclude current user
        );
        if (!checkResult.success && checkResult.data?.hasHead) {
          setError(`${checkResult.message}. Vui lòng chọn phòng ban khác hoặc role khác.`);
          return;
        }
      } catch (err) {
        setError('Không thể kiểm tra trạng thái trưởng phòng. Vui lòng thử lại.');
        return;
      }
    }

    // Chỉ gửi những trường mà departmentHead có thể sửa
    let dataToUpdate = { ...form };
    if (isDepartmentHead) {
      dataToUpdate = {
        position: form.position,
        role: form.role,
      };
    }
    // Đổi name thành fullName cho đúng backend
    if (dataToUpdate.name) {
      dataToUpdate.fullName = dataToUpdate.name;
      delete dataToUpdate.name;
    }
    // Truyền đúng code phòng ban lên backend
    const selectedDepartment = departments.find(dep => dep.id === form.department || dep._id === form.department);
    if (selectedDepartment) {
      dataToUpdate.departmentCode = selectedDepartment.code;
    }
    delete dataToUpdate.department;
    delete dataToUpdate.departmentId;
    delete dataToUpdate.id;
    // Truyền dữ liệu ra ngoài cho ListUser xử lý
    setIsUpdating(true);
    try {
      if (onSubmit) {
        await onSubmit(dataToUpdate, form.id);
        showSuccess('Cập nhật thông tin nhân viên thành công!');
        setSuccess(true);
        
        // Đóng form sau 1 giây để user thấy thông báo thành công
        timeoutRef.current = setTimeout(() => {
          onClose();
        }, 1000);
      }
    } catch (err) {
      const errorMessage = err.message || 'Có lỗi xảy ra khi cập nhật';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="user-update-modal" ref={modalRef}>
      <div className="user-update-dialog">
        <div className="user-update-header">
          <div>
            <div className="user-update-title">Cập nhật thông tin nhân viên</div>
            <div className="user-update-desc">
              {isDepartmentHead
                ? "Bạn chỉ có thể sửa vị trí và chức vụ của Leader/Staff"
                : "Vui lòng cập nhật đầy đủ thông tin nhân viên"
              }
            </div>
          </div>
          <button className="user-update-close" onClick={handleClose} disabled={isUpdating}>×</button>
        </div>
        <form className="user-update-form" onSubmit={handleSubmit} ref={dropdownRef} noValidate>
          {error && <div className="user-update-error">{error}</div>}
          {success && <div className="user-update-success">Cập nhật thành công!</div>}

          {/* Thông tin cá nhân */}
          <div className="user-update-section">
            <div className="user-update-section-title">
              <img className="icon-color" src={usersIcon} alt="user" /> Thông tin cá nhân
            </div>
            <div className="user-update-row">
              <div className="user-update-group">
                <label>Mã nhân viên</label>
                <input name="employeeCode" value={form.employeeCode} onChange={handleChange} disabled />
              </div>
              <div className="user-update-group">
                <label>Họ và tên<span className="required">*</span></label>
                <input
                  name="name"
                  value={form.name || ''}
                  onChange={handleChange}
                  placeholder="Nhập họ và tên đầy đủ"
                  disabled={!canEditField('name')}
                  className={validationErrors.name ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.name || ''}</span>
              </div>
              <div className="user-update-group">
                <label>Email<span className="required">*</span></label>
                <input
                  name="email"
                  value={form.email || ''}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={!canEditField('email')}
                  className={validationErrors.email ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.email || ''}</span>
              </div>
            </div>
          </div>
          {/* Thông tin công việc */}
          <div className="user-update-section">
            <div className="user-update-section-title">
              <img className="icon-color" src={documentIcon} alt="work" /> Thông tin công việc
            </div>
            <div className="user-update-row">
              <div className="user-update-group">
                <label>Phòng ban<span className="required">*</span></label>
                <div className={`user-update-dropdown ${openDropdown === 'department' ? 'open' : ''}`}>
                  <button
                    type="button"
                    className={`user-update-dropdown-btn ${validationErrors.department ? 'error' : ''}`}
                    onClick={() => setOpenDropdown(openDropdown === 'department' ? null : 'department')}
                    disabled={!canEditField('department')}
                  >
                    <span>
                      {form.department
                        ? departments.find(dep => (dep.id || dep._id) === form.department)?.name || 'Chọn phòng ban'
                        : 'Chọn phòng ban'
                      }
                    </span>
                    <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
                  </button>
                  {openDropdown === 'department' && (
                    <div className="user-update-dropdown-menu">
                      {departments.map((dep) => (
                        <div
                          key={dep.id || dep._id}
                          className="user-update-dropdown-item"
                          onClick={() => {
                            setForm(prev => ({ ...prev, department: dep.id || dep._id }));
                            setOpenDropdown(null);
                            // Clear validation error when user selects
                            if (validationErrors.department) {
                              setValidationErrors(prev => ({ ...prev, department: '' }));
                            }
                          }}
                        >
                          {dep.name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <span className="field-error">{validationErrors.department || ''}</span>
              </div>
              <div className="user-update-group">
                <label>Vị trí</label>
                <input
                  name="position"
                  value={form.position || ''}
                  onChange={handleChange}
                  placeholder="Vị trí"
                  disabled={!canEditField('position')}
                  className={validationErrors.position ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.position || ''}</span>
              </div>
              <div className="user-update-group">
                <label>Chức vụ và phân quyền<span className="required">*</span></label>
                <div className={`user-update-dropdown ${openDropdown === 'role' ? 'open' : ''}`}>
                  <button
                    type="button"
                    className={`user-update-dropdown-btn ${validationErrors.role ? 'error' : ''}`}
                    onClick={() => setOpenDropdown(openDropdown === 'role' ? null : 'role')}
                    disabled={!canEditField('role')}
                  >
                    <span>
                      {form.role
                        ? roleOptions.find(role => role.value === form.role)?.label || 'Chọn chức vụ'
                        : 'Chọn chức vụ'
                      }
                    </span>
                    <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
                  </button>
                  {openDropdown === 'role' && (
                    <div className="user-update-dropdown-menu">
                      {roleOptions.map((role) => (
                        <div
                          key={role.value}
                          className="user-update-dropdown-item"
                          onClick={() => {
                            setForm(prev => ({ ...prev, role: role.value }));
                            setOpenDropdown(null);
                            // Clear validation error when user selects
                            if (validationErrors.role) {
                              setValidationErrors(prev => ({ ...prev, role: '' }));
                            }
                          }}
                        >
                          {role.label}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <span className="field-error">{validationErrors.role || ''}</span>
              </div>
            </div>
          </div>
          <button className="user-update-submit" type="submit" disabled={isUpdating}>
            {isUpdating ? 'Đang cập nhật...' : 'Cập nhật thông tin'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default UserUpdate;
