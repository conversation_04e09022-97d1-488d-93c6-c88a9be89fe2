@import url('../index.css');
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@100..900&display=swap');

/* Removed old dashboard layout styles as they're now handled by DashboardLayout */
.dashboard-welcome {
  background: #f0f4fa;
  border-radius: 16px;
  padding: 32px 0;
  margin-bottom: 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.dashboard-welcome h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
}
.dashboard-welcome p {
  color: #888;
  font-size: 16px;
}
.dashboard-section {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  margin-bottom: 32px;
  padding: 24px 32px;
}
.dashboard-section.dashboard-tasks {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 32px 36px 36px 36px;
  margin-bottom: 32px;
  border: none;
}
.dashboard-tasks-row {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  margin-bottom: 16px;
  border-bottom: 1px solid #6D6E6F;
}
.dashboard-tasks-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}
.dashboard-tasks-header span {
  font-size: 18px;
  font-weight: 700;
}
.dashboard-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}
.dashboard-tasks-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 32px;
  margin-left: 0;
  /* Đảm bảo không bị đẩy quá xa */
}
.dashboard-tasks-tabs-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}
.dashboard-tasks-tabs {
  display: flex;
  gap: 32px;
  margin-left: 0;
  flex: 1;
}
.dashboard-tasks-tabs span {
  color: #888;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
}
.dashboard-tasks-tabs .active {
  color: #111;
  font-weight: bold;
}
.dashboard-tasks-tabs .active::after {
  content: '';
  display: block;
  height: 3px;
  background: #222;
  border-radius: 2px;
  margin-top: 6px;
  width: 100%;
}
.dashboard-task-list {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 24px;
  border-bottom: 1.5px solid #e6a6a1;
}
.dashboard-task-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0 0 0 0;
  margin-bottom: 0;
  font-size: 16px;
  box-shadow: none;
  border-top: 1.5px solid #e6a6a1;
  min-height: 48px;
}
.dashboard-task-list li:last-child {
  border-bottom: none;
}
.dashboard-task-project {
  background: #DFD0D0;
  color: #1F1F1F;
  border-radius: 8px;
  padding: 3px 18px;
  font-size: 15px;
  font-weight: 600;
  margin-left: auto;
  margin-right: 12px;
  letter-spacing: 0.01em;
  display: flex;
  align-items: center;
  height: 28px;
}
.dashboard-task-date {
  color: #1F1F1F;
  font-size: 15px;
  min-width: 70px;
  text-align: right;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 28px;
}
.dashboard-add-task {
  background: none;
  color: #888;
  border: none;
  border-radius: 8px;
  padding: 0;
  font-size: 16px;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 12px;
  margin-top: 0;
  text-align: left;
  transition: color 0.2s;
  display: block;
}
.dashboard-add-task:hover {
  color: #e06b6b;
}
.dashboard-bottom {
  display: flex;
  gap: 32px;
  justify-content: space-between;
  align-items: flex-start;
  background: transparent;
  box-shadow: none;
  padding: 0;
}
.dashboard-bottom-row {
  display: flex;
  flex-direction: row;
  gap: 32px;
  margin-top: 24px;
}
.dashboard-projects-list {
  background: #fff;
  border-radius: 16px;
  padding: 24px 24px 40px 24px;
  min-width: 0;
  box-shadow: none;
  margin-bottom: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.dashboard-projects-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 24px;
}
.dashboard-projects-grid {
  display: flex;
  flex-direction: row;
  gap: 7px;
  align-items: flex-start;
}
.dashboard-project-add {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 160px;
  height: 48px;
  border: 0.5px dashed #6D6E6F;
  border-radius: 12px;
  color: #222;
  font-size: 16px;
  font-weight: 500;
  background: #fff;
  cursor: pointer;
  transition: border 0.2s, background 0.2s;
  padding: 0 18px;
  gap: 10px;
  margin-bottom: 0;
}
.dashboard-project-add-icon {
  font-size: 22px;
  font-weight: 700;
  margin-right: 2px;
  color: #bdbdbd;
}
.dashboard-project-add-label {
  font-size: 16px;
  font-weight: 500;
  color: #222;
}
.dashboard-project-add:hover {
  border: 1.5px solid #e06b6b;
  background: #fbeaea;
}
.dashboard-project {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background: #fff;
  color: #222;
  border-radius: 12px;
  padding: 0 18px;
  width: 180px;
  height: 48px;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  border: none;
  box-shadow: none;
  transition: background 0.2s, color 0.2s;
  margin-bottom: 0;
}
.dashboard-project.active, .dashboard-project:hover {
  background: #fbeaea;
  color: #e06b6b;
}
.dashboard-project-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2px;
}
.dashboard-project-label {
  font-size: 16px;
  font-weight: 600;
  color: inherit;
}
.dashboard-team-members {
  background: #fff;
  border-radius: 16px;
  padding: 24px 24px 40px 24px;
  min-width: 0;
  box-shadow: none;
  margin-bottom: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.dashboard-team-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 24px;
}
.dashboard-team-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px 0;
  align-items: center;
  min-height: 180px;
}
.dashboard-team-add {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  box-shadow: none;
  cursor: pointer;
  margin: 0 auto;
  gap: 8px
}
.dashboard-team-add-circle {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px dashed #6D6E6F;
  border-radius: 50%;
  background: #fff;
  color: #222;
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 4px;
  transition: border 0.2s, background 0.2s;
}
.dashboard-team-add-circle:hover {
  border: 2px solid #e06b6b;
  background: #fbeaea;
}
.dashboard-team-add-label {
  font-size: 15px;
  font-weight: 500;
  color: #222;
  text-align: center;
  margin: 0;
  padding: 0;
  line-height: 1.2;
}
.dashboard-member {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-width: 0;
  margin-bottom: 0;
}
.dashboard-member-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: #F44336;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}
.dashboard-member-email {
  font-size: 15px;
  color: #222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 130px;
}
.dashboard-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}
.home-grid {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.home-grid-row {
  width: 100%;
}

.home-main-row {
  display: flex;
  gap: 28px;
}

.home-main-left {
  flex: 2;
}

.home-main-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 28px;
}

@media (max-width: 1100px) {
  .home-main-row {
    flex-direction: column;
  }
  .home-main-left, .home-main-right {
    flex: unset;
  }
}
@media (max-width: 1100px) {
  .dashboard-bottom {
    flex-direction: column;
    gap: 16px;
  }
  .dashboard-bottom-row {
    flex-direction: column;
    gap: 16px;
  }
  .dashboard-projects-list, .dashboard-team-members {
    min-width: unset;
    width: 100%;
    padding: 16px;
  }
  .dashboard-projects-grid, .dashboard-team-grid {
    flex-direction: column;
    gap: 16px;
  }
  .dashboard-team-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 900px) {
  .dashboard-content {
    flex-direction: column;
  }
  .dashboard-main {
    padding: 16px;
  }
  .dashboard-projects-list, .dashboard-team-members {
    min-width: unset;
    width: 100%;
    padding: 16px;
  }
  .dashboard-projects-grid, .dashboard-team-grid {
    flex-direction: column;
    gap: 16px;
  }
}
@media (max-width: 768px) {
  .dashboard-main {
    margin-left: 60px;
  }
  .dashboard-team-list-row {
    grid-template-columns: 1fr;
  }
  .dashboard-team-grid {
    grid-template-columns: 1fr;
  }
}