"use client"

import { useEffect, useState } from "react"
import { useParams, useNavigate } from "react-router-dom"
import { getDepartmentById } from '../../../api/departmentManagement';
import { getUsersByDepartment, getAllUsers } from '../../../api/userManagement';

const DepartmentDetail = () => {
  const { id: departmentId } = useParams()
  const navigate = useNavigate()
  const [department, setDepartment] = useState(null)
  const [members, setMembers] = useState([])
  const [allEmployees, setAllEmployees] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        const [deptRes, membersRes, allEmpRes] = await Promise.all([
          getDepartmentById(departmentId),
          getUsersByDepartment(departmentId),
          getAllUsers(),
        ])

        setDepartment(deptRes.data)
        setMembers(membersRes.data || [])
        setAllEmployees(allEmpRes.data || [])
      } catch (err) {
        setError(err.message || "Lỗi khi tải chi tiết phòng ban")
      } finally {
        setLoading(false)
      }
    }

    if (departmentId) fetchData()
  }, [departmentId])

  if (!departmentId) return null

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          fontSize: 16,
          color: "#6b7280",
        }}
      >
        <div style={{ textAlign: "center" }}>
          <div
            style={{
              width: 40,
              height: 40,
              border: "4px solid #f3f4f6",
              borderTop: "4px solid #3b82f6",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto 16px",
            }}
          ></div>
          Đang tải dữ liệu...
        </div>
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  if (error) {
    return (
      <div
        style={{
          padding: 24,
          textAlign: "center",
          color: "#dc2626",
        }}
      >
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" style={{ margin: "0 auto 16px", color: "#dc2626" }}>
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <div style={{ fontSize: 18, fontWeight: 600, marginBottom: 8 }}>Có lỗi xảy ra</div>
        <div style={{ marginBottom: 16 }}>{error}</div>
        <button
          onClick={() => navigate(-1)}
          style={{
            padding: "8px 16px",
            background: "#3b82f6",
            color: "#fff",
            border: "none",
            borderRadius: 8,
            cursor: "pointer",
          }}
        >
          Quay lại
        </button>
      </div>
    )
  }

  // Trưởng phòng được populate từ backend qua virtual field 'head'
  const headEmployee = department.head
  
  // Hoặc tìm trong danh sách members dựa trên role departmentHead
  const headFromMembers = members.find(member => member.role === 'departmentHead')
  
  // Hoặc tìm theo employeeCode khớp với headEmployeeCode
  const headByEmployeeCode = members.find(member => 
    department.headEmployeeCode && member.employeeCode === department.headEmployeeCode
  )
  
  // Ưu tiên dữ liệu từ populate head, sau đó role, cuối cùng employeeCode
  const actualHead = headEmployee || headFromMembers || headByEmployeeCode

  const roleLabels = {
    departmenthead: "Trưởng phòng",
    leader: "Trưởng nhóm",
    staff: "Nhân viên",
   
  }

  const renderStatusBadge = (isActive) => (
    <span
      style={{
        background: isActive ? "#dcfce7" : "#fef2f2",
        color: isActive ? "#16a34a" : "#dc2626",
        padding: "4px 12px",
        borderRadius: 20,
        fontWeight: 500,
        fontSize: 12,
        textTransform: "uppercase",
        letterSpacing: "0.5px",
      }}
    >
      {isActive ? "Hoạt động" : "Đã khóa"}
    </span>
  )

  const renderRoleBadge = (role) => {
    const colors = {
      departmenthead: { bg: "#fef3c7", color: "#d97706" },
      leader: { bg: "#dbeafe", color: "#1d4ed8" },
      staff: { bg: "#f3f4f6", color: "#374151" },
      intern: { bg: "#fce7f3", color: "#be185d" },
    }

    const style = colors[role] || colors.staff

    return (
      <span
        style={{
          background: style.bg,
          color: style.color,
          padding: "4px 8px",
          borderRadius: 12,
          fontSize: 12,
          fontWeight: 500,
        }}
      >
        {roleLabels[role] || role}
      </span>
    )
  }

  return (
    <div style={{ padding: 24, maxWidth: 1200, margin: "0 auto" }}>
      {/* Header */}
      <div
        style={{
          background: "#fff",
          borderRadius: 16,
          padding: 32,
          marginBottom: 24,
          boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", marginBottom: 24 }}>
          <button
            onClick={() => navigate(-1)}
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              padding: "8px 16px",
              background: "#f8fafc",
              border: "1px solid #e2e8f0",
              borderRadius: 8,
              color: "#475569",
              cursor: "pointer",
              fontSize: 14,
              fontWeight: 500,
              marginRight: 16,
            }}
          >
            ← Quay lại
          </button>

          <div style={{ flex: 1 }}>
            <div style={{ display: "flex", alignItems: "center", gap: 16, marginBottom: 8 }}>
              <div
                style={{
                  width: 48,
                  height: 48,
                  background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
                  borderRadius: 12,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                  <path d="M3 21h18M5 21V7l8-4v18M19 21V10l-6-3"/>
                </svg>
              </div>
              <div>
                <h1 style={{ margin: 0, fontSize: 28, fontWeight: 700, color: "#111827" }}>{department.name}</h1>
                <div style={{ display: "flex", alignItems: "center", gap: 12, marginTop: 4 }}>
                  <span
                    style={{
                      background: "#f1f5f9",
                      color: "#475569",
                      padding: "4px 8px",
                      borderRadius: 6,
                      fontSize: 13,
                      fontWeight: 600,
                      fontFamily: "monospace",
                    }}
                  >
                    {department.code}
                  </span>
                  {renderStatusBadge(department.isActive)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div style={{ borderBottom: "1px solid #e5e7eb" }}>
          <div style={{ display: "flex", gap: 32 }}>
            {[
              { id: "overview", label: "Tổng quan" },
              { id: "members", label: `Nhân viên (${members.length})` },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                  padding: "12px 0",
                  background: "none",
                  border: "none",
                  borderBottom: activeTab === tab.id ? "2px solid #3b82f6" : "2px solid transparent",
                  color: activeTab === tab.id ? "#3b82f6" : "#6b7280",
                  fontWeight: activeTab === tab.id ? 600 : 500,
                  fontSize: 14,
                  cursor: "pointer",
                  transition: "all 0.2s",
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div
        style={{
          background: "#fff",
          borderRadius: 16,
          padding: 32,
          boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        }}
      >
        {activeTab === "overview" && (
          <div>
            <h3 style={{ margin: "0 0 24px 0", fontSize: 20, fontWeight: 600 }}>Thông tin chi tiết</h3>

            <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: 24 }}>
              <div>
                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Tên phòng ban
                  </label>
                  <div style={{ fontSize: 16, color: "#111827" }}>{department.name}</div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Mã phòng ban
                  </label>
                  <div style={{ fontSize: 16, color: "#111827" }}>{department.code}</div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Trưởng phòng
                  </label>
                  <div style={{ fontSize: 16, color: "#111827" }}>
                    {actualHead ? (
                      <div>
                        <div style={{ fontWeight: 500 }}>{actualHead.fullName}</div>
                        <div style={{ color: "#6b7280", fontSize: 14 }}>{actualHead.email}</div>
                        {actualHead.employeeCode && (
                          <div style={{ color: "#6b7280", fontSize: 12 }}>Mã NV: {actualHead.employeeCode}</div>
                        )}
                      </div>
                    ) : (
                      <span style={{ color: "#9ca3af", fontStyle: "italic" }}>Chưa có</span>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Số lượng nhân viên
                  </label>
                  <div style={{ fontSize: 24, fontWeight: 700, color: "#3b82f6" }}>{members.length}</div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Trạng thái
                  </label>
                  <div>{renderStatusBadge(department.isActive)}</div>
                </div>

                <div style={{ marginBottom: 16 }}>
                  <label style={{ display: "block", fontSize: 14, fontWeight: 600, color: "#374151", marginBottom: 4 }}>
                    Ngày tạo
                  </label>
                  <div style={{ fontSize: 16, color: "#111827" }}>
                    {department.createdAt ? new Date(department.createdAt).toLocaleDateString("vi-VN") : "N/A"}
                  </div>
                </div>
              </div>
            </div>


          </div>
        )}

        {activeTab === "members" && (
          <div>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: 24 }}>
              <h3 style={{ margin: 0, fontSize: 20, fontWeight: 600 }}>Danh sách nhân viên ({members.length})</h3>
              <button
                onClick={() => navigate(`/hr?department=${encodeURIComponent(department.name)}`)}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 8,
                  padding: "8px 16px",
                  background: "#3b82f6",
                  color: "#fff",
                  border: "none",
                  borderRadius: 8,
                  fontSize: 14,
                  fontWeight: 500,
                  cursor: "pointer",
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                Quản lý nhân sự
              </button>
            </div>

            {members.length > 0 ? (
              <div style={{ overflowX: "auto", borderRadius: 12, border: "1px solid #e5e7eb" }}>
                <table style={{ width: "100%", borderCollapse: "collapse" }}>
                  <thead>
                    <tr style={{ background: "#f8fafc" }}>
                      <th
                        style={{
                          padding: "12px 16px",
                          textAlign: "left",
                          fontWeight: 600,
                          color: "#374151",
                          fontSize: 14,
                        }}
                      >
                        Nhân viên
                      </th>
                      <th
                        style={{
                          padding: "12px 16px",
                          textAlign: "left",
                          fontWeight: 600,
                          color: "#374151",
                          fontSize: 14,
                        }}
                      >
                        Email
                      </th>
                      <th
                        style={{
                          padding: "12px 16px",
                          textAlign: "center",
                          fontWeight: 600,
                          color: "#374151",
                          fontSize: 14,
                        }}
                      >
                        Chức vụ
                      </th>
                      <th
                        style={{
                          padding: "12px 16px",
                          textAlign: "center",
                          fontWeight: 600,
                          color: "#374151",
                          fontSize: 14,
                        }}
                      >
                        Trạng thái
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {members.map((member) => (
                      <tr
                        key={member._id || member.id}
                        style={{
                          borderBottom: "1px solid #f3f4f6",
                          transition: "background-color 0.2s",
                        }}
                        onMouseEnter={(e) => (e.target.closest("tr").style.background = "#f8fafc")}
                        onMouseLeave={(e) => (e.target.closest("tr").style.background = "#fff")}
                      >
                        <td style={{ padding: "12px 16px" }}>
                          <div style={{ fontWeight: 500, color: "#111827" }}>{member.fullName}</div>
                        </td>
                        <td style={{ padding: "12px 16px", color: "#6b7280" }}>{member.email}</td>
                        <td style={{ padding: "12px 16px", textAlign: "center" }}>{renderRoleBadge(member.role)}</td>
                        <td style={{ padding: "12px 16px", textAlign: "center" }}>
                          {renderStatusBadge(member.isActive)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div
                style={{
                  textAlign: "center",
                  padding: "48px 20px",
                  color: "#6b7280",
                }}
              >
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" style={{ margin: "0 auto 16px", opacity: 0.5 }}>
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>Chưa có nhân viên nào</div>
                <div style={{ fontSize: 14 }}>Phòng ban này chưa có nhân viên được phân công</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default DepartmentDetail
