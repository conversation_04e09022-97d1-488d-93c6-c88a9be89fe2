import userAvatar from '../assets/user1.png';

const myJobsData = [
  {
    id: 'TSK-27',
    code: 'TSK-27',
    name: '<PERSON><PERSON><PERSON> giao diện trang chủ dự án',
    project: 'SDTC',
    status: 'waiting',
    priority: 'high',
    dueDate: '20/06/2025',
    startDate: '15/06/2025',
    creator: { name: '<PERSON><PERSON><PERSON>', avatar: userAvatar },
    assignee: [{ name: '<PERSON><PERSON><PERSON>', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 25,
    description: 'Thiết kế và phát triển giao diện trang chủ cho dự án SDTC với các tính năng hiện đại và thân thiện với người dùng.',
    activities: [
      { user: 'Tr<PERSON>ành', content: 'Đã tạo wireframe cho trang chủ', time: 'Hôm qua' },
      { user: '<PERSON><PERSON><PERSON>', content: '<PERSON><PERSON><PERSON> đầu coding giao diện', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Homepage_Design.figma', date: 'Hôm qua', type: 'figma' },
      { name: 'Requirements.pdf', date: '2 ngày trước', type: 'pdf' }
    ]
  },
  {
    id: 'TSK-28',
    code: 'TSK-28',
    name: 'Thiết kế logo cho dự án mới',
    project: 'SDTC',
    status: 'waiting',
    priority: 'high',
    dueDate: '25/06/2025',
    startDate: '18/06/2025',
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    assignee: [{ name: 'Minh Tuấn', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 10,
    description: 'Tạo logo chuyên nghiệp và ấn tượng cho dự án mới, phù hợp với thương hiệu và mục tiêu của công ty.',
    activities: [
      { user: 'Minh Tuấn', content: 'Nghiên cứu thương hiệu và đối thủ', time: 'Hôm qua' },
      { user: 'Minh Tuấn', content: 'Tạo concept đầu tiên', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Brand_Guidelines.pdf', date: 'Hôm qua', type: 'pdf' }
    ]
  },
  {
    id: 'TSK-29',
    code: 'TSK-29',
    name: 'Tích hợp API đăng nhập',
    project: 'SDTC',
    status: 'waiting',
    priority: 'medium',
    dueDate: '22/06/2025',
    startDate: '16/06/2025',
    creator: { name: 'Hồng Nhung', avatar: userAvatar },
    assignee: [{ name: 'Hồng Nhung', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 40,
    description: 'Tích hợp API đăng nhập với hệ thống xác thực, đảm bảo bảo mật và trải nghiệm người dùng tốt.',
    activities: [
      { user: 'Hồng Nhung', content: 'Đã setup API endpoints', time: '2 ngày trước' },
      { user: 'Hồng Nhung', content: 'Đang test authentication flow', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'API_Documentation.docx', date: '2 ngày trước', type: 'docx' },
      { name: 'Test_Cases.xlsx', date: 'Hôm qua', type: 'xlsx' }
    ]
  },
  {
    id: 'TSK-30',
    code: 'TSK-30',
    name: 'Chuẩn bị tài liệu hướng dẫn sử dụng',
    project: 'SDTC',
    status: 'waiting',
    priority: 'normal',
    dueDate: '30/06/2025',
    startDate: '20/06/2025',
    creator: { name: 'Quốc Huy', avatar: userAvatar },
    assignee: [{ name: 'Quốc Huy', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 15,
    description: 'Tạo tài liệu hướng dẫn sử dụng chi tiết cho người dùng cuối, bao gồm các tính năng chính và cách sử dụng.',
    activities: [
      { user: 'Quốc Huy', content: 'Bắt đầu thu thập yêu cầu', time: 'Hôm qua' },
      { user: 'Quốc Huy', content: 'Tạo outline cho tài liệu', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'User_Requirements.docx', date: 'Hôm qua', type: 'docx' }
    ]
  },
  {
    id: 'TSK-31',
    code: 'TSK-31',
    name: 'Phát triển tính năng tìm kiếm',
    project: 'ChopChat',
    status: 'in-progress',
    priority: 'high',
    dueDate: '15/07/2025',
    startDate: '10/07/2025',
    creator: { name: 'Thanh Tâm', avatar: userAvatar },
    assignee: [{ name: 'Thanh Tâm', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 60,
    description: 'Phát triển tính năng tìm kiếm nâng cao với khả năng lọc và sắp xếp kết quả.',
    activities: [
      { user: 'Thanh Tâm', content: 'Hoàn thành backend search API', time: '2 ngày trước' },
      { user: 'Thanh Tâm', content: 'Đang phát triển frontend interface', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Search_API_Spec.pdf', date: '2 ngày trước', type: 'pdf' },
      { name: 'UI_Mockup.figma', date: 'Hôm qua', type: 'figma' }
    ]
  },
  {
    id: 'TSK-32',
    code: 'TSK-32',
    name: 'Tối ưu hóa hiệu suất trang chủ',
    project: 'SDTC',
    status: 'in-progress',
    priority: 'medium',
    dueDate: '18/07/2025',
    startDate: '12/07/2025',
    creator: { name: 'Bảo Ngọc', avatar: userAvatar },
    assignee: [{ name: 'Bảo Ngọc', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 35,
    description: 'Tối ưu hóa hiệu suất trang chủ để cải thiện tốc độ tải và trải nghiệm người dùng.',
    activities: [
      { user: 'Bảo Ngọc', content: 'Phân tích hiệu suất hiện tại', time: '3 ngày trước' },
      { user: 'Bảo Ngọc', content: 'Tối ưu hóa hình ảnh và CSS', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Performance_Report.pdf', date: '3 ngày trước', type: 'pdf' }
    ]
  },
  {
    id: 'TSK-33',
    code: 'TSK-33',
    name: 'Sửa lỗi hiển thị trên thiết bị di động',
    project: 'ChopChat',
    status: 'in-progress',
    priority: 'normal',
    dueDate: '22/07/2025',
    startDate: '15/07/2025',
    creator: { name: 'Đình Phúc', avatar: userAvatar },
    assignee: [{ name: 'Đình Phúc', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 70,
    description: 'Sửa các lỗi hiển thị và responsive trên thiết bị di động để đảm bảo trải nghiệm tốt.',
    activities: [
      { user: 'Đình Phúc', content: 'Xác định các lỗi responsive', time: '4 ngày trước' },
      { user: 'Đình Phúc', content: 'Sửa lỗi layout trên mobile', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Mobile_Issues.xlsx', date: '4 ngày trước', type: 'xlsx' },
      { name: 'Fix_Screenshots.zip', date: 'Hôm qua', type: 'zip' }
    ]
  },
  {
    id: 'TSK-34',
    code: 'TSK-34',
    name: 'Cập nhật cơ sở dữ liệu người dùng',
    project: 'SDTC',
    status: 'in-progress',
    priority: 'medium',
    dueDate: '25/07/2025',
    startDate: '18/07/2025',
    creator: { name: 'Thảo Linh', avatar: userAvatar },
    assignee: [{ name: 'Thảo Linh', avatar: userAvatar }],
    statusColor: '#1890ff',
    progress: 45,
    description: 'Cập nhật cấu trúc cơ sở dữ liệu người dùng để hỗ trợ các tính năng mới.',
    activities: [
      { user: 'Thảo Linh', content: 'Backup dữ liệu hiện tại', time: '2 ngày trước' },
      { user: 'Thảo Linh', content: 'Thực hiện migration script', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Database_Schema.sql', date: '2 ngày trước', type: 'sql' },
      { name: 'Migration_Plan.docx', date: 'Hôm qua', type: 'docx' }
    ]
  },
  {
    id: 'TSK-35',
    code: 'TSK-35',
    name: 'Thiết kế trang đăng nhập',
    project: 'SDTC',
    status: 'completed',
    priority: 'high',
    dueDate: '10/06/2025',
    startDate: '05/06/2025',
    creator: { name: 'Quang Vinh', avatar: userAvatar },
    assignee: [{ name: 'Quang Vinh', avatar: userAvatar }],
    statusColor: '#52c41a',
    progress: 100,
    description: 'Thiết kế giao diện trang đăng nhập với UX/UI hiện đại và thân thiện.',
    activities: [
      { user: 'Quang Vinh', content: 'Hoàn thành wireframe', time: '5 ngày trước' },
      { user: 'Quang Vinh', content: 'Thiết kế hoàn tất', time: '3 ngày trước' }
    ],
    attachments: [
      { name: 'Login_Design.figma', date: '3 ngày trước', type: 'figma' },
      { name: 'Design_Assets.zip', date: '3 ngày trước', type: 'zip' }
    ]
  },
  {
    id: 'TSK-36',
    code: 'TSK-36',
    name: 'Tạo tài liệu API cho nhóm phát triển',
    project: 'ChopChat',
    status: 'completed',
    priority: 'medium',
    dueDate: '05/06/2025',
    startDate: '01/06/2025',
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    assignee: [{ name: 'Anh Tuấn', avatar: userAvatar }],
    statusColor: '#52c41a',
    progress: 100,
    description: 'Tạo tài liệu API chi tiết để hỗ trợ nhóm phát triển trong việc tích hợp.',
    activities: [
      { user: 'Anh Tuấn', content: 'Thu thập thông tin API endpoints', time: '7 ngày trước' },
      { user: 'Anh Tuấn', content: 'Hoàn thành tài liệu', time: '5 ngày trước' }
    ],
    attachments: [
      { name: 'API_Documentation.pdf', date: '5 ngày trước', type: 'pdf' },
      { name: 'Postman_Collection.json', date: '5 ngày trước', type: 'json' }
    ]
  },
  {
    id: 'TSK-37',
    code: 'TSK-37',
    name: 'Tích hợp thanh tìm kiếm',
    project: 'SDTC',
    status: 'completed',
    priority: 'normal',
    dueDate: '08/06/2025',
    startDate: '03/06/2025',
    creator: { name: 'Mai Linh', avatar: userAvatar },
    assignee: [{ name: 'Mai Linh', avatar: userAvatar }],
    statusColor: '#52c41a',
    progress: 100,
    description: 'Tích hợp thanh tìm kiếm với khả năng tìm kiếm nhanh và chính xác.',
    activities: [
      { user: 'Mai Linh', content: 'Phát triển search component', time: '8 ngày trước' },
      { user: 'Mai Linh', content: 'Hoàn thành tích hợp', time: '6 ngày trước' }
    ],
    attachments: [
      { name: 'Search_Component.jsx', date: '6 ngày trước', type: 'jsx' }
    ]
  },
  {
    id: 'TSK-38',
    code: 'TSK-38',
    name: 'Cập nhật giao diện người dùng',
    project: 'ChopChat',
    status: 'completed',
    priority: 'normal',
    dueDate: '12/06/2025',
    startDate: '07/06/2025',
    creator: { name: 'Đức Anh', avatar: userAvatar },
    assignee: [{ name: 'Đức Anh', avatar: userAvatar }],
    statusColor: '#52c41a',
    progress: 100,
    description: 'Cập nhật giao diện người dùng để cải thiện trải nghiệm và tính thẩm mỹ.',
    activities: [
      { user: 'Đức Anh', content: 'Thiết kế mockup mới', time: '10 ngày trước' },
      { user: 'Đức Anh', content: 'Hoàn thành implementation', time: '7 ngày trước' }
    ],
    attachments: [
      { name: 'UI_Updates.figma', date: '7 ngày trước', type: 'figma' },
      { name: 'Style_Guide.pdf', date: '7 ngày trước', type: 'pdf' }
    ]
  },
  {
    id: 'TSK-39',
    code: 'TSK-39',
    name: 'Kiểm thử tính năng giỏ hàng',
    project: 'ChopChat',
    status: 'consider',
    priority: 'high',
    dueDate: '28/06/2025',
    startDate: '20/06/2025',
    creator: { name: 'Hà My', avatar: userAvatar },
    assignee: [{ name: 'Hà My', avatar: userAvatar }],
    statusColor: '#fa8c16',
    progress: 80,
    description: 'Kiểm thử toàn diện tính năng giỏ hàng để đảm bảo hoạt động ổn định.',
    activities: [
      { user: 'Hà My', content: 'Tạo test cases', time: '5 ngày trước' },
      { user: 'Hà My', content: 'Thực hiện testing', time: 'Hôm nay' }
    ],
    attachments: [
      { name: 'Test_Cases.xlsx', date: '5 ngày trước', type: 'xlsx' },
      { name: 'Bug_Report.pdf', date: 'Hôm qua', type: 'pdf' }
    ]
  }
];

// Function to get my jobs
export const getMyJobs = () => {
  return myJobsData;
};

export default myJobsData; 