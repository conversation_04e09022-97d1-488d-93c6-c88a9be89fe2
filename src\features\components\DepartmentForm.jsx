"use client"

import { useState, useEffect, useRef } from "react"
import { createDepartment, updateDepartment } from '../../api/departmentManagement';
import { getEligibleDepartmentHeads } from '../../api/userManagement';
import { showSuccess, showError } from '../../components/Toastify';
import '../../styles/DepartmentForm.css';
import departmentIcon from '../../assets/icon-sidebar/doingu.svg';
import usersIcon from '../../assets/users.svg';
import dropdownIcon from '../../assets/icon-sidebar/dropdown.svg';

const DepartmentForm = ({ department, onSuccess, onCancel }) => {
  const isEdit = !!department
  const [formData, setFormData] = useState({
    name: department?.name || "",
    code: department?.code || "",
    headEmployeeCode: department?.headEmployeeCode || "",
    isActive: department?.isActive !== undefined ? department.isActive : true,
  })
  const [eligibleHeads, setEligibleHeads] = useState([])
  const [selectedHead, setSelectedHead] = useState(null)
  const [headSearchTerm, setHeadSearchTerm] = useState("")
  const [showHeadSuggestions, setShowHeadSuggestions] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  const [validationErrors, setValidationErrors] = useState({})
  
  const headInputRef = useRef(null)
  const headSuggestionsRef = useRef(null)
  const modalRef = useRef(null)

  useEffect(() => {
    fetchEligibleHeads()
    initializeSelectedHead()
  }, [])

  // Handle click outside suggestions
  useEffect(() => {
    function handleClickOutside(event) {
      if (headSuggestionsRef.current && !headSuggestionsRef.current.contains(event.target) &&
          headInputRef.current && !headInputRef.current.contains(event.target)) {
        setShowHeadSuggestions(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle click outside modal to close
  useEffect(() => {
    function handleModalClickOutside(event) {
      if (modalRef.current && event.target === modalRef.current) {
        if (!loading) {
          onCancel();
        }
      }
    }
    document.addEventListener("mousedown", handleModalClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleModalClickOutside);
    };
  }, [loading, onCancel]);

  const fetchEligibleHeads = async (searchTerm = "") => {
    try {
      const excludeId = isEdit ? (department._id || department.id) : null
      const response = await getEligibleDepartmentHeads(searchTerm, excludeId)
      setEligibleHeads(response.data || [])
    } catch (err) {
      console.error("Error fetching eligible heads:", err)
    }
  }

  const initializeSelectedHead = () => {
    if (isEdit && formData.headEmployeeCode) {
      // Tìm thông tin trưởng phòng hiện tại từ department data nếu có
      if (department?.head) {
        setSelectedHead(department.head)
        setHeadSearchTerm(`${department.head.fullName} (${department.head.employeeCode})`)
      } else {
        setHeadSearchTerm(formData.headEmployeeCode)
      }
    }
  }

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    if (error) setError(null)
  }

  const validate = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = "Tên phòng ban là bắt buộc";
    } else if (formData.name.length > 100) {
      errors.name = "Tên phòng ban không được vượt quá 100 ký tự";
    }
    
    if (!formData.code.trim()) {
      errors.code = "Mã phòng ban là bắt buộc";
    } else if (formData.code.length > 20) {
      errors.code = "Mã phòng ban không được vượt quá 20 ký tự";
    }
    
    return errors;
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    setSuccess(false)
    setValidationErrors({})
    
    if (loading) return

    // Validate form
    const errors = validate()
    const hasErrors = Object.values(errors).some(error => error !== '');
    if (hasErrors) {
      setValidationErrors(errors);
      return;
    }

    setLoading(true)

    try {
      const payload = {
        ...formData,
        code: formData.code.toUpperCase(),
      }

      if (isEdit) {
        await updateDepartment(department._id || department.id, payload)
        showSuccess("Cập nhật phòng ban thành công")
      } else {
        await createDepartment(payload)
        showSuccess("Tạo phòng ban thành công")
      }

      setSuccess(true)
      
      // Close form after success
      setTimeout(() => {
        if (onSuccess) onSuccess()
      }, 1000)

    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || "Lỗi khi lưu phòng ban"
      setError(errorMessage)
      showError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleHeadSearchChange = (value) => {
    setHeadSearchTerm(value)
    setShowHeadSuggestions(true)
    
    // Debounce search
    clearTimeout(window.headSearchTimeout)
    window.headSearchTimeout = setTimeout(() => {
      fetchEligibleHeads(value)
    }, 300)
  }

  const handleHeadSelect = (head) => {
    setSelectedHead(head)
    setHeadSearchTerm(`${head.fullName} (${head.employeeCode})`)
    setFormData(prev => ({ ...prev, headEmployeeCode: head.employeeCode }))
    setShowHeadSuggestions(false)
    
    // Hiển thị thông báo nếu user đang ở phòng ban khác
    if (head.departmentInfo && isEdit && head.departmentInfo.id !== (department._id || department.id)) {
      showSuccess(`${head.fullName} sẽ rời khỏi phòng ban "${head.departmentInfo.name}" và được thăng chức lên trưởng phòng`)
    } else if (head.departmentInfo && !isEdit) {
      showSuccess(`${head.fullName} sẽ rời khỏi phòng ban "${head.departmentInfo.name}" và được thăng chức lên trưởng phòng`)
    }
  }

  const handleHeadClear = () => {
    setSelectedHead(null)
    setHeadSearchTerm("")
    setFormData(prev => ({ ...prev, headEmployeeCode: "" }))
    setShowHeadSuggestions(false)
  }

  const handleClose = () => {
    if (!loading) {
      onCancel()
    }
  }

  return (
    <div className="department-form-modal" ref={modalRef}>
      <div className="department-form-dialog">
        <div className="department-form-header">
          <div>
            <div className="department-form-title">
              {isEdit ? "Cập nhật phòng ban" : "Thêm phòng ban mới"}
            </div>
            <div className="department-form-desc">
              {isEdit ? "Chỉnh sửa thông tin phòng ban" : "Tạo phòng ban mới trong hệ thống"}
            </div>
          </div>
          <button className="department-form-close" onClick={handleClose} disabled={loading}>×</button>
        </div>
        
        <form className="department-form-form" onSubmit={handleSubmit} noValidate>
          {/* Thông tin cơ bản */}
          <div className="department-form-section">
            <div className="department-form-section-title">
              <img className="icon-blue" src={departmentIcon} alt="department" /> Thông tin cơ bản
            </div>
            <div className="department-form-row">
              <div className="department-form-group">
                <label>Tên phòng ban<span className="required">*</span></label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nhập tên phòng ban"
                  maxLength={100}
                  className={validationErrors.name ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.name || ''}</span>
              </div>
              <div className="department-form-group">
                <label>Mã phòng ban<span className="required">*</span></label>
                <input
                  type="text"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
                  placeholder="NHẬP MÃ PHÒNG BAN (VD: IT, HR, SALES)"
                  maxLength={20}
                  className={validationErrors.code ? 'error' : ''}
                  style={{ textTransform: "uppercase" }}
                />
                <span className="field-error">{validationErrors.code || ''}</span>
              </div>
            </div>
          </div>

          {/* Thông tin quản lý */}
          <div className="department-form-section">
            <div className="department-form-section-title">
              <img className="icon-blue" src={usersIcon} alt="users" /> Thông tin quản lý
            </div>
            <div className="department-form-row">
              <div className="department-form-group">
                <label>Trưởng phòng</label>
                <div className="department-form-autocomplete">
                  <input
                    ref={headInputRef}
                    type="text"
                    value={headSearchTerm}
                    onChange={(e) => handleHeadSearchChange(e.target.value)}
                    onFocus={() => {
                      setShowHeadSuggestions(true)
                      if (!headSearchTerm) fetchEligibleHeads()
                    }}
                    placeholder="Tìm kiếm theo tên, email hoặc mã nhân viên..."
                    className="department-form-autocomplete-input"
                  />
                  {selectedHead && (
                    <button
                      type="button"
                      className="department-form-autocomplete-clear"
                      onClick={handleHeadClear}
                    >
                      ×
                    </button>
                  )}
                  {showHeadSuggestions && eligibleHeads.length > 0 && (
                    <div ref={headSuggestionsRef} className="department-form-autocomplete-menu">
                      {eligibleHeads.map((head) => (
                        <div
                          key={head.id}
                          className="department-form-autocomplete-item"
                          onClick={() => handleHeadSelect(head)}
                        >
                          <div className="autocomplete-item-main">
                            <span className="autocomplete-item-name">{head.fullName}</span>
                            <span className="autocomplete-item-code">({head.employeeCode})</span>
                          </div>
                          <div className="autocomplete-item-details">
                            <span className="autocomplete-item-email">{head.email}</span>
                            <span className="autocomplete-item-role">{head.roleText}</span>
                            {head.departmentInfo && (
                              <span className="autocomplete-item-dept">
                                {head.departmentInfo.name}
                                {isEdit && head.departmentInfo.id !== (department._id || department.id) && (
                                  <span style={{ color: '#dc2626', fontSize: '11px', marginLeft: '4px' }}>
                                    (sẽ rời khỏi)
                                  </span>
                                )}
                                {!isEdit && (
                                  <span style={{ color: '#dc2626', fontSize: '11px', marginLeft: '4px' }}>
                                    (sẽ rời khỏi)
                                  </span>
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  {showHeadSuggestions && headSearchTerm && eligibleHeads.length === 0 && (
                    <div ref={headSuggestionsRef} className="department-form-autocomplete-menu">
                      <div className="department-form-autocomplete-item no-results">
                        Không tìm thấy nhân viên phù hợp
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Thông báo cảnh báo khi chọn trưởng phòng từ phòng ban khác */}
                {selectedHead && selectedHead.departmentInfo && 
                 ((isEdit && selectedHead.departmentInfo.id !== (department._id || department.id)) || 
                  (!isEdit && selectedHead.departmentInfo.id)) && (
                  <div style={{ 
                    marginTop: '8px', 
                    padding: '8px 12px', 
                    backgroundColor: '#fef3c7', 
                    border: '1px solid #f59e0b', 
                    borderRadius: '6px',
                    fontSize: '12px',
                    color: '#92400e'
                  }}>
                    <strong>{selectedHead.fullName}</strong> sẽ rời khỏi phòng ban "{selectedHead.departmentInfo.name}" và được thăng chức lên trưởng phòng.
                  </div>
                )}
    
              </div>
            </div>
            
            <div className="department-form-checkbox">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.isActive}
                onChange={(e) => handleInputChange("isActive", e.target.checked)}
              />
              <label htmlFor="isActive">Phòng ban đang hoạt động</label>
            </div>
           
          </div>

          {error && (
            <div className="department-form-error">
              {error}
            </div>
          )}

          {success && (
            <div className="department-form-success">
              {isEdit ? "Cập nhật phòng ban thành công!" : "Tạo phòng ban thành công!"}
            </div>
          )}

          <div className="department-form-actions">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="department-form-btn department-form-btn-cancel"
            >
              Hủy bỏ
            </button>
            <button
              type="submit"
              disabled={loading}
              className="department-form-btn department-form-btn-submit"
            >
              {loading ? "Đang xử lý..." : isEdit ? "Cập nhật" : "Tạo mới"}
            </button>
          </div>
          
          <div className="department-form-note">*Các trường bắt buộc</div>
        </form>
      </div>
    </div>
  )
}

export default DepartmentForm
