import React, { useState, useEffect, useRef } from "react";
import calendarIcon from "../../assets/startdate.svg";
import user1 from "../../assets/user1.png";
import attachIcon from "../../assets/attachment.svg";
import addIcon from "../../assets/add.svg";
import closeIcon from "../../assets/closePanel.svg";
import "../../styles/StatisticalUpdate.css";

const StatisticalUpdate = ({ project, onClose, onUpdate }) => {
  const [name, setName] = useState("");
  const [desc, setDesc] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [progress, setProgress] = useState(0);
  const [done, setDone] = useState(0);
  const [total, setTotal] = useState(0);
  const [attachment, setAttachment] = useState(null);
  const [attachmentName, setAttachmentName] = useState("");
  const fileInputRef = useRef();

  useEffect(() => {
    if (project) {
      setName(project.name || "");
      setDesc(project.desc || "");
      setStartDate(project.startDate || "");
      setEndDate(project.dueDate || "");
      setProgress(project.progress || 0);
      setDone(project.done || 0);
      setTotal(project.total || 0);
    }
  }, [project]);

  const handleAttachClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAttachment(file);
      setAttachmentName(file.name);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validation cho dates
    const today = new Date().toISOString().split('T')[0];
    if (startDate && startDate < today) {
      alert("Thời gian bắt đầu không thể là quá khứ");
      return;
    }
    if (endDate && endDate < today) {
      alert("Thời gian kết thúc không thể là quá khứ");
      return;
    }
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      alert("Thời gian kết thúc phải sau thời gian bắt đầu");
      return;
    }
    
    if (onUpdate) {
      onUpdate({
        ...project,
        name,
        desc,
        startDate,
        dueDate: endDate,
        progress,
        done,
        total,
        attachment, // truyền file đính kèm
      });
    }
    if (onClose) onClose();
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Sửa dự án: {name}</h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>
        <form className="project-form" onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label>ID Dự án</label>
              <input type="text" value={project?.id || ""} disabled />
            </div>
            <div className="form-group">
              <label>Tên dự án</label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
          </div>
          <div className="form-group">
            <label>Mô tả</label>
            <textarea
              rows={3}
              value={desc}
              onChange={(e) => setDesc(e.target.value)}
            />
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Thời gian bắt đầu</label>
              <div className="input-icon">
                <input
                  type="date"
                  value={startDate}
                  min={new Date().toISOString().split('T')[0]}
                  onChange={(e) => setStartDate(e.target.value)}
                />
                <img
                  src={calendarIcon}
                  alt="calendar"
                  className="icon-calendar"
                />
              </div>
            </div>
            <div className="form-group">
              <label>Thời gian kết thúc</label>
              <div className="input-icon">
                <input
                  type="date"
                  value={endDate}
                  min={new Date().toISOString().split('T')[0]}
                  onChange={(e) => setEndDate(e.target.value)}
                />
                <img
                  src={calendarIcon}
                  alt="calendar"
                  className="icon-calendar"
                />
              </div>
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Thành viên thực hiện</label>
              <div className="members">
                <img src={user1} alt="user1" className="avatar" />
                <img src={user1} alt="user2" className="avatar" />
                <img src={user1} alt="user3" className="avatar" />
                <img src={user1} alt="user4" className="avatar" />
                <button className="add-member" type="button">
                  <img
                    src={addIcon}
                    alt="Thêm"
                    style={{ width: 18, height: 18 }}
                  />
                </button>
              </div>
            </div>
            <div className="form-group">
              <label>Tệp đính kèm</label>
              <button
                className="attach-btn"
                type="button"
                onClick={handleAttachClick}
              >
                <img
                  src={attachIcon}
                  alt="Đính kèm"
                  style={{ width: 18, height: 18 }}
                />
              </button>
              <input
                type="file"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleFileChange}
              />
              {attachmentName && (
                <span className="attachment-name">{attachmentName}</span>
              )}
            </div>
          </div>
          <div className="progress-section">
            <label>Tiến độ</label>
            <div className="progress-bar">
              <div className="progress" style={{ width: `${progress}%` }}></div>
            </div>
            <div className="progress-info">
              <span>{progress}%</span>
              <span>
                {done}/{total} công việc hoàn thành
              </span>
            </div>
          </div>
          <button type="submit" className="update-btn">
            Cập nhật dự án
          </button>
        </form>
      </div>
    </div>
  );
};

export default StatisticalUpdate;