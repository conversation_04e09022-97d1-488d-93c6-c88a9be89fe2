@import url('../index.css');

.department-form-modal {
  position: fixed;
  z-index: 1000;
  inset: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}

.department-form-dialog {
  background: #fff;
  border-radius: 16px;
  width: 800px;
  max-width: 99vw;
  height: 600px;
  max-height: 90vh;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 0 0 24px 0;
  animation: fadeIn .18s;
  display: flex;
  flex-direction: column;
}

.department-form-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 18px 20px 0 20px;
}

.department-form-title {
  font-size: 20px;
  font-weight: 700;
  color: #222;
}

.department-form-desc {
  font-size: 15px;
  color: #888;
  margin-top: 2px;
}

.department-form-close {
  background: none;
  border: none;
  font-size: 28px;
  color: #888;
  cursor: pointer;
  margin-left: 8px;
  margin-top: -8px;
}

.department-form-close:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.department-form-form {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-sizing: border-box;
  flex: 1;
}

.department-form-section {
  background: #fafbfc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 0;
}

.department-form-section-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2d5be3;
  font-size: 15px;
  margin-bottom: 16px;
  gap: 8px;
}

.icon-blue {
  width: 18px;
  height: 18px;
  filter: brightness(0) saturate(100%) invert(36%) sepia(98%) saturate(641%) hue-rotate(191deg) brightness(102%) contrast(101%);
}

.department-form-row {
  display: flex;
  gap: 18px;
  width: 100%;
  box-sizing: border-box;
}

.department-form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
}

.department-form-group label {
  font-size: 14px;
  color: #444;
  font-weight: 500;
  margin-bottom: 2px;
}

.department-form-group input,
.department-form-group textarea {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 15px;
  color: #222;
  background: #fff;
  outline: none;
  transition: border 0.15s;
  width: 100%;
  box-sizing: border-box;
}

.department-form-group input:focus,
.department-form-group textarea:focus {
  border: 1.5px solid #2d5be3;
  background: #fff;
}

/* Custom Dropdown */
.department-form-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

.department-form-dropdown-btn {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 15px;
  background: #fff;
  cursor: pointer;
  text-align: left;
  font-weight: 400;
  color: #222;
  box-shadow: none;
  outline: none;
  transition: border 0.15s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.department-form-dropdown-btn:focus {
  border: 1.5px solid #2d5be3;
}

.department-form-dropdown-btn .dropdown-icon {
  width: 16px;
  height: 16px;
  opacity: 1;
  pointer-events: none;
  flex-shrink: 0;
}

.department-form-dropdown-menu {
  display: none;
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
  z-index: 100;
  padding: 8px 0;
  margin-top: 4px;
  max-height: 145px;
  overflow-y: auto;
  box-sizing: border-box;
}

.department-form-dropdown.open .department-form-dropdown-menu {
  display: block;
}

.department-form-dropdown-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #000;
  cursor: pointer;
  background: #fff;
  transition: background 0.15s;
  white-space: nowrap;
}

.department-form-dropdown-item:hover {
  background: #f5f7fa;
}

/* Validation Error Styles */
.department-form-group input.error,
.department-form-group textarea.error,
.department-form-dropdown-btn.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.12);
}

.field-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 4px;
  display: block;
  min-height: 12px;
  line-height: 12px;
}

.department-form-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}

.department-form-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #2d5be3;
}

.department-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.department-form-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.department-form-btn-cancel {
  background: #fff;
  color: #6b7280;
  border: 2px solid #e5e7eb;
}

.department-form-btn-cancel:hover:not(:disabled) {
  border-color: #d1d5db;
  background: #f9fafb;
}

.department-form-btn-cancel:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.department-form-btn-submit {
  background: #2d5be3;
  color: #fff;
  min-width: 120px;
}

.department-form-btn-submit:hover:not(:disabled) {
  background: #1a3fa1;
}

.department-form-btn-submit:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.department-form-error {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 16px;
  color: #c33;
  font-size: 14px;
  margin-top: 12px;
}

.department-form-success {
  background: #efe;
  border: 1px solid #cfc;
  border-radius: 8px;
  padding: 16px;
  color: #363;
  font-size: 14px;
  margin-top: 12px;
}

.department-form-note {
  font-size: 12px;
  color: #e74c3c;
margin-top: 0px;
  text-align: center;
}

.required {
  color: #e74c3c;
  margin-left: 2px;
}

/* Autocomplete Styles */
.department-form-autocomplete {
  position: relative;
  width: 100%;
}

.department-form-autocomplete-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 15px;
  transition: all 0.2s;
  background: #fff;
}

.department-form-autocomplete-input:focus {
  outline: none;
  border-color: #2d5be3;
  box-shadow: 0 0 0 3px rgba(45, 91, 227, 0.1);
}

.department-form-autocomplete-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: #e5e7eb;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #6b7280;
  transition: all 0.2s;
}

.department-form-autocomplete-clear:hover {
  background: #d1d5db;
  color: #374151;
}

.department-form-autocomplete-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
}

.department-form-autocomplete-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.department-form-autocomplete-item:last-child {
  border-bottom: none;
}

.department-form-autocomplete-item:hover {
  background: #f8fafc;
}

.department-form-autocomplete-item.no-results {
  color: #6b7280;
  cursor: default;
  text-align: center;
  font-style: italic;
}

.department-form-autocomplete-item.no-results:hover {
  background: transparent;
}

.autocomplete-item-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.autocomplete-item-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.autocomplete-item-code {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
}

.autocomplete-item-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.autocomplete-item-email {
  color: #4b5563;
}

.autocomplete-item-role {
  background: #eff6ff;
  color: #2563eb;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.autocomplete-item-dept {
  background: #f0f9ff;
  color: #0ea5e9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.department-form-help-text {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  font-style: italic;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 700px) {
  .department-form-dialog {
    width: 99vw;
    height: 90vh;
    padding: 0;
  }
  
  .department-form-header {
    padding: 16px;
  }
  
  .department-form-form {
    padding: 16px;
    gap: 16px;
  }
  
  .department-form-section {
    padding: 16px;
  }
  
  .department-form-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .department-form-actions {
    margin-top: 16px;
    padding-top: 16px;
  }
} 