/* Delete Confirmation Popup Styles */
.delete-confirm-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

.delete-confirm-popup {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
}

.delete-confirm-header {
  padding: 24px 24px 0 24px;
  border-bottom: none;
}

.delete-confirm-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  text-align: left;
}

.delete-confirm-content {
  padding: 16px 24px 24px 24px;
}

.delete-confirm-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  text-align: left;
}

.delete-confirm-actions {
  padding: 0 16px 16px 16px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.delete-confirm-cancel-btn {
  background: white;
  border: 1px solid #fff;
  color: #5D5D5D;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 105px;
}

.delete-confirm-cancel-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.delete-confirm-delete-btn {
  background: #dc2626;
  border: 1px solid #dc2626;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 105px;
}

.delete-confirm-delete-btn:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.delete-confirm-delete-btn:active {
  background-color: #991b1b;
  border-color: #991b1b;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .delete-confirm-popup {
    width: 95%;
    margin: 20px;
  }
  
  .delete-confirm-header {
    padding: 20px 20px 0 20px;
  }
  
  .delete-confirm-title {
    font-size: 18px;
  }
  
  .delete-confirm-content {
    padding: 12px 20px 20px 20px;
  }
  
  .delete-confirm-message {
    font-size: 13px;
  }
  
  .delete-confirm-actions {
    padding: 0 20px 20px 20px;
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .delete-confirm-cancel-btn,
  .delete-confirm-delete-btn {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
  }
}
