/* Dashboard Layout Styles */
.dashboard-root {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  overflow: hidden;
}

.dashboard-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.dashboard-main {
  flex: 1;
  padding: 75px 20px 40px 20px;
  margin-left: 250px;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #f7f8fa;
  min-height: 0;
}

/* Disable page scroll for specific pages that have table scroll */
.dashboard-main.no-page-scroll {
  overflow-y: hidden;
}

/* Scrollbar styling for main content */
.dashboard-main::-webkit-scrollbar {
  width: 6px;
}

.dashboard-main::-webkit-scrollbar-track {
  background: transparent;
}

.dashboard-main::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.dashboard-main:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.dashboard-main::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 0;
    padding: 20px 16px;
  }
}

/* Ensure content containers don't overflow */
.dashboard-main > * {
  max-width: 100%;
}

/* Specific styles for different page types */
.dashboard-main .statistical-list-container,
.dashboard-main .my-job-container,
.dashboard-main .personal-notes-container,
.dashboard-main .activity-log-container,
.dashboard-main .documents-main-content,
.dashboard-main .members-container {
  width: 100%;
  height: 100%;
}

/* Ensure proper spacing for content */
.dashboard-main .home-grid {
  width: 100%;
  min-height: 100%;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 28px;
}

/* Ensure Home page components display correctly */
.dashboard-main .home-grid-row {
  width: 100%;
}

.dashboard-main .home-main-row {
  display: flex;
  gap: 28px;
}

.dashboard-main .home-main-left {
  flex: 2;
}

.dashboard-main .home-main-right {
  display: flex;
  flex-direction: column;
  gap: 28px;
}
