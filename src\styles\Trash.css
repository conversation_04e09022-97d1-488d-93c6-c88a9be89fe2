@import url('../index.css');
.trash-container {
  background: #f7f8fa;
  border-radius: 12px;
}
.trash-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 20px 24px;
}
.trash-title {
  display: flex;
  align-items: center;
  gap: 12px;
}
.trash-title-main {
  font-size: 22px;
  font-weight: 600;
  color: #5d5d5d;
}
.trash-title-desc {
  font-size: 15px;
  color: #5d5d5d;
}
.trash-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.trash-icon {
  width: 24px;
  height: 24px;
  display: block;
}
.trash-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
.trash-search-wrapper {
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 20px;
  padding: 0 12px;
  min-width: 220px;
  height: 40px;
  margin-right: 12px;
}
.trash-search-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  opacity: 0.7;
}
.trash-search {
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  flex: 1;
  padding: 8px 0;
}
.trash-empty-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}
.trash-empty-btn:hover {
  background: #c0392b;
}
.trash-empty-icon {
  width: 20px;
  height: 20px;
  display: block;
}
.trash-stats-row {
  display: flex;
  gap: 18px;
  margin-bottom: 18px;
}
.trash-stat-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 18px 22px;
  flex: 1;
  min-width: 180px;
}
.trash-stat-title {
  font-size: 15px;
  color: #5d5d5d;
  font-weight: 500;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
}
.trash-stat-icon img {
  width: 20px;
  height: 20px;
  display: block;
}
.trash-stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #222;
  margin-bottom: 4px;
}
.trash-stat-desc {
  font-size: 12px;
  color: #888;
}
.trash-tabs {
  display: flex;
  gap: 4px;
  background: #ECEEF080;
  border-radius: 12px;
  padding: 6px;
  width: fit-content;
}
.trash-tab {
  background: transparent;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 15px;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  color: #5d5d5d;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
  white-space: nowrap;
}
.trash-tab.active {
  background: #fff;
  color: #5d5d5d;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.trash-tab:hover:not(.active) {
  background: rgba(255,255,255,0.5);
}
.trash-bottom-actions {
  display: flex;
  gap: 12px;
}
.trash-report-btn, .trash-refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fff;
  color: #5d5d5d;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.trash-report-btn:hover, .trash-refresh-btn:hover {
  background: #f2f3f5;
  border-color: #ddd;
}
.trash-bottom-icon {
  width: 18px;
  height: 18px;
  display: block;
}
.trash-actions-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  margin-bottom: 16px;
}

/* Skeleton Loading Styles */
.trash-skeleton-container {
  /* Đảm bảo skeleton có cùng chiều cao và cuộn như danh sách thật */
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  /* margin-top giữ lại nếu cần, hoặc set về 0 nếu muốn sát header */
  margin-top: 0;
}

.trash-skeleton-item {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  display: flex;
  align-items: flex-start;
  gap: 16px;
  opacity: 0.7;
  cursor: default;
  pointer-events: none;
  animation: fadeInOut 2s ease-in-out infinite;
}

.trash-skeleton-checkbox {
  width: 18px;
  height: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 3px;
  margin-top: 2px;
}

.trash-skeleton-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trash-skeleton-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trash-skeleton-icon {
  width: 20px;
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.trash-skeleton-title {
  height: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 200px;
}

.trash-skeleton-type {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 60px;
}

.trash-skeleton-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.trash-skeleton-text {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.trash-skeleton-text:nth-child(1) {
  width: 180px;
}

.trash-skeleton-text:nth-child(2) {
  width: 150px;
}

.trash-skeleton-text:nth-child(3) {
  width: 120px;
}

.trash-skeleton-members {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.trash-skeleton-member {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.trash-skeleton-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.trash-skeleton-action-type {
  height: 16px;
  width: 50px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.trash-skeleton-more-btn {
  width: 24px;
  height: 24px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}
/* Animations */
@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.4; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}