import { getCurrentUserRole, getEndpointsByRole } from './endpoints';
import { ADMIN_ENDPOINTS } from './endpoints';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;
    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Helper function để lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
  const role = userRaw.user?.role?.toLowerCase() || userRaw.role?.toLowerCase() || getCurrentUserRole();
  if (role === 'admin') {
    return ADMIN_ENDPOINTS;
  }
  return getEndpointsByRole(role);
};

// ========== USER MANAGEMENT FUNCTIONS ==========

// Lấy danh sách tất cả nhân viên
export async function getAllUsers(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const queryString = new URLSearchParams(params).toString();
    const url = `${endpoints.USERS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Tạo nhân viên mới
export async function createUser(userData) {
  try {
    const endpoints = getCurrentEndpoints();
    // Đảm bảo truyền departmentId nếu có
    const dataToSend = { ...userData };
    if (dataToSend.departmentId) {
      dataToSend.department = dataToSend.departmentId;
      delete dataToSend.departmentId;
    }
    const response = await fetch(endpoints.CREATE_USER, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(dataToSend),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật thông tin nhân viên
export async function updateUser(userId, userData) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.UPDATE_USER(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khóa/mở khóa tài khoản nhân viên
export async function toggleUserStatus(userId, isBlocked) {
  try {
    const endpoints = getCurrentEndpoints();
    // Sử dụng endpoint phù hợp dựa trên role
    const url = endpoints.TOGGLE_USER_STATUS ?
      endpoints.TOGGLE_USER_STATUS(userId) :
      endpoints.TOGGLE_USER_BLOCK(userId);

    const response = await fetch(url, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ isBlocked }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Xóa nhân viên
export async function deleteUser(userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.DELETE_USER(userId), {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Thay đổi role nhân viên
export async function changeUserRole(userId, newRole) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.CHANGE_USER_ROLE(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ newRole }),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Khôi phục nhân viên đã xóa
export async function restoreUser(userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.RESTORE_USER(userId), {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách nhân viên đã xóa
export async function getDeletedUsers() {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.DELETED_USERS, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy lịch sử hoạt động của nhân viên
export async function getUserActivities(userId) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.USER_ACTIVITIES(userId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== EXPORT FUNCTIONS ==========

// Xuất danh sách nhân viên
export async function exportUserList(params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const queryString = new URLSearchParams({ ...params, export: 'true' }).toString();
    const url = `${endpoints.EXPORT_USERS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== UTILITY FUNCTIONS ==========

// Debug function để log cấu trúc data từ backend
export const debugUserData = (backendUser, index = 0) => {
  console.log(`=== DEBUG USER DATA ${index} ===`);
  console.log('Full user object:', backendUser);
  console.log('Department fields:');
  console.log('- backendUser.department:', backendUser.department);
  console.log('- backendUser.departmentId:', backendUser.departmentId);
  console.log('- backendUser.departmentName:', backendUser.departmentName);
  console.log('- typeof department:', typeof backendUser.department);
  console.log('- typeof departmentId:', typeof backendUser.departmentId);

  if (backendUser.department && typeof backendUser.department === 'object') {
    console.log('- department.name:', backendUser.department.name);
    console.log('- department.code:', backendUser.department.code);
  }

  if (backendUser.departmentId && typeof backendUser.departmentId === 'object') {
    console.log('- departmentId.name:', backendUser.departmentId.name);
    console.log('- departmentId.code:', backendUser.departmentId.code);
  }
  console.log('=== END DEBUG ===');
};

// Transform dữ liệu từ backend về format frontend
export const transformUserData = (backendUser) => {
  // Xử lý department theo nhiều format có thể từ backend
  let departmentName = 'Chưa phân công';

  if (backendUser.departmentId?.name) {
    // Format như Profile: departmentId là object với name
    departmentName = backendUser.departmentId.name;
  } else if (backendUser.department?.name) {
    // Format: department là object với name
    departmentName = backendUser.department.name;
  } else if (backendUser.departmentName) {
    // Format: departmentName là string riêng
    departmentName = backendUser.departmentName;
  } else if (typeof backendUser.department === 'string' && backendUser.department && backendUser.department !== 'Chưa phân công') {
    // Format: department là string
    departmentName = backendUser.department;
  } else if (typeof backendUser.departmentId === 'string' && backendUser.departmentId) {
    // Format: departmentId là string (ID only) - sẽ được resolve sau
    departmentName = `ID:${backendUser.departmentId}`;
  } else {
    // Fallback cuối cùng - thử tìm trong các trường khác
    const possibleDeptFields = [
      backendUser.dept,
      backendUser.phongban,
      backendUser.department_name,
      backendUser.departmentCode
    ];

    for (const field of possibleDeptFields) {
      if (field && typeof field === 'string' && field !== 'Chưa phân công') {
        departmentName = field;
        break;
      }
    }
  }

  return {
    id: backendUser._id || backendUser.id || 'N/A',
    employeeCode: backendUser.employeeCode || 'N/A',
    fullName: backendUser.fullName || backendUser.name || 'Chưa có tên',
    name: backendUser.fullName || backendUser.name || 'Chưa có tên',
    email: backendUser.email || 'Chưa có email',
    avatar: backendUser.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
    department: backendUser.departmentInfo?.name || departmentName,
    departmentId: backendUser.departmentInfo?.id || backendUser.departmentId || null,
    role: backendUser.role || 'staff',
    status: backendUser.isBlocked ? 'locked' : (backendUser.isActive !== false ? 'active' : 'locked'),
    createdAt: backendUser.createdAt ? new Date(backendUser.createdAt).toLocaleDateString('vi-VN') : 'N/A',
    position: backendUser.position || 'Chưa xác định'
  };
};

// Kiểm tra quyền của user hiện tại
export const checkUserPermissions = () => {
  const role = getCurrentUserRole();
  return {
    canViewUsers: ['admin', 'ceo', 'hr', 'departmenthead'].includes(role),
    canCreateUsers: ['admin', 'ceo', 'hr'].includes(role),
    canEditUsers: ['admin', 'ceo', 'hr', 'departmenthead'].includes(role),
    canDeleteUsers: ['admin', 'ceo', 'hr'].includes(role),
    canLockUsers: ['admin', 'ceo', 'hr'].includes(role),
    canChangeRoles: ['admin', 'ceo', 'hr'].includes(role),
    canViewReports: ['admin', 'ceo', 'hr', 'leader'].includes(role),
    role: role
  };
};

// Lấy department name từ department ID (fallback nếu backend không populate)
export const getDepartmentNameById = async (departmentId) => {
  try {
    if (!departmentId) return 'Chưa phân công';

    // Import getDepartmentById từ departmentManagement
    const { getDepartmentById } = await import('./departmentManagement');
    const response = await getDepartmentById(departmentId);

    if (response.success && response.data) {
      return response.data.name || 'Chưa có tên';
    }

    return 'Chưa phân công';
  } catch (err) {
    console.error('Error fetching department name:', err);
    return 'Chưa phân công';
  }
};

// Enhanced transform với department lookup
export const transformUserDataWithDepartmentLookup = async (backendUser) => {
  const basicTransform = transformUserData(backendUser);

  // Nếu department đã có tên, return luôn
  if (basicTransform.department !== 'Chưa phân công') {
    return basicTransform;
  }

  // Nếu có departmentId nhưng chưa có tên, thử lookup
  const departmentId = backendUser.departmentId || backendUser.department;
  if (departmentId && typeof departmentId === 'string') {
    const departmentName = await getDepartmentNameById(departmentId);
    return {
      ...basicTransform,
      department: departmentName
    };
  }

  return basicTransform;
};

// Lấy danh sách thành viên của một dự án
export async function getProjectMembers(projectId) {
  try {
    const endpoints = getCurrentEndpoints();
    const response = await fetch(endpoints.PROJECT_MEMBERS(projectId), {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách thành viên trong phòng ban
export async function getUsersByDepartment(departmentId, params = {}) {
  try {
    const endpoints = getCurrentEndpoints();
    const queryString = new URLSearchParams(params).toString();
    const url = `${endpoints.USERS_BY_DEPARTMENT(departmentId)}${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy danh sách user có thể làm trưởng phòng
export async function getEligibleDepartmentHeads(search = '', excludeDepartmentId = null) {
  try {
    const queryParams = new URLSearchParams();
    if (search.trim()) {
      queryParams.append('search', search.trim());
    }
    if (excludeDepartmentId) {
      queryParams.append('excludeDepartmentId', excludeDepartmentId);
    }
    
    const url = `${ADMIN_ENDPOINTS.USERS}/eligible-department-heads${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Kiểm tra phòng ban đã có trưởng phòng chưa
export async function checkDepartmentHead(departmentCode, excludeUserId = null) {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append('departmentCode', departmentCode);
    if (excludeUserId) {
      queryParams.append('excludeUserId', excludeUserId);
    }
    
    const url = `${ADMIN_ENDPOINTS.USERS}/check-department-head?${queryParams.toString()}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Export getCurrentUserRole để sử dụng ở nơi khác
export { getCurrentUserRole };
