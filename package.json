{"name": "project-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "build:prod": "vite build --mode production", "preview:prod": "npm run build:prod && vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hello-pangea/dnd": "^18.0.1", "@mui/material": "^7.2.0", "@react-oauth/google": "^0.12.2", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "multer": "^2.0.1", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "xlsx": "^0.15.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.5", "vite": "^6.3.5"}}