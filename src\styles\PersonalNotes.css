@import url('../index.css');
/* CSS cho trang <PERSON>hi chú cá nhân */
.personal-notes-container {
  width: 100%;
  height: calc(100vh - 139px); /* Fixed height to prevent page scroll */
  background-color: #FAFBFC;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent container scroll */
}

.personal-notes-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 10px;
  flex-direction: row;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.personal-notes-header h1 {
  display: flex;
  align-items: center;
  font-size: 22px;
  color: #5D5D5D;
  margin: 0;
  font-weight: 600;
}

.notes-title {
  margin: 0;
}

.notes-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.notes-search {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 240px;
}

.notes-search input {
  width: 100%;
  padding: 8px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  font-size: 14px;
  outline: none;
  background-color: white;
}

.notes-search input:focus {
  border-color: #4f92ff;
}

.search-button {
  position: absolute;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: #757575;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 20px;
  flex: 1; /* Take remaining space */
  overflow-y: auto; /* Allow scrolling within grid */
  padding-bottom: 20px; /* Add some bottom padding */
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

.notes-grid:hover {
  scrollbar-color: #d1d5db transparent;
}

.notes-grid::-webkit-scrollbar {
  width: 6px;
}

.notes-grid::-webkit-scrollbar-track {
  background: transparent;
}

.notes-grid::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notes-grid:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.notes-grid::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.note-card {
  cursor: pointer;
  transition: transform 0.2s ease;
  height: 150px;
  width: 150px;
  position: relative;
}

.note-card:hover {
  transform: translateY(-5px);
}

.sticky-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: contain;
}

.note-content {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  padding: 22px 15px 15px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 2;
  box-sizing: border-box;
  max-width: 100%;
}

.note-content h3,
.note-content p,
.note-input,
.note-textarea {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.note-content h3 {
  font-size: 13px;
  margin: 0 0 6px 0;
  color: #333;
  font-weight: 500;
  position: relative;
  z-index: 2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-content p {
  font-size: 12px;
  color: #555;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  position: relative;
  z-index: 2;
  margin: 0;
  line-height: 1.3;
  max-width: 100%;
}

/* Styles cho nút thêm ghi chú mới dạng sticky note */
.new-note-card {
  cursor: pointer;
}

/* Các styles cũ không còn sử dụng - có thể xóa */
.add-note-card {
  background-color: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.add-note-card:hover {
  background-color: #eee;
  border-color: #aaa;
}

.add-note-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  height: 100%;
  justify-content: center;
  z-index: 2;
}

.add-note-content img {
  width: 30px;
  height: 30px;
  margin-bottom: 10px;
}

.add-note-content p {
  color: #666;
  font-size: 14px;
}
/* Media queries for responsiveness */
@media (max-width: 768px) {
  .personal-notes-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-left {
    margin-bottom: 15px;
  }
  
  .notes-search {
    width: 100%;
    max-width: 100%;
  }
  
  .notes-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}