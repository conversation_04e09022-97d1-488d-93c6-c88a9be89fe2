import userAvatar from '../assets/user1.png';

const tasksData = [
  {
    id: 'TSK-01',
    name: '<PERSON><PERSON><PERSON> diện trang chủ dự án',
    status: 'in-progress',
    priority: 'high',
    assignee: [
      { avatar: userAvatar, name: '<PERSON><PERSON><PERSON><PERSON>' },
      { avatar: userAvatar, name: '<PERSON><PERSON>' },
      { avatar: userAvatar, name: '<PERSON><PERSON>n <PERSON>' },
      { avatar: userAvatar, name: '<PERSON><PERSON>' },
    ],
    dueDate: '19/8/2025',
    startDate: '19/6/2025',
    creator: { name: '<PERSON><PERSON><PERSON>', avatar: userAvatar },
    description:
      'Nhóm thống nhất công nghệ, ngôn ngữ lập trình và công cụ phát triển. Sau đó tiến hành cài đặt phần mềm, cấu hình môi trường và quản lý mã nguồn để sẵn sàng cho quá trình lập trình.',
    attachments: [
      { name: '<PERSON><PERSON><PERSON> c<PERSON>u khách hàng.xls', date: 'Hôm nay', type: 'xls' },
      { name: 'SRS.docx', date: 'Hôm qua', type: 'docx' },
    ],
    activities: [
      { user: 'Gia Bảo', content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan', time: 'Hôm nay' },
      { user: 'Tấn Sanh', content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan', time: 'Hôm qua' },
      { user: 'Tấn Sanh', content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan', time: 'Hôm qua' },
      { user: 'Tấn Sanh', content: 'Phần này tôi chưa rõ bạn giải thích giúp tôi với @Letuan', time: 'Hôm qua' },
    ],
    children: [
      {
        id: 'TSK-07',
        name: 'Làm Header',
        status: 'completed',
        priority: 'high',
        assignee: [
          { avatar: userAvatar, name: 'Tấn Sanh' },
          { avatar: userAvatar, name: 'Letuan' },
        ],
        dueDate: '10/06/2023',
        comments: 6,
      },
      {
        id: 'TSK-06',
        name: 'Làm thanh tìm kiếm',
        status: 'waiting',
        priority: 'normal',
        assignee: [
          { avatar: userAvatar, name: 'Gia Bảo' },
          { avatar: userAvatar, name: 'Letuan' },
        ],
        dueDate: '20/06/2023',
      },
      {
        id: 'TSK-02',
        name: 'Làm section 1',
        status: 'waiting',
        priority: 'normal',
        assignee: [
          { avatar: userAvatar, name: 'Trí Thành' },
          { avatar: userAvatar, name: 'Letuan' },
        ],
        dueDate: '25/06/2023',
      },
    ],
  },

  {
    id: 'TSK-08',
    name: 'Phát triển module đăng nhập',
    status: 'in-progress',
    priority: 'high',
    assignee: [
      { avatar: userAvatar, name: 'Minh Tuấn' },
      { avatar: userAvatar, name: 'Hồng Nhung' },
      { avatar: userAvatar, name: 'Quốc Huy' },
      { avatar: userAvatar, name: 'Thanh Tâm' },
    ],
    dueDate: '25/9/2025',
    startDate: '01/7/2025',
    creator: { name: 'Minh Tuấn', avatar: userAvatar },
    description:
      'Nhóm tiến hành thiết kế và phát triển module đăng nhập, bao gồm giao diện người dùng, xác thực người dùng và tích hợp với cơ sở dữ liệu. Đảm bảo tuân thủ các yêu cầu bảo mật và hiệu suất.',
    attachments: [
      { name: 'Yêu cầu bảo mật.pdf', date: 'Hôm nay', type: 'pdf' },
      { name: 'Thiết kế UI.docx', date: 'Hôm qua', type: 'docx' },
    ],
    activities: [
      { user: 'Hồng Nhung', content: 'Cần làm rõ yêu cầu xác thực 2 yếu tố @Thanh Tâm', time: 'Hôm nay' },
      { user: 'Quốc Huy', content: 'Đã hoàn thành giao diện đăng nhập, cần review @Minh Tuấn', time: 'Hôm qua' },
      { user: 'Thanh Tâm', content: 'Đã cập nhật tài liệu yêu cầu @Hồng Nhung', time: 'Hôm qua' },
      { user: 'Minh Tuấn', content: 'Đã cấu hình môi trường backend @Quốc Huy', time: 'Hôm qua' },
    ],
    children: [
      {
        id: 'TSK-09',
        name: 'Thiết kế giao diện đăng nhập',
        status: 'completed',
        priority: 'high',
        assignee: [
          { avatar: userAvatar, name: 'Hồng Nhung' },
          { avatar: userAvatar, name: 'Quốc Huy' },
        ],
        dueDate: '15/07/2025',
        comments: 4,
      },
      {
        id: 'TSK-10',
        name: 'Tích hợp API xác thực',
        status: 'in-progress',
        priority: 'high',
        assignee: [
          { avatar: userAvatar, name: 'Minh Tuấn' },
          { avatar: userAvatar, name: 'Thanh Tâm' },
        ],
        dueDate: '20/07/2025',
      },
      {
        id: 'TSK-11',
        name: 'Kiểm thử bảo mật',
        status: 'waiting',
        priority: 'normal',
        assignee: [
          { avatar: userAvatar, name: 'Quốc Huy' },
          { avatar: userAvatar, name: 'Thanh Tâm' },
        ],
        dueDate: '22/07/2025',
      },
    ],
  },

  {
    id: 'TSK-16',
    name: 'Xây dựng API cho tính năng giỏ hàng',
    status: 'in-progress',
    priority: 'high',
    assignee: [
      { avatar: userAvatar, name: 'Bảo Ngọc' },
      { avatar: userAvatar, name: 'Đình Phúc' },
      { avatar: userAvatar, name: 'Thảo Linh' },
      { avatar: userAvatar, name: 'Quang Vinh' },
    ],
    dueDate: '15/10/2025',
    startDate: '25/7/2025',
    creator: { name: 'Bảo Ngọc', avatar: userAvatar },
    description:
      'Nhóm tiến hành thiết kế và phát triển API cho tính năng giỏ hàng, bao gồm các endpoint để thêm, xóa, cập nhật sản phẩm và tính toán tổng giá. Đảm bảo tích hợp với cơ sở dữ liệu và kiểm tra tính ổn định.',
    attachments: [
      { name: 'API Specification.docx', date: 'Hôm nay', type: 'docx' },
      { name: 'Database Schema.pdf', date: 'Hôm qua', type: 'pdf' },
    ],
    activities: [
      { user: 'Đình Phúc', content: 'Đã hoàn thành endpoint thêm sản phẩm, cần review @Bảo Ngọc', time: 'Hôm nay' },
      { user: 'Thảo Linh', content: 'Cần làm rõ yêu cầu tính năng cập nhật số lượng @Quang Vinh', time: 'Hôm qua' },
      { user: 'Quang Vinh', content: 'Đã cấu hình môi trường test API @Đình Phúc', time: 'Hôm qua' },
      { user: 'Bảo Ngọc', content: 'Đã gửi tài liệu đặc tả API cho nhóm @Thảo Linh', time: 'Hôm qua' },
    ],
    children: [
      {
        id: 'TSK-17',
        name: 'Thiết kế endpoint thêm sản phẩm',
        status: 'completed',
        priority: 'high',
        assignee: [
          { avatar: userAvatar, name: 'Đình Phúc' },
          { avatar: userAvatar, name: 'Bảo Ngọc' },
        ],
        dueDate: '05/08/2025',
        comments: 3,
      },
      {
        id: 'TSK-18',
        name: 'Tích hợp tính năng xóa sản phẩm',
        status: 'in-progress',
        priority: 'medium',
        assignee: [
          { avatar: userAvatar, name: 'Thảo Linh' },
          { avatar: userAvatar, name: 'Quang Vinh' },
        ],
        dueDate: '10/08/2025',
      },
      {
        id: 'TSK-19',
        name: 'Kiểm thử API giỏ hàng',
        status: 'consider',
        priority: 'medium',
        assignee: [
          { avatar: userAvatar, name: 'Bảo Ngọc' },
          { avatar: userAvatar, name: 'Đình Phúc' },
        ],
        dueDate: '12/08/2025',
      },
    ],
  },

  {
    id: 'TSK-20',
    name: 'Thiết kế giao diện trang sản phẩm',
    status: 'waiting',
    priority: 'medium',
    assignee: [
      { avatar: userAvatar, name: 'Anh Tuấn' },
      { avatar: userAvatar, name: 'Mai Linh' },
      { avatar: userAvatar, name: 'Đức Anh' },
      { avatar: userAvatar, name: 'Hà My' },
    ],
    dueDate: '05/11/2025',
    startDate: '01/8/2025',
    creator: { name: 'Anh Tuấn', avatar: userAvatar },
    description:
      'Nhóm tiến hành thiết kế giao diện cho trang sản phẩm, bao gồm bố cục, hình ảnh sản phẩm, mô tả và nút tương tác. Đảm bảo giao diện thân thiện với người dùng và tương thích trên nhiều thiết bị.',
    attachments: [
      { name: 'Wireframe trang sản phẩm.pdf', date: 'Hôm nay', type: 'pdf' },
      { name: 'Yêu cầu thiết kế.docx', date: 'Hôm qua', type: 'docx' },
    ],
    activities: [
      { user: 'Mai Linh', content: 'Đã gửi wireframe ban đầu, cần ý kiến @Anh Tuấn', time: 'Hôm nay' },
      { user: 'Đức Anh', content: 'Cần xác nhận màu sắc chủ đạo @Hà My', time: 'Hôm qua' },
      { user: 'Hà My', content: 'Đã chuẩn bị tài liệu tham khảo giao diện @Mai Linh', time: 'Hôm qua' },
      { user: 'Anh Tuấn', content: 'Đã họp nhóm để thống nhất yêu cầu thiết kế @Đức Anh', time: 'Hôm qua' },
    ],
    children: [
      {
        id: 'TSK-21',
        name: 'Thiết kế bố cục trang sản phẩm',
        status: 'completed',
        priority: 'high',
        assignee: [
          { avatar: userAvatar, name: 'Mai Linh' },
          { avatar: userAvatar, name: 'Anh Tuấn' },
        ],
        dueDate: '15/08/2025',
        comments: 4,
      },
      {
        id: 'TSK-22',
        name: 'Tích hợp hình ảnh sản phẩm',
        status: 'consider',
        priority: 'medium',
        assignee: [
          { avatar: userAvatar, name: 'Đức Anh' },
          { avatar: userAvatar, name: 'Hà My' },
        ],
        dueDate: '20/08/2025',
      },
      {
        id: 'TSK-23',
        name: 'Kiểm tra giao diện trên thiết bị di động',
        status: 'consider',
        priority: 'normal',
        assignee: [
          { avatar: userAvatar, name: 'Anh Tuấn' },
          { avatar: userAvatar, name: 'Mai Linh' },
        ],
        dueDate: '25/08/2025',
      },
    ],
  },
];

// Deep clone the tasks array for state management
let tasks = JSON.parse(JSON.stringify(tasksData));

// Function to update task status
export const updateTaskStatus = (taskId, newStatus) => {
  // Find the task in the main tasks array
  let taskFound = false;

  // First check parent tasks
  for (let i = 0; i < tasks.length; i++) {
    if (tasks[i].id === taskId) {
      tasks[i].status = newStatus;
      taskFound = true;
      break;
    }

    // Check children if task not found in parent
    if (!taskFound && tasks[i].children) {
      for (let j = 0; j < tasks[i].children.length; j++) {
        if (tasks[i].children[j].id === taskId) {
          tasks[i].children[j].status = newStatus;
          taskFound = true;
          break;
        }
      }
    }

    if (taskFound) break;
  }

  // Return a fresh copy of tasks
  return JSON.parse(JSON.stringify(tasks));
};

// Function to update task priority
export const updateTaskPriority = (taskId, newPriority) => {
  // Find the task in the main tasks array
  let taskFound = false;

  // First check parent tasks
  for (let i = 0; i < tasks.length; i++) {
    if (tasks[i].id === taskId) {
      tasks[i].priority = newPriority;
      taskFound = true;
      break;
    }

    // Check children if task not found in parent
    if (!taskFound && tasks[i].children) {
      for (let j = 0; j < tasks[i].children.length; j++) {
        if (tasks[i].children[j].id === taskId) {
          tasks[i].children[j].priority = newPriority;
          taskFound = true;
          break;
        }
      }
    }

    if (taskFound) break;
  }

  // Return a fresh copy of tasks
  return JSON.parse(JSON.stringify(tasks));
};

// Function to reset tasks to original state (for testing)
export const resetTasks = () => {
  tasks = JSON.parse(JSON.stringify(tasksData));
  return tasks;
};

// Lưu và lấy task list theo từng projectId
export function getTasksByProjectId(projectId) {
  if (!projectId) return tasks; // Nếu không có projectId, trả về tasks mặc định
  const key = `tasks_project_${projectId}`;
  const stored = localStorage.getItem(key);
  if (stored) {
    try {
      return JSON.parse(stored);
    } catch {
      return [];
    }
  }
  // Nếu là project mặc định (ví dụ SDTC), trả về tasks mẫu
  if (projectId === '1' || projectId === 1) return tasks;
  return [];
}

export function saveTasksByProjectId(projectId, taskList) {
  if (!projectId) return;
  const key = `tasks_project_${projectId}`;
  localStorage.setItem(key, JSON.stringify(taskList));
}

export default tasks;