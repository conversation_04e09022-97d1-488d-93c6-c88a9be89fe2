// Import các thư viện và component cần thiết
import React from 'react';
import '../styles/Home.css'
// DashboardSidebar and DashboardTopbar are now handled by DashboardLayout
import Overview from '../features/pages/Home/Overview';
import ProjectInProgress from '../features/pages/Home/ProjectInProgress';
import WorkStatus from '../features/pages/Home/WorkStatus';
import RecentActivity from '../features/pages/Home/RecentActivity';

const Home = () => {


  return (
    <div className="home-grid" style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '28px',
      width: '100%',
      minHeight: '100%'
    }}>
      <div className="home-grid-row" style={{ width: '100%' }}>
        <Overview />
      </div>
      <div className="home-grid-row home-main-row" style={{
        display: 'flex',
        gap: '28px',
        width: '100%'
      }}>
        <div className="home-main-left" style={{ flex: 2 }}>
          <ProjectInProgress />
        </div>
        <div className="home-main-right" style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '28px'
        }}>
          <WorkStatus />
          <RecentActivity />
        </div>
      </div>
    </div>
  );
};

export default Home; // Export component để sử dụng ở nơi khác
