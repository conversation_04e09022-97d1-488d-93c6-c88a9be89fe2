@import url('../index.css');
.trash-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 8px 34px;
  gap: 16px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  border: none;
}
.trash-action-bar span {
  font-weight: 500;
  color: #5b5b5b;
}
.trash-action-bar .clear-selection {
  background: none;
  border: none;
  color: #5B5B5B;
  cursor: pointer;
  margin-left: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s;
}
.trash-action-bar .clear-selection:hover {
  color: #e74c3c;
}
.trash-action-bar .restore-btn,
.trash-action-bar .delete-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #fff;
  border: 1px solid #d6e6fa;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.trash-action-bar .restore-btn {
  color: #5b5b5b;
  background: #fff;
  border: 1px solid #7C7C7C1A;
}
.trash-action-bar .restore-btn:hover {
  background: #f8f9fa;
  border: 1px solid #7C7C7C1A ;
}
.trash-action-bar .delete-btn {
  color: #fff;
  background: #e74c3c;
  border: 1px solid #007BFF1A;
}
.trash-action-bar .delete-btn:hover {
  background: #c0392b;
  border: 1px solid #007BFF1A;
}

.selection-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-start;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-end;
}

.icon-restore-img {
  width: 16px;
  height: 16px;
  display: inline-block;
}

.icon-delete-img {
  width: 16px;
  height: 16px;
  display: inline-block;
  filter: brightness(0) invert(1);
}
