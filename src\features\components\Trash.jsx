import React, { useState } from "react";
import All from "../pages/Trash/TrashAll";
import Job from "../pages/Trash/TrashJob";
import Project from "../pages/Trash/TrashProject";
import TrashIcon from "../../assets/trash.svg";
import Thungrac from "../../assets/trash-2.svg";
import SquareCheckIcon from "../../assets/square-check-big.svg";
import FileTextIcon from "../../assets/file-text.svg";
import SearchIcon from "../../assets/search.svg";
import RefreshIcon from "../../assets/refresh-ccw.svg";
import EmptyTrash from "./TrashEmpty";
import { trashData as initialTrashData } from "../../storage/trashData";
import "../../styles/Trash.css";

const Trash = () => {
  const [tab, setTab] = useState("all");
  const [showEmpty, setShowEmpty] = useState(false);
  const [jobStats, setJobStats] = useState({ jobCount: 0, selectedCount: 0 });
  const [projectStats, setProjectStats] = useState({ projectCount: 0, selectedCount: 0 });

  // Tổng hợp số liệu
  const totalItems = jobStats.jobCount + projectStats.projectCount;
  const totalSelected = jobStats.selectedCount + projectStats.selectedCount;

  return (
    <div className="trash-container">
      <div className="trash-header">
        <div className="trash-title">
          <span className="trash-icon-wrapper">
            <img src={TrashIcon} alt="Trash Icon" className="trash-icon" />
          </span>
          <div>
            <div className="trash-title-main">Thùng rác</div>
            <div className="trash-title-desc">Quản lí các mục đã xóa</div>
          </div>
        </div>
        <div className="trash-actions">
          <span className="trash-search-wrapper">
            <input className="trash-search" placeholder="Tìm kiếm trong thùng rác..." />
            <img src={SearchIcon} alt="Tìm kiếm" className="trash-search-icon" />
          </span>
          {/* <button className="trash-empty-btn" onClick={() => setShowEmpty(true)}>
            <img src={BrushCleaningIcon} alt="Làm trống" className="trash-empty-icon" />
            Làm trống
          </button> */}
        </div>
      </div>
      <div className="trash-stats-row">
        <div className="trash-stat-card">
          <div className="trash-stat-title">
            Tổng số mục{" "}
            <span className="trash-stat-icon">
              <img src={Thungrac} alt="Tổng số mục" />
            </span>
          </div>
          <div className="trash-stat-value">{totalItems}</div>
          <div className="trash-stat-desc">
            {projectStats.projectCount} dự án, {jobStats.jobCount} công việc
          </div>
        </div>
        <div className="trash-stat-card">
          <div className="trash-stat-title">
            Đã chọn{" "}
            <span className="trash-stat-icon">
              <img src={SquareCheckIcon} alt="Đã chọn" />
            </span>
          </div>
          <div className="trash-stat-value">{totalSelected}</div>
          <div className="trash-stat-desc">mục được chọn</div>
        </div>
        <div className="trash-stat-card">
          <div className="trash-stat-title">
            Dự án đã xóa{" "}
            <span className="trash-stat-icon">
              <img src={FileTextIcon} alt="Dự án đã xóa" />
            </span>
          </div>
          <div className="trash-stat-value">{projectStats.projectCount}</div>
          <div className="trash-stat-desc">+1 tháng trước</div>
        </div>
        <div className="trash-stat-card">
          <div className="trash-stat-title">
            Công việc đã xóa{" "}
            <span className="trash-stat-icon">
              <img src={SquareCheckIcon} alt="Công việc đã xóa" />
            </span>
          </div>
          <div className="trash-stat-value">{jobStats.jobCount}</div>
          <div className="trash-stat-desc">Công việc trong thùng rác</div>
        </div>
      </div>
      <div className="trash-actions-row">
        <div className="trash-tabs">
          <button
            className={`trash-tab${tab === "all" ? " active" : ""}`}
            onClick={() => setTab("all")}
          >
            Tất cả ({totalItems})
          </button>
          <button
            className={`trash-tab${tab === "project" ? " active" : ""}`}
            onClick={() => setTab("project")}
          >
            Dự án ({projectStats.projectCount})
          </button>
          <button
            className={`trash-tab${tab === "task" ? " active" : ""}`}
            onClick={() => setTab("task")}
          >
            Công việc ({jobStats.jobCount})
          </button>
        </div>
        <div className="trash-bottom-actions">
          {/* <button className="trash-report-btn">
            <img src={DownloadIcon} alt="Xuất báo cáo" className="trash-bottom-icon" />
            Xuất báo cáo
          </button> */}
          <button className="trash-refresh-btn">
            <img src={RefreshIcon} alt="Làm mới" className="trash-bottom-icon" />
            Làm mới
          </button>
        </div>
      </div>
      {/* Mount cả Project và Job để luôn lấy số liệu, chỉ hiển thị tab active */}
      <div style={{ display: tab === "all" ? "block" : "none" }}><All /></div>
      <div style={{ display: tab === "project" ? "block" : "none" }}><Project onDataChange={setProjectStats} /></div>
      <div style={{ display: tab === "task" ? "block" : "none" }}><Job onDataChange={setJobStats} /></div>
      {/* Mount ẩn để luôn đồng bộ số liệu */}
      <div style={{ display: "none" }}><Project onDataChange={setProjectStats} /></div>
      <div style={{ display: "none" }}><Job onDataChange={setJobStats} /></div>
      {tab === "document" && <TrashDocument />}
      {showEmpty && (
        <div className="empty-trash-overlay">
          <EmptyTrash onCancel={() => setShowEmpty(false)} onConfirm={() => {}} />
        </div>
      )}
    </div>
  );
};

export default Trash;
