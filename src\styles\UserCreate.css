@import url('../index.css');
.user-create-modal {
  position: fixed;
  z-index: 1000;
  inset: 0;
  background: rgba(0,0,0,0.18);
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-create-dialog {
  background: #fff;
  border-radius: 16px;
  width: 800px;
  max-width: 99vw;
  max-height: 90vh;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 0 0 24px 0;
  animation: fadeIn .18s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.user-create-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 18px 20px 0 20px;
}
.user-create-title {
  font-size: 20px;
  font-weight: 700;
  color: #222;
}
.user-create-desc {
  font-size: 15px;
  color: #888;
  margin-top: 2px;
}
.user-create-close {
  background: none;
  border: none;
  font-size: 28px;
  color: #888;
  cursor: pointer;
  margin-left: 8px;
  margin-top: -8px;
}
.user-create-close:disabled {
  color: #ccc;
  cursor: not-allowed;
}
.user-create-form {
  padding: 18px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-sizing: border-box;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}
.user-create-section {
  background: #fafbfc;
  border-radius: 12px;
  padding: 18px 18px 8px 18px;
  margin-bottom: 8px;
}
.user-create-section-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2d5be3;
  font-size: 15px;
  margin-bottom: 12px;
  gap: 8px;
}
.icon-blue {
  width: 18px;
  height: 18px;
  filter: brightness(0) saturate(100%) invert(36%) sepia(98%) saturate(641%) hue-rotate(191deg) brightness(102%) contrast(101%);
}
.user-create-row {
  display: flex;
  gap: 18px;
  width: 100%;
  box-sizing: border-box;
}
.user-create-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 0;
}
.user-create-group label {
  font-size: 14px;
  color: #444;
  font-weight: 500;
}
.user-create-group input,
.user-create-group textarea {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  color: #222;
  background: #fff;
  outline: none;
  transition: border 0.15s;
  width: 100%;
  box-sizing: border-box;
}
.user-create-group input:focus,
.user-create-group textarea:focus {
  border: 1.5px solid #2d5be3;
  background: #fff;
}
/* Custom Dropdown */
.user-create-dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}
.user-create-dropdown-btn {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  font-size: 15px;
  background: #fff;
  cursor: pointer;
  text-align: left;
  font-weight: 400;
  color: #222;
  box-shadow: none;
  outline: none;
  transition: border 0.15s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}
.user-create-dropdown-btn:focus {
  border: 1.5px solid #2d5be3;
}
.user-create-dropdown-btn .dropdown-icon {
  width: 16px;
  height: 16px;
  opacity: 1;
  pointer-events: none;
  flex-shrink: 0;
}
.user-create-dropdown-menu {
  display: none;
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
  z-index: 100;
  padding: 8px 0;
  margin-top: 4px;
  max-height: 145px;
  overflow-y: auto;
  box-sizing: border-box;
}
.user-create-dropdown.open .user-create-dropdown-menu {
  display: block;
}
.user-create-dropdown-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #000;
  cursor: pointer;
  background: #fff;
  transition: background 0.15s;
  white-space: nowrap;
}
.user-create-dropdown-item:hover {
  background: #f5f7fa;
}
/* Validation Error Styles */
.user-create-group input.error,
.user-create-group textarea.error,
.user-create-dropdown-btn.error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.12);
}
.field-error {
  color: #dc3545;
  font-size: 11px;
  margin-top: 1px;
  display: block;
  min-height: 12px;
  line-height: 12px;
}
.user-create-password-wrap {
  display: flex;
  align-items: center;
  position: relative;
}
.user-create-eye {
  position: absolute;
  right: 10px;
  top: 58%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #888;
  cursor: pointer;
  user-select: none;
}
.user-create-password-checks {
  margin-top: 6px;
  font-size: 13px;
  color: #888;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.user-create-password-checks .valid {
  color: #2dbe60;
}
.user-create-password-checks .invalid {
  color: #e74c3c;
}
.user-create-submit {
  margin-top: 8px;
  background: #2d5be3;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  padding: 12px 0;
  cursor: pointer;
  transition: background 0.15s;
}
.user-create-submit:hover {
  background: #1a3fa1;
}
.user-create-submit:disabled {
  background: #ccc;
  cursor: not-allowed;
}
.user-create-error {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 12px;
  color: #c33;
  font-size: 14px;
  margin-top: 8px;
}
.user-create-success {
  background: #efe;
  border: 1px solid #cfc;
  border-radius: 8px;
  padding: 12px;
  color: #363;
  font-size: 14px;
  margin-top: 8px;
}
.user-create-note {
  font-size: 12px;
  color: #e74c3c;
  margin-top: 4px;
  text-align: center;
}
.required {
  color: #e74c3c;
  margin-left: 2px;
}
@media (max-width: 700px) {
  .user-create-dialog {
    width: 99vw;
    max-height: 95vh;
    padding: 0;
    margin: 8px;
  }
  .user-create-header, .user-create-form { padding: 16px 12px 0 12px; }
  .user-create-row { flex-direction: column; gap: 0; }
}
