// Sample data for small tasks
const smallTasksData = [
  {
    id: 'ST-001',
    category: "UX/UI",
    status: "Đang chờ",
    description: "<PERSON><PERSON><PERSON> thiết kế UC phần staff",
    assignee: {
      name: "<PERSON><PERSON>"
    },
    estimatedTime: "3 Ngày",
    actualTime: "3 Ngày",
    dependency: "Frontend Development",
    dependencies: [],
    priority: "normal",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ST-002',
    category: "Backend",
    status: "Đang triển khai",
    description: "API cho quản lý người dùng",
    assignee: {
      name: "Nguyễn <PERSON>ăn <PERSON>"
    },
    estimatedTime: "5 Ngày",
    actualTime: "2 Ngày",
    dependency: "Database Design",
    dependencies: ["UX/UI"],
    priority: "high",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ST-003',
    category: "Testing",
    status: "<PERSON><PERSON><PERSON> thành",
    description: "Kiểm thử module đăng nhập",
    assignee: {
      name: "Trần Thị B"
    },
    estimatedTime: "2 Ngày",
    actualTime: "2 Ngày",
    dependency: "Login Module",
    dependencies: ["Backend"],
    priority: "normal",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ST-004',
    category: "Documentation",
    status: "Đang chờ",
    description: "Viết tài liệu hướng dẫn sử dụng hệ thống",
    assignee: {
      name: "Phạm Văn C"
    },
    estimatedTime: "4 Ngày",
    actualTime: "0 Ngày",
    dependency: "Project Overview",
    dependencies: ["Testing"],
    priority: "medium",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'ST-005',
    category: "DevOps",
    status: "Đang triển khai",
    description: "Thiết lập CI/CD cho dự án",
    assignee: {
      name: "Ngô Thị D"
    },
    estimatedTime: "6 Ngày",
    actualTime: "1 Ngày",
    dependency: "Server Setup",
    dependencies: ["Documentation"],
    priority: "high",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
];

// Deep clone the tasks array for state management
let smallTasks = JSON.parse(JSON.stringify(smallTasksData));

// Storage key for localStorage
const STORAGE_KEY = 'small_tasks_data';

// Initialize storage
const initializeStorage = () => {
  const stored = localStorage.getItem(STORAGE_KEY);
  if (stored) {
    try {
      smallTasks = JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing stored small tasks:', error);
      smallTasks = JSON.parse(JSON.stringify(smallTasksData));
    }
  } else {
    // Save initial data to localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(smallTasks));
  }
};

// Save to localStorage
const saveToStorage = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(smallTasks));
};

// Get all small tasks
export const getAllSmallTasks = () => {
  initializeStorage();
  return JSON.parse(JSON.stringify(smallTasks));
};

// Get small task by ID
export const getSmallTaskById = (taskId) => {
  initializeStorage();
  return smallTasks.find(task => task.id === taskId);
};

// Create new small task
export const createSmallTask = (taskData) => {
  initializeStorage();
  const newTask = {
    id: `ST-${Date.now()}`,
    ...taskData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  smallTasks.push(newTask);
  saveToStorage();
  return JSON.parse(JSON.stringify(smallTasks));
};

// Update small task
export const updateSmallTask = (taskId, updatedData) => {
  initializeStorage();
  const taskIndex = smallTasks.findIndex(task => task.id === taskId);
  
  if (taskIndex !== -1) {
    smallTasks[taskIndex] = {
      ...smallTasks[taskIndex],
      ...updatedData,
      updatedAt: new Date().toISOString()
    };
    saveToStorage();
  }
  
  return JSON.parse(JSON.stringify(smallTasks));
};

// Delete small task
export const deleteSmallTask = (taskId) => {
  initializeStorage();
  smallTasks = smallTasks.filter(task => task.id !== taskId);
  saveToStorage();
  return JSON.parse(JSON.stringify(smallTasks));
};

// Update task status
export const updateSmallTaskStatus = (taskId, newStatus) => {
  return updateSmallTask(taskId, { status: newStatus });
};

// Update task assignee
export const updateSmallTaskAssignee = (taskId, newAssignee) => {
  return updateSmallTask(taskId, { assignee: newAssignee });
};

// Update task time
export const updateSmallTaskTime = (taskId, estimatedTime, actualTime) => {
  return updateSmallTask(taskId, { estimatedTime, actualTime });
};

// Get tasks by status
export const getSmallTasksByStatus = (status) => {
  initializeStorage();
  return smallTasks.filter(task => task.status === status);
};

// Get tasks by category
export const getSmallTasksByCategory = (category) => {
  initializeStorage();
  return smallTasks.filter(task => task.category === category);
};

// Get tasks by assignee
export const getSmallTasksByAssignee = (assigneeName) => {
  initializeStorage();
  return smallTasks.filter(task => task.assignee.name === assigneeName);
};

// Reset to original data
export const resetSmallTasks = () => {
  // Clear localStorage first
  localStorage.removeItem(STORAGE_KEY);
  smallTasks = JSON.parse(JSON.stringify(smallTasksData));
  saveToStorage();
  return smallTasks;
};

// Force reset and clear localStorage
export const forceResetSmallTasks = () => {
  localStorage.removeItem(STORAGE_KEY);
  smallTasks = JSON.parse(JSON.stringify(smallTasksData));
  saveToStorage();
  return smallTasks;
};

// Clear all tasks
export const clearAllSmallTasks = () => {
  smallTasks = [];
  saveToStorage();
  return smallTasks;
};

export default smallTasks; 