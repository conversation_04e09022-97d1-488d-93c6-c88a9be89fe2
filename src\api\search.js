import { SEARCH_ENDPOINTS } from "./endpoints";

const getAuthToken = () => {
  try {
    const token =
      localStorage.getItem("token") || localStorage.getItem("authToken");
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    return token || user.token;
  } catch {
    return null;
  }
};

const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: "Có lỗi xảy ra" };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || "Có lỗi xảy ra");
  }
  return response.json();
};

export async function searchAll(query) {
  if (!query || typeof query !== "string" || query.trim().length < 2) {
    throw new Error("Vui lòng nhập ít nhất 2 ký tự để tìm kiếm.");
  }
  const url = SEARCH_ENDPOINTS.BASIC_SEARCH(query.trim());
  const response = await fetch(url, {
    method: "GET",
    headers: getAuthHeaders(),
  });
  return handleResponse(response);
}

export async function searchAdvanced(body) {
  const url = SEARCH_ENDPOINTS.ADVANCED_SEARCH;
  const response = await fetch(url, {
    method: "POST",
    headers: getAuthHeaders(),
    body: JSON.stringify(body),
  });
  return handleResponse(response);
}

export async function getSearchHistory() {
  const url = SEARCH_ENDPOINTS.SEARCH_HISTORY;
  const response = await fetch(url, {
    method: "GET",
    headers: getAuthHeaders(),
  });
  return handleResponse(response);
}
