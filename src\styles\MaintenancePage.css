.maintenance-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
  padding: 20px;
}

.maintenance-container {
  background: white;
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.maintenance-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9800, #f44336, #2196f3, #4caf50);
  animation: pulse 2s ease-in-out infinite;
}

.maintenance-icon {
  margin-bottom: 30px;
  animation: float 3s ease-in-out infinite;
}

.maintenance-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.maintenance-message {
  font-size: 1.2rem;
  color: #5a6c7d;
  margin-bottom: 30px;
  line-height: 1.6;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.maintenance-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border-left: 4px solid #ff9800;
}

.maintenance-info p {
  margin: 0;
  color: #495057;
  font-size: 1rem;
}

.maintenance-info strong {
  color: #2c3e50;
  font-weight: 600;
}

.maintenance-actions {
  margin: 40px 0;
}

.refresh-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.refresh-button:active {
  transform: translateY(0);
}

.refresh-button svg {
  transition: transform 0.3s ease;
}

.refresh-button:hover svg {
  transform: rotate(180deg);
}

.maintenance-footer {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e9ecef;
}

.maintenance-footer p {
  margin: 8px 0;
  color: #6c757d;
  font-size: 0.95rem;
}

.maintenance-footer p:first-child {
  font-weight: 600;
  color: #495057;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .maintenance-container {
    padding: 40px 20px;
    margin: 10px;
  }
  
  .maintenance-title {
    font-size: 2rem;
  }
  
  .maintenance-message {
    font-size: 1.1rem;
  }
  
  .maintenance-info {
    padding: 15px;
  }
  
  .refresh-button {
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .maintenance-page {
    padding: 10px;
  }
  
  .maintenance-container {
    padding: 30px 15px;
  }
  
  .maintenance-title {
    font-size: 1.8rem;
  }
  
  .maintenance-message {
    font-size: 1rem;
  }
  
  .maintenance-icon svg {
    width: 80px;
    height: 80px;
  }
} 