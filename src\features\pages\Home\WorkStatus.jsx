// Thêm cache và preload ở đầu file
let workStatusCache = { today: null, week: null };
let workStatusCacheTimestamp = { today: 0, week: 0 };
const WORKSTATUS_CACHE_DURATION = 10000; // 10s
let workStatusPreloadPromises = { today: null, week: null };

import React, { useState, useEffect } from "react";
import waitingIcon from "../../../assets/waiting.svg";
import completeIcon from "../../../assets/complete.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import considerIcon from "../../../assets/consider.svg";
import overdueIcon from "../../../assets/triangle-alert.svg";
import { getMyProjectTasks } from "../../../api/taskManagement";
import { getAllProjects } from "../../../api/projectManagement";
import { getCurrentUserRole } from "../../../api/endpoints";

const statusList = [
  { label: "Đang chờ", icon: waitingIcon, color: "#e6f4ff", key: "pending" },
  {
    label: "Đang tiến hành",
    icon: deploymentIcon,
    color: "#f3f0ff",
    key: "in_progress",
  },
  {
    label: "Hoàn thành",
    icon: completeIcon,
    color: "#e9fbe5",
    key: "completed",
  },
  {
    label: "Quá hạn",
    icon: overdueIcon,
    color: "#fff2f0",
    key: "overdue",
  },
  { label: "Xem xét", icon: considerIcon, color: "#f5f5f5", key: "review" },
];

const defaultData = {
  today: [8, 8, 8, 3, 8],
  week: [8, 8, 8, 3, 8],
};

const preloadWorkStatus = async (tab) => {
  if (workStatusPreloadPromises[tab]) return workStatusPreloadPromises[tab];

  workStatusPreloadPromises[tab] = (async () => {
    try {
      // --- Date range calculation ---
      const now = new Date();
      let startDate, endDate;

      if (tab === "today") {
        startDate = new Date(now);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(now);
        endDate.setHours(23, 59, 59, 999);
      } else { // 'week'
        const day = now.getDay();
        const diff = now.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is Sunday
        startDate = new Date(new Date(now).setDate(diff));
        startDate.setHours(0, 0, 0, 0);
        
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59, 999);
      }

      const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
      const userId = userRaw._id || userRaw.id || (userRaw.user && (userRaw.user._id || userRaw.user.id));
      const userRole = getCurrentUserRole();

      let tasks = [];
      if (userRole === 'admin' || userRole === 'ceo') {
        const projectRes = await getAllProjects();
        const projects = projectRes.data || projectRes || [];
        const taskPromises = projects.map(p => getMyProjectTasks({ projectId: p._id || p.id }).catch(() => ({ data: [] })));
        const taskResults = await Promise.all(taskPromises);
        tasks = taskResults.flatMap(res => res.data || res);
      } else {
        if (!userId) return [0,0,0,0,0];

        // For other roles, just get their tasks
        const res = await getMyProjectTasks();
        tasks = res.data || res || [];
      }

      // Filter tasks by date range (today or this week)
      const timeFilteredTasks = tasks.filter(task => {
        // Prefer deadline, fallback to update/creation date
        const taskDate = new Date(task.deadline || task.updatedAt || task.createdAt);
        return taskDate >= startDate && taskDate <= endDate;
      });
      
      const statusKeys = ["pending", "in_progress", "completed", "overdue", "review"];
      const counts = statusKeys.map(key => timeFilteredTasks.filter(t => t.status === key).length);
      
      workStatusCache[tab] = counts;
      workStatusCacheTimestamp[tab] = Date.now();
      return counts;
    } catch {
      return [0, 0, 0, 0, 0];
    } finally {
      workStatusPreloadPromises[tab] = null;
    }
  })();
  return workStatusPreloadPromises[tab];
};

const WorkStatus = () => {
  const [tab, setTab] = useState("today");
  const [data, setData] = useState(workStatusCache[tab] || [0, 0, 0, 0, 0]);
  const [loading, setLoading] = useState(!workStatusCache[tab]);
  const [error, setError] = useState(null);

  useEffect(() => {
    let ignore = false;
    const fetchStatus = async () => {
      const now = Date.now();
      if (workStatusCache[tab] && (now - workStatusCacheTimestamp[tab]) < WORKSTATUS_CACHE_DURATION) {
        setData(workStatusCache[tab]);
        setLoading(false);
        // Preload in background
        preloadWorkStatus(tab).then(newData => {
          if (!ignore && newData) {
            setData(newData);
          }
        });
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const newData = await preloadWorkStatus(tab);
        if (!ignore && newData) {
          setData(newData);
          setLoading(false);
        }
      } catch (err) {
        if (!ignore) {
          setError(err.message || "Lỗi tải trạng thái công việc");
          setLoading(false);
        }
      }
    };
    fetchStatus();
    return () => { ignore = true; };
  }, [tab]);

  return (
    <div
      style={{
        background: "#fff",
        borderRadius: 12,
        padding: 20,
        maxWidth: 490,
        minWidth: 320, // Giữ form không bị co lại
        boxShadow: "0 2px 8px #eee",
        width: '100%',
      }}
    >
      <h3 style={{ marginBottom: 16 }}>Trạng thái công việc</h3>
      <div
        style={{
          display: "flex",
          marginBottom: 16,
          background: "#f5f5f5",
          borderRadius: 8,
        }}
      >
        <button
          onClick={() => setTab("today")}
          style={{
            flex: 1,
            padding: 8,
            border: "none",
            background: tab === "today" ? "#fff" : "#f5f5f5",
            borderRadius: 8,
            fontWeight: tab === "today" ? 600 : 400,
          }}
        >
          Hôm nay
        </button>
        <button
          onClick={() => setTab("week")}
          style={{
            flex: 1,
            padding: 8,
            border: "none",
            background: tab === "week" ? "#fff" : "#f5f5f5",
            borderRadius: 8,
            fontWeight: tab === "week" ? 600 : 400,
          }}
        >
          Tuần này
        </button>
      </div>
      <div>
        {error ? (
          <div style={{ color: 'red' }}>{error}</div>
        ) : loading ? (
          statusList.map((status, idx) => (
            <div
              key={status.label}
              style={{
                display: "flex",
                alignItems: "center",
                background: status.color,
                borderRadius: 8,
                padding: "8px 12px",
                marginBottom: 8,
                opacity: 0.7,
                width: '100%', // Giữ nguyên chiều rộng
                minWidth: 0,
              }}
            >
              <div style={{ width: 20, height: 20, background: '#f0f0f0', borderRadius: 4, marginRight: 10, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
              <div style={{ flex: 1, height: 16, background: '#f0f0f0', borderRadius: 4, animation: 'pulse 1.5s ease-in-out infinite', minWidth: 80 }}></div>
              <div style={{ width: 30, height: 16, background: '#f0f0f0', borderRadius: 4, marginLeft: 10, animation: 'pulse 1.5s ease-in-out infinite' }}></div>
            </div>
          ))
        ) : (
          statusList.map((status, idx) => (
                <div
                  key={status.label}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    background: status.color,
                    borderRadius: 8,
                    padding: "8px 12px",
                    marginBottom: 8,
                  }}
                >
                  <img
                    src={status.icon}
                    alt={status.label}
                    style={{ width: 20, height: 20, marginRight: 10 }}
                  />
                  <span style={{ flex: 1 }}>{status.label}</span>
                  <span style={{ fontWeight: 600 }}>{data[idx]}</span>
                </div>
              ))
        )}
      </div>
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default WorkStatus;
