import React, { useState, useEffect, useRef } from 'react';
import '../../styles/DocumentDetail.css';
import closePanelIcon from '../../assets/closePanel.svg';
import userIcon from '../../assets/user1.png';
import startdateIcon from '../../assets/startdate.svg';
import pencilLineIcon from '../../assets/pencil-line.svg';
import { FaPaperPlane } from 'react-icons/fa';
import { getDocument, addProjectFileComment, updateProjectFileDescription } from '../../api/documents';
import { getProjectById } from '../../api/projectManagement';
import { getProfile } from '../../api/profile';
import { useParams } from 'react-router-dom';
import { showSuccess, showError, showInfo } from '../../utils/toastUtils';

// Utility functions for localStorage
const getStorageKey = (documentId) => `document_${documentId}_data`;

const saveDocumentDataToStorage = (documentId, data) => {
  try {
    localStorage.setItem(getStorageKey(documentId), JSON.stringify(data));
  } catch (error) {
    console.warn('Không thể lưu dữ liệu vào localStorage:', error);
  }
};

const getDocumentDataFromStorage = (documentId) => {
  try {
    const data = localStorage.getItem(getStorageKey(documentId));
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.warn('Không thể đọc dữ liệu từ localStorage:', error);
    return null;
  }
};

// Helper function để lấy avatar với fallback
const getUserAvatar = (user) => {
  const defaultAvatar = 'https://randomuser.me/api/portraits/men/1.jpg';
  if (!user) return defaultAvatar;

  // Kiểm tra avatar trực tiếp
  if (user.avatar && user.avatar.trim() !== '' && !user.avatar.includes('ui-avatars.com/api/?name=?')) {
    return user.avatar;
  }

  // Fallback về avatar mặc định
  return defaultAvatar;
};

const DocumentDetail = ({ document, onClose }) => {
  const { projectId } = useParams();
  const [visible, setVisible] = useState(false);
  const [description, setDescription] = useState('');
  const [originalDescription, setOriginalDescription] = useState('');
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [activities, setActivities] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [projectData, setProjectData] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const contentRef = useRef(null);



  useEffect(() => {
    let ignore = false;
    async function fetchDetail() {
      if (!document || !projectId) return;
      setLoading(true);
      setError('');

      const documentId = document._id || document.id;

      // Load dữ liệu từ localStorage trước
      const storedData = getDocumentDataFromStorage(documentId);

      try {
        // Fetch project data, document detail và user profile song song
        const [projectResponse, docDetail, userProfile] = await Promise.all([
          getProjectById(projectId).catch(() => null),
          getDocument(documentId).catch(() => null),
          getProfile().catch(() => null)
        ]);

        if (!ignore) {
          // Set project data
          if (projectResponse?.data) {
            setProjectData(projectResponse.data);
          }

          // Set current user data
          if (userProfile?.data) {
            setCurrentUser(userProfile.data);
          } else if (userProfile?.user) {
            setCurrentUser(userProfile.user);
          } else if (userProfile?.email) {
            setCurrentUser(userProfile);
          } else {
            // Fallback: lấy user từ localStorage
            try {
              const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
              if (storedUser.email) {
                setCurrentUser(storedUser);
              }
            } catch (error) {
              console.error('Error getting user from localStorage:', error);
            }
          }

          // Set document description - ưu tiên localStorage nếu có
          const desc = storedData?.description || docDetail?.description || document.description || 'Tài liệu này sử dụng với mục đích hướng dẫn người dùng có thể thao tác và sử dụng tốt ứng dụng trong dự án';
          setDescription(desc);
          setOriginalDescription(docDetail?.description || document.description || desc);

          // Set activities - merge localStorage với API data
          const apiActivities = docDetail?.comments || docDetail?.activities || document.activities || [];
          const localActivities = storedData?.activities || [];
          const mergedActivities = [...localActivities, ...apiActivities];
          setActivities(mergedActivities);
        }
      } catch (err) {
        // Nếu API lỗi, sử dụng dữ liệu từ localStorage hoặc props document
        console.warn('Không thể tải chi tiết từ API, sử dụng dữ liệu local:', err);
        if (!ignore) {
          const desc = storedData?.description || document.description || 'Tài liệu này sử dụng với mục đích hướng dẫn người dùng có thể thao tác và sử dụng tốt ứng dụng trong dự án';
          setDescription(desc);
          setOriginalDescription(document.description || desc);
          setActivities(storedData?.activities || document.activities || []);
          setError(''); // Không hiển thị lỗi vì đã có fallback data
        }
      } finally {
        if (!ignore) setLoading(false);
      }
    }
    if (document) {
      setTimeout(() => setVisible(true), 10);
      fetchDetail();
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    } else {
      setVisible(false);
    }
    return () => { ignore = true; };
  }, [document, projectId]);

  if (!document) return null;

  const handleOverlayClick = (e) => {
    if (e.target.classList.contains('document-detail-overlay')) {
      handleClose();
    }
  };

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => onClose && onClose(), 250);
  };

  const handleEditDescription = () => {
    setIsEditingDescription(true);
  };

  const handleSaveDescription = async () => {
    const documentId = document._id || document.id;
    try {
      if (projectId && documentId) {
        await updateProjectFileDescription(projectId, documentId, description);
        setOriginalDescription(description);
        showSuccess('Cập nhật mô tả thành công!');
      }

      // Save to localStorage
      const storedData = getDocumentDataFromStorage(documentId) || {};
      saveDocumentDataToStorage(documentId, {
        ...storedData,
        description: description
      });

      setIsEditingDescription(false);
    } catch (error) {
      console.error('Lỗi khi cập nhật mô tả:', error);
      // Vẫn save vào localStorage ngay cả khi API lỗi
      const storedData = getDocumentDataFromStorage(documentId) || {};
      saveDocumentDataToStorage(documentId, {
        ...storedData,
        description: description
      });
      setIsEditingDescription(false);
      showInfo('Đã lưu mô tả locally. Sẽ đồng bộ với server khi có kết nối.');
    }
  };

  const handleCancelEditDescription = () => {
    setDescription(originalDescription);
    setIsEditingDescription(false);
  };

  const handleAddComment = async () => {
    const documentId = document._id || document.id;
    if (newComment.trim() && projectId && documentId) {
      const comment = {
        id: Date.now(),
        user: currentUser?.fullName || 'Bạn',
        avatar: getUserAvatar(currentUser),
        content: newComment.trim(),
        timestamp: new Date().toLocaleString('vi-VN'),
        isLocal: true // Đánh dấu comment này là local
      };

      // Cập nhật UI ngay lập tức
      const newActivities = [comment, ...activities];
      setActivities(newActivities);
      setNewComment('');

      // Save to localStorage
      const storedData = getDocumentDataFromStorage(documentId) || {};
      saveDocumentDataToStorage(documentId, {
        ...storedData,
        activities: newActivities.filter(act => act.isLocal) // Chỉ lưu local comments
      });

      // Thử gửi lên server
      try {
        const response = await addProjectFileComment(projectId, documentId, comment.content);

        // Nếu thành công, cập nhật comment với dữ liệu từ server
        if (response?.data) {
          const updatedComment = {
            ...comment,
            id: response.data._id,
            user: response.data.author?.fullName || currentUser?.fullName || 'Bạn',
            avatar: getUserAvatar(response.data.author) || getUserAvatar(currentUser),
            timestamp: new Date(response.data.createdAt).toLocaleString('vi-VN'),
            isLocal: false
          };

          setActivities(prev => prev.map(act =>
            act.id === comment.id ? updatedComment : act
          ));
        }
      } catch (error) {
        console.error('Lỗi khi gửi bình luận lên server:', error);
        // Comment đã được thêm vào UI và localStorage, không cần làm gì thêm
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddComment();
    }
  };





  return (
    <div className="document-detail-overlay" onClick={handleOverlayClick} style={{position: 'fixed', inset: 0, background: 'rgba(0,0,0,0.08)', zIndex: 999}}>
      <div className={`document-detail-container${visible ? ' show' : ''}`}>
        <button className="document-detail-close-btn" onClick={handleClose} title="Đóng">
          <img src={closePanelIcon} alt="Đóng" style={{width: 20, height: 20}} />
        </button>

        {/* Header cố định */}
        <div className="document-detail-header">
          <h3>{document.name}</h3>
        </div>

        <div className="document-detail-content" ref={contentRef}>
          {loading && <div className="document-detail-loading">Đang tải chi tiết...</div>}
          {error && <div className="document-detail-error">{error}</div>}
          {!loading && !error && (
            <>
              <div className="document-detail-row">
                <span className="document-detail-label">Người tạo</span>
                <span className="document-detail-value">
                  <img
                    src={getUserAvatar(document.uploadedBy || document.createdBy)}
                    alt="User"
                    className="document-user-avatar"
                  />
                  {document.uploadedBy?.fullName || document.createdBy?.fullName || document.creator || 'Không xác định'}
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">Dự án</span>
                <span className="document-detail-value">
                  {projectData?.name || document.project || 'SDTC'}
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">Ngày tạo</span>
                <span className="document-detail-value">
                  <img src={startdateIcon} alt="Ngày tạo" style={{ width: 16, marginRight: 8, verticalAlign: 'middle' }} />
                  {document.uploadedAt
                    ? new Date(document.uploadedAt).toLocaleDateString('vi-VN')
                    : document.createdAt
                      ? (document.createdAt.includes && document.createdAt.includes('/')
                          ? document.createdAt
                          : new Date(document.createdAt).toLocaleDateString('vi-VN'))
                      : 'N/A'
                  }
                </span>
              </div>

              <div className="document-detail-row">
                <span className="document-detail-label">
                  Mô tả
                  <button onClick={handleEditDescription} className="document-edit-btn">
                    <img src={pencilLineIcon} alt="Edit" />
                  </button>
                </span>
                <span className="document-detail-value document-description-section">
                  {isEditingDescription ? (
                    <div className="document-description-edit">
                      <textarea
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        className="document-description-textarea"
                        rows="3"
                      />
                      <div className="document-description-actions">
                        <button onClick={handleSaveDescription} className="document-btn-save">
                          Lưu
                        </button>
                        <button onClick={handleCancelEditDescription} className="document-btn-cancel">
                          Hủy
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="document-description-display">
                      <span>{description}</span>
                    </div>
                  )}
                </span>
              </div>



              <div className="document-detail-row document-detail-row-activity">
                <span className="document-detail-label">Bình luận</span>
                <span className="document-detail-value">
                  <div className="document-detail-activity-list">
                    {activities.length > 0 ? activities.map((act, idx) => (
                      <div key={idx} className="document-detail-activity-item">
                        <img src={getUserAvatar({ avatar: act.avatar })} alt={act.user} className="document-detail-activity-avatar" />
                        <div className="document-detail-activity-content">
                          <span className="document-detail-activity-name">{act.user}</span>
                          <span className="document-detail-activity-text">{act.content}</span>
                          {act.timestamp && <span className="document-detail-activity-time">{act.timestamp}</span>}
                        </div>
                      </div>
                    )) : <span style={{ color: '#888', fontStyle: 'italic' }}>Chưa có bình luận</span>}
                  </div>
                </span>
              </div>
            </>
          )}
        </div>

        {/* Comment footer cố định */}
        <div className="document-comment-footer">
          <div className="document-detail-row document-detail-row-comment">
            <div className="document-detail-comment-box-wrapper">
              <div className="document-detailfile-comment-box">
                <input
                  className="document-detail-comment-input"
                  placeholder="Viết bình luận cho bạn"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
                <div className="document-detail-comment-actions">
                  <button
                    className="document-detail-comment-btn"
                    title="Gửi bình luận"
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                  >
                    <FaPaperPlane />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentDetail;
