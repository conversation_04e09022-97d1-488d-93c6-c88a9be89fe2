@import url('../index.css');

.pm-login-container {
  display: flex;
  height: 120vh;
  align-items: stretch;
  justify-content: center;
  background: #f7f8fa;
}

.pm-login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: none;
  border-right: none;
  min-width: 0;
}

.pm-login-right {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: #fff;
  height: 100%;
  min-width: 0;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12), 0 1.5px 6px rgba(0,0,0,0.08);
}

.pm-login-illustration {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  margin: 0 auto;
}

.pm-login-form-wrapper {
  padding: 48px 36px 36px 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pm-login-logo {
  width: 60px;
  height: 60px;
  margin-bottom: 28px;
  display: block;
}

.pm-login-form-wrapper h2 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #22213a;
  margin-bottom: 8px;
  margin-top: 0;
  letter-spacing: 0.01em;
}

.pm-login-desc {
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 28px;
}

.google-login-btn {
  width: 100%;
  padding: 10px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 18px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.google-login-btn:hover {
  box-shadow: 0 2px 8px rgba(80, 0, 40, 0.08);
}

.google-icon {
  width: 20px;
  height: 20px;
}

.pm-divider {
  margin: 20px 70px 20px 70px;
  text-align: center;
  color: #b0a6b0;
  font-size: 0.875rem;
  position: relative;
}

.pm-divider span {
  background: #fff;
  padding: 0 12px;
  position: relative;
  z-index: 2;
}

.pm-divider:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 1px;
  background: #eee;
  z-index: 1;
}

.pm-login-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 16px;
}

.pm-login-form label {
  text-align: left;
  color: #7a6e7a;
  font-size: 1rem;
  margin-bottom: 2px;
  font-weight: 500;
}

.pm-login-form input[type="email"],
.pm-login-form input[type="password"],
.pm-password-input[type="text"] {
  padding: 12px 14px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 8px;
  background: #faf7fa;
  color: #3d2c3c;
  transition: border-color 0.2s;
  width: 420px;
  box-sizing: border-box;
}

.pm-login-form input[type="email"]:focus,
.pm-login-form input[type="password"]:focus {
  outline: none;
  border-color: #a83279;
}

.pm-password-input:focus,
.pm-password-input[type="text"]:focus {
  outline: none;
  border-color: #a83279;
}

.pm-login-form .row {
  display: flex;
  gap: 10px;
}

.pm-login-form .row > div {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pm-login-form .row input[type="text"] {
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 6px;
  background: #faf7fa;
  color: #3d2c3c;
}

.pm-login-form .row input[type="text"]:focus {
  outline: none;
  border-color: #a83279;
}

.pm-remember-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  width: 100%;
  font-size: 0.98rem;
}

.pm-login-form input[type="checkbox"] {
  accent-color: #2563eb;
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.pm-login-btn {
  width: 100%;
  padding: 13px 0;
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 10px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
}

.pm-login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.pm-register-link {
  color: #7a6e7a;
  font-size: 1rem;
}

.pm-register-link a {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  margin-left: 4px;
}

.pm-register-link a:hover {
  text-decoration: underline;
}

.pm-forgot-link {
  color: #2563eb;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: text-decoration 0.2s;
  margin-left: 8px;
}

.pm-forgot-link:hover {
  color: #0056b3;
}

.pm-password-input {
  width: 100%;
  padding-right: 38px;
  box-sizing: border-box;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 6px;
  background: #faf7fa;
  color: #3d2c3c;
  transition: border-color 0.2s;
}

.pm-password-input:focus {
  outline: none;
  border-color: #a83279;
}

.pm-password-toggle {
  position: absolute;
  right: 8px;
  top: 44%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  color: #DED2D9;
  font-size: 22px;
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 11;
}

.input-error {
  border: 2px solid #e53935 !important;
    box-shadow: none;
    background
: #fdf6f6;
    position: relative;
    z-index: 2;
}

/* Container để định vị thông báo lỗi */
.input-validation-wrapper {
  position: relative;
  width: 100%;
}

.validation-error-message {
  color: #e53935;
  font-size: 0.75rem;
  position: absolute;
  left: 0;
  top: 100%;
  background: #fff;
  padding: 0 6px;
  z-index: 10;
  margin-top: -10px;
  margin-left: 10px;
  line-height: 0.2;
  pointer-events: none;
  
}

@media (max-width: 1200px) {
  .pm-login-left {
    max-width: 45vw;
  }
  .pm-illustration {
    max-width: 400px;
  }
}

@media (max-width: 900px) {
  .pm-login-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }
  .pm-login-left, .pm-login-right {
    flex: unset;
    width: 100%;
    min-height: 320px;
    max-width: 100vw;
    height: auto;
  }
  .pm-login-form-wrapper {
    margin: 32px auto;
    max-width: 95vw;
    padding: 24px 8px 24px 8px;
  }
  .pm-login-form input[type="email"],
  .pm-login-form input[type="password"],
  .pm-password-input[type="text"],
  .pm-password-input {
    /* width: 100%; */
    min-width: 0;
    max-width: 100vw;
  }
  .pm-illustration {
    width: 100%;
    max-width: 350px;
    height: auto;
    margin: 0 auto;
  }
}

@media (max-width: 600px) {
  .pm-login-form-wrapper {
    padding: 8px;
    max-width: 100vw;
  }
  .pm-login-right {
    padding: 16px 4px;
    min-width: unset;
    max-width: 100vw;
  }
  .pm-login-form input[type="email"],
  .pm-login-form input[type="password"],
  .pm-password-input[type="text"],
  .pm-password-input {
    width: 288px;
    height: 44px;
    min-width: 0;
    max-width: 100vw;
    font-size: 0.98rem;
    padding: 10px 10px;
  }
  .pm-login-form label {
    font-size: 0.98rem;
  }
  .pm-login-btn {
    font-size: 1rem;
    padding: 11px 0;
  }
  .pm-login-logo {
    width: 44px;
    height: 44px;
    margin-bottom: 18px;
  }
  .pm-login-form-wrapper h2 {
    font-size: 1.15rem;
  }
  .pm-login-desc {
    font-size: 0.95rem;
  }
  .pm-register-link, .pm-forgot-link {
    font-size: 0.95rem;
  }
}

@media (max-width: 400px) {
  .pm-login-form-wrapper {
    padding: 2px;
  }
  .pm-login-form input[type="email"],
  .pm-login-form input[type="password"],
  .pm-password-input[type="text"],
  .pm-password-input {
    font-size: 0.92rem;
    padding: 8px 6px;
  }
  .pm-login-btn {
    font-size: 0.95rem;
    padding: 9px 0;
  }
}
