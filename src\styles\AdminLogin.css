@import url('../index.css');
.admin-login-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, #232526 0%, #a83279 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.admin-login-bg::before {
  content: '';
  position: absolute;
  top: -80px;
  left: -80px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, #fff0 60%, #a83279 100%);
  opacity: 0.25;
  z-index: 1;
  filter: blur(10px);
}

.admin-login-bg::after {
  content: '';
  position: absolute;
  bottom: -100px;
  right: -100px;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, #fff0 60%, #614385 100%);
  opacity: 0.18;
  z-index: 1;
  filter: blur(12px);
}

.admin-login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  position: relative;
  z-index: 2;
}

.admin-login-card {
  background: rgba(255,255,255,0.95);
  border-radius: 22px;
  box-shadow: 0 8px 32px 0 rgba(168, 50, 121, 0.18), 0 1.5px 8px 0 rgba(97, 67, 133, 0.10);
  padding: 54px 38px 38px 38px;
  max-width: 410px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 2;
  border: 2.5px solid #a83279;
  animation: popIn 0.7s cubic-bezier(.68,-0.55,.27,1.55);
}

@keyframes popIn {
  0% { transform: scale(0.85) translateY(40px); opacity: 0; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

.admin-login-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #a83279;
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px #f7e1f0;
}

.admin-login-desc {
  color: #614385;
  font-size: 1.08rem;
  margin-bottom: 32px;
  font-weight: 500;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.admin-login-form label {
  text-align: left;
  color: #a83279;
  font-weight: 600;
  margin-bottom: 2px;
  letter-spacing: 0.5px;
}

.admin-login-form input[type="email"],
.admin-login-form input[type="password"],
.admin-password-wrapper input[type="text"] {
  padding: 13px 15px;
  border: 1.5px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1.08rem;
  background: #f7f7fa;
  color: #232526;
  transition: border-color 0.2s, box-shadow 0.2s;
  width: 100%;
  box-sizing: border-box;
  font-weight: 500;
  box-shadow: 0 1.5px 8px 0 rgba(168, 50, 121, 0.04);
}

.admin-login-form input:focus {
  outline: none;
  border-color: #a83279;
  box-shadow: 0 0 0 2px #f7e1f0;
}

.admin-password-wrapper {
  position: relative;
  width: 100%;
}

.admin-password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #a83279 0%, #614385 100%);
  border: none;
  color: #fff;
  font-weight: 700;
  cursor: pointer;
  font-size: 0.98rem;
  padding: 3px 13px;
  border-radius: 6px;
  box-shadow: 0 1.5px 8px 0 rgba(168, 50, 121, 0.10);
  transition: background 0.2s, color 0.2s;
}

.admin-password-toggle:hover {
  background: linear-gradient(135deg, #614385 0%, #a83279 100%);
  color: #fff;
}

.admin-login-btn {
  width: 100%;
  padding: 15px 0;
  background: linear-gradient(135deg, #a83279 0%, #614385 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  font-size: 1.15rem;
  font-weight: 800;
  cursor: pointer;
  margin-top: 12px;
  box-shadow: 0 2px 12px 0 rgba(168, 50, 121, 0.13);
  letter-spacing: 1px;
  transition: background 0.2s, transform 0.1s;
}

.admin-login-btn:hover {
  background: linear-gradient(135deg, #614385 0%, #a83279 100%);
  transform: translateY(-2px) scale(1.03);
}
