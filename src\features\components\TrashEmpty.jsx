import React, { useRef, useEffect } from "react";
import "../../styles/EmptyTrash.css";

const EmptyTrash = ({ onCancel, onConfirm }) => {
  const ref = useRef();
  useEffect(() => {
    const handleClick = (e) => {
      if (ref.current && !ref.current.contains(e.target)) {
        onCancel && onCancel();
      }
    };
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [onCancel]);
  return (
    <div className="empty-trash-modal" ref={ref}>
      <div className="empty-trash-title">Làm trống thùng rác</div>
      <div className="empty-trash-desc">
        Bạn có chắc chắn muốn xóa vĩnh viễn tất cả các mục trong thùng rác? Hành động này không thể hoàn tác.
      </div>
      <div className="empty-trash-actions">
        <button className="empty-trash-cancel" onClick={onCancel}>Hủy</button>
        <button className="empty-trash-confirm" onClick={onConfirm}>Làm trống thùng rác</button>
      </div>
    </div>
  );
};

export default EmptyTrash;
