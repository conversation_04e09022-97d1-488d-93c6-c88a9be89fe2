@import url('../index.css');
.trash-list-container {
  background: #fff;
  border-radius: 8px;
  padding: 18px 0 0 0;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  height: 600px; /* <PERSON><PERSON><PERSON> cao cố định */
  display: flex;
  flex-direction: column;
}

.trash-list-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar cho trash list content */
.trash-list-content::-webkit-scrollbar {
  width: 6px;
}

.trash-list-content::-webkit-scrollbar-track {
  background: transparent;
}

.trash-list-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.trash-list-content:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

.trash-list-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
.trash-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px 12px 32px;
  border-bottom: 1px solid #f2f3f5;
  font-size: 15px;
  color: #888;
}
.trash-list-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}
.trash-list-header-right {
  font-size: 13px;
  color: #aaa;
}
.trash-list-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 32px 12px 32px;
  border-bottom: 1px solid #f2f3f5;
  gap: 16px;
}
.trash-list-item:last-child {
  border-bottom: none;
}
.trash-list-checkbox {
  margin-right: 16px;
  margin-top: 4px;
}
.trash-list-main {
  flex: 1;
}
.trash-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #444;
  display: flex;
  align-items: center;
  gap: 8px;
}

.trash-list-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.trash-list-icon img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Màu cho icon Dự án (file-text) - màu xanh dương */
.trash-list-icon img[alt="Dự án"] {
  filter: brightness(0) saturate(100%) invert(25%) sepia(100%) saturate(4218%) hue-rotate(213deg) brightness(97%) contrast(101%);
}

/* Màu cho icon Công việc (check) - màu xanh lá */
.trash-list-icon img[alt="Công việc"] {
  filter: brightness(0) saturate(100%) invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}
.trash-list-type {
  background: #f2f3f5;
  color: #888;
  font-size: 13px;
  border-radius: 8px;
  padding: 2px 12px;
  margin-left: 8px;
}
.trash-list-status {
  background: #f2f3f5;
  color: #888;
  font-size: 13px;
  border-radius: 8px;
  padding: 2px 12px;
  margin-left: 8px;
}
.trash-list-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #555;
}
.trash-list-members {
  display: flex;
  gap: 8px;
  margin-top: 6px;
}
.trash-list-member {
  background: #f2f3f5;
  color: #888;
  font-size: 13px;
  border-radius: 8px;
  padding: 2px 10px;
}
.trash-list-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-direction: row;
  position: relative;
}
.trash-list-more {
  background: none;
  border: none;
  color: #aaa;
  font-size: 22px;
  cursor: pointer;
}
