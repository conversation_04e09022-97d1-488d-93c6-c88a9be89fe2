
// <PERSON><PERSON><PERSON> fetch API cho tạo, l<PERSON><PERSON> danh s<PERSON>ch, chỉnh sửa ghi chú cá nhân
import { PERSONAL_NOTES_ENDPOINTS } from './endpoints';

export const createPersonalNote = async (noteData, token) => {
  const res = await fetch(PERSONAL_NOTES_ENDPOINTS.CREATE_NOTE, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      title: noteData.title,
      description: noteData.content,
      // Thêm các trường khác nếu cần
    }),
  });
  return await res.json();
};

export const getPersonalNotes = async (token) => {
  const res = await fetch(PERSONAL_NOTES_ENDPOINTS.GET_NOTES, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  return await res.json();
};

export const updatePersonalNote = async (id, noteData, token) => {
  const res = await fetch(PERSONAL_NOTES_ENDPOINTS.UPDATE_NOTE(id), {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      title: noteData.title,
      description: noteData.content,
      // Thêm các trường khác nếu cần
    }),
  });
  return await res.json();
};
