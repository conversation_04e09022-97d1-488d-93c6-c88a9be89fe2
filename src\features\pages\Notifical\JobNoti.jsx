import { useState, useEffect } from 'react';
import { NOTIFICATION_ENDPOINTS } from '../../../api/endpoints';

const getToken = () => {
  return localStorage.getItem('token') || '';
};

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '';
  const date = new Date(dateTimeString);
  if (isNaN(date.getTime())) {
    return dateTimeString;
  }

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

const JobNoti = ({ globalFilter = 'all' }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch notifications from API
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const token = getToken();
      const res = await fetch(NOTIFICATION_ENDPOINTS.ALL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const dataRaw = await res.json();
      let data = dataRaw?.data || dataRaw || [];
      // Filter only job/task notifications
      data = data.filter(noti => [
        'task_assigned',
        'task_completed',
        'task_overdue'
      ].includes(noti.type));
      // Apply globalFilter
      if (globalFilter === 'unread') {
        data = data.filter(noti => !noti.isRead);
      } else if (globalFilter === 'read') {
        data = data.filter(noti => noti.isRead);
      }
      setNotifications(data);
    } catch (err) {
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
    // eslint-disable-next-line
  }, [globalFilter]);

  const handleMarkAsRead = async (notificationId) => {
    try {
      const token = getToken();
      await fetch(NOTIFICATION_ENDPOINTS.MARK_READ(notificationId), {
        method: 'PUT',
        headers: { Authorization: `Bearer ${token}` },
      });
      fetchNotifications();
    } catch (err) {
      // handle error if needed
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return '#ff4757';
      case 'medium': return '#ffa502';
      case 'low': return '#2ed573';
      default: return '#007bff';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'task_assigned': return '📋';
      case 'task_overdue': return '⏰';
      case 'task_completed': return '✅';
      default: return '📝';
    }
  };

  return (
    <div className="job-notifications">
      {/* Notifications list */}
      <div className="notifications-list">
        {loading ? (
          <div className="no-notifications"><p>Đang tải thông báo...</p></div>
        ) : notifications.length === 0 ? (
          <div className="no-notifications">
            <p>Không có thông báo công việc nào</p>
          </div>
        ) : (
          notifications.map(notification => (
            <div
              key={notification.id}
              className={`notification-item-simple ${notification.isRead ? 'read' : 'unread'}`}
              onClick={() => handleMarkAsRead(notification.id)}
            >
              <div className="notification-left-border" style={{ background: getPriorityColor(notification.priority) }}></div>
              <div className="notification-simple-content">
                <div className="notification-sender">
                  {notification.assignedBy?.fullName || notification.assignedBy?.name || notification.assignedBy?.email || notification.sender?.fullName || notification.sender?.name || notification.sender?.email || (typeof notification.sender === 'string' ? notification.sender : null) || 'Hệ thống'}
                </div>
                <div className="notification-text">
                  {getTypeIcon(notification.type)} {notification.message || notification.content} • <span className="notification-simple-time">{formatDateTime(notification.time || notification.createdAt)}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default JobNoti;