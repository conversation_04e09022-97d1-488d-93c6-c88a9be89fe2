@import url('../index.css');

/* Project Members Avatar */
.project-members-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  display: inline-block;
  margin-right: 6px;
  position: relative;
}

.project-members-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.project-remove-member-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: none;
  background: #ff4757;
  color: white;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: pointer;
}

/* Project Detail Popup Main */
.project-detail-popup {
  position: fixed;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1001;
}

.project-detail-popup-option {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  color: #5b5b5b;
  font-size: 15px;
  cursor: pointer;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.project-detail-popup-option:last-child {
  margin-bottom: 0;
}

.project-detail-popup-option:hover {
  background-color: #f5f5f5;
}

.project-detail-popup-option.delete-option {
  color: #ff4444;
}

.project-detail-popup-option.delete-option:hover {
  background-color: #f5f5f5;
}

.project-detail-popup-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.project-detail-popup-icon.delete-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

/* Modal Overlay */
.project-detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Modal Container */
.project-detail-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 400px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

/* Edit Project Form */
.edit-project-form-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.edit-project-form-description {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #666;
}

.edit-project-form-field {
  margin-bottom: 20px;
}

.edit-project-form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.edit-project-form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.edit-project-form-input:focus {
  border-color: #007bff;
}

/* Delete Project Form */
.delete-project-form-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.delete-project-form-description {
  margin: 0 0 24px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* Modal Actions */
.project-detail-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.project-detail-modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 105px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-detail-modal-btn.cancel-btn {
  border: 1px solid #ddd;
  background: white;
  color: #666;
}

.project-detail-modal-btn.cancel-btn:hover {
  background-color: #f5f5f5;
}

.project-detail-modal-btn.primary-btn {
  border: none;
  background: #007bff;
  color: white;
}

.project-detail-modal-btn.primary-btn:hover {
  background-color: #0056b3;
}

.project-detail-modal-btn.danger-btn {
  border: none;
  background: #dc3545;
  color: white;
}

.project-detail-modal-btn.danger-btn:hover {
  background-color: #c82333;
}
