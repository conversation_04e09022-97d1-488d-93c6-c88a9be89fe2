const API_URL = import.meta.env.VITE_API_URL || 'https://project-management-ji01.onrender.com';

// Helper function để lấy token từ localStorage (sử dụng logic giống ProtectedRoute)
const getAuthToken = () => {
  try {
    // <PERSON><PERSON><PERSON> tra token từ nhiều nguồn khác nhau
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;

    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Helper function để lấy thông tin user hiện tại
const getCurrentUser = () => {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return {};
  }
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// ========== UNIVERSAL PROFILE MANAGEMENT ==========

// Lấy thông tin cá nhân (universal cho tất cả roles)
export async function getProfile() {
  try {
    const response = await fetch(`${API_URL}/api/auth/profile`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật thông tin cá nhân (universal cho tất cả roles)
export async function updateProfile(profileData) {
  try {
    const response = await fetch(`${API_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(profileData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Cập nhật avatar (universal cho tất cả roles)
export async function updateAvatar(file) {
  try {
    const token = getAuthToken();
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await fetch(`${API_URL}/api/auth/update-avatar`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        // Don't set Content-Type for FormData, let browser set it
      },
      body: formData,
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Đổi mật khẩu (universal cho tất cả roles)
export async function changePassword(passwordData) {
  try {
    const response = await fetch(`${API_URL}/api/auth/change-password`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(passwordData),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

export async function getProfileById(userId) {
  try {
    const response = await fetch(`${API_URL}/api/auth/profile/${userId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== ROLE-SPECIFIC FUNCTIONS ==========

// Lấy danh sách nhân viên cùng phòng ban (cho tất cả roles)
export async function getColleagues() {
  try {
    const currentUser = getCurrentUser();
    const endpoint = getProfileEndpoint(currentUser.role);
    
    const response = await fetch(`${API_URL}${endpoint}/colleagues`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// ========== ROLE DETECTION UTILITIES ==========

// Kiểm tra quyền của user hiện tại
export const getUserRole = () => {
  const currentUser = getCurrentUser();
  return currentUser.role?.toLowerCase() || 'staff';
};

// Kiểm tra xem user có phải là CEO không
export const isCEO = () => {
  return getUserRole() === 'ceo';
};

// Kiểm tra xem user có phải là HR không
export const isHR = () => {
  return getUserRole() === 'hr';
};

// Kiểm tra xem user có phải là Leader không
export const isLeader = () => {
  return getUserRole() === 'leader';
};

// Kiểm tra xem user có phải là Department Head không
export const isDepartmentHead = () => {
  return getUserRole() === 'departmenthead';
};

// Kiểm tra xem user có phải là Staff không
export const isStaff = () => {
  return getUserRole() === 'staff';
};

// Lấy tên hiển thị của role
export const getRoleDisplayName = (role) => {
  const roleMap = {
    'staff': 'Nhân viên',
    'leader': 'Trưởng nhóm',
    'departmenthead': 'Trưởng phòng',
    'hr': 'Nhân sự',
    'ceo': 'Giám đốc',
    'admin': 'Quản trị viên'
  };
  return roleMap[role?.toLowerCase()] || role || 'Chưa xác định';
};

// Lấy danh sách quyền của role hiện tại
export const getCurrentUserPermissions = () => {
  const role = getUserRole();
  const permissions = {
    canViewProfile: true,
    canEditProfile: true,
    canChangePassword: true,
    canViewColleagues: true,
    canCreateProjects: false,
    canManageUsers: false,
    canViewReports: false,
    canViewActivityLogs: false
  };

  switch (role) {
    case 'ceo':
      return {
        ...permissions,
        canCreateProjects: true,
        canManageUsers: true,
        canViewReports: true,
        canViewActivityLogs: true
      };
    case 'hr':
      return {
        ...permissions,
        canManageUsers: true,
        canViewReports: true
      };
    case 'departmenthead':
      return {
        ...permissions,
        canCreateProjects: true,
        canViewReports: true,
        canManageUsers: true // Limited to department
      };
    case 'leader':
      return {
        ...permissions,
        canCreateProjects: true,
        canViewReports: true
      };
    case 'staff':
    default:
      return permissions;
  }
};
