import React from 'react';
import '../styles/ConfirmDialog.css';

const ConfirmDialog = ({ 
  show, 
  title, 
  message, 
  confirmText = "Xác nhận", 
  cancelText = "Hủy", 
  onConfirm, 
  onCancel,
  type = "warning" // success, warning, danger, info
}) => {
  if (!show) return null;

  const handleConfirm = () => {
    onConfirm();
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <div className="confirm-dialog-overlay">
      <div className={`confirm-dialog ${type}`}>
        <div className="confirm-dialog-header">
          <div className={`confirm-dialog-icon ${type}`}>
            {type === 'warning' && '⚠️'}
            {type === 'danger' && '🚫'}
            {type === 'success' && '✅'}
            {type === 'info' && 'ℹ️'}
          </div>
          <h3 className="confirm-dialog-title">{title}</h3>
        </div>
        
        <div className="confirm-dialog-body">
          <p className="confirm-dialog-message">{message}</p>
        </div>
        
        <div className="confirm-dialog-footer">
          <button 
            className="btn btn-cancel" 
            onClick={handleCancel}
          >
            {cancelText}
          </button>
          <button 
            className={`btn btn-confirm ${type}`} 
            onClick={handleConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;