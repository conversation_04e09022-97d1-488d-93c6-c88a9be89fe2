// <PERSON><PERSON> liệu thông báo dự án
export const projectNotificationData = [
  {
    id: 1,
    title: "Dự án mới được tạo",
    message: "<PERSON><PERSON> hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "project-created",
    priority: "high",
    projectName: "Website E-commerce",
    projectCode: "WEB-EC-2025",
    createdBy: {
      name: "<EMAIL>",
      role: "Project Manager"
    },
    teamSize: 8,
    deadline: "2025-03-15",
    budget: "500,000,000 VND"
  },
  {
    id: 2,
    title: "Thay đổi deadline dự án",
    message: "Đã hoàn thành nhiệm vụ social media food",
    time: "1 giờ trước",
    isRead: false,
    type: "deadline-changed",
    priority: "medium",
    projectName: "Mobile Banking App",
    projectCode: "MOB-BANK-2024",
    oldDeadline: "2025-01-15",
    newDeadline: "2025-01-29",
    changedBy: {
      name: "Trần Thị B",
      role: "Project Manager"
    },
    reason: "<PERSON><PERSON><PERSON> cầu thêm tính năng từ client"
  },
  {
    id: 3,
    title: "<PERSON>ự án hoàn thành",
    message: "Dự án 'CRM System' đã hoàn thành và được bàn giao cho client",
    time: "2 giờ trước",
    isRead: true,
    type: "project-completed",
    priority: "high",
    projectName: "CRM System",
    projectCode: "CRM-SYS-2024",
    completedBy: {
      name: "Lê Văn C",
      role: "Tech Lead"
    },
    completionRate: "100%",
    deliveredDate: "2025-01-05",
    clientFeedback: "Excellent work, meets all requirements"
  },
  {
    id: 4,
    title: "Thành viên mới tham gia",
    message: "Phạm Thị D đã được thêm vào dự án 'AI Chatbot'",
    time: "4 giờ trước",
    isRead: true,
    type: "member-added",
    priority: "low",
    projectName: "AI Chatbot",
    projectCode: "AI-BOT-2025",
    newMember: {
      name: "Phạm Thị D",
      role: "AI Engineer",
      avatar: "/src/assets/user1.png"
    },
    addedBy: {
      name: "Hoàng Văn E",
      role: "Project Manager"
    },
    currentTeamSize: 6
  },
  {
    id: 5,
    title: "Cập nhật tiến độ dự án",
    message: "Dự án 'ERP System' đã đạt 75% tiến độ",
    time: "6 giờ trước",
    isRead: true,
    type: "progress-update",
    priority: "medium",
    projectName: "ERP System",
    projectCode: "ERP-SYS-2024",
    previousProgress: "65%",
    currentProgress: "75%",
    updatedBy: {
      name: "Vũ Thị F",
      role: "Scrum Master"
    },
    milestonesCompleted: [
      "Database Design",
      "User Authentication",
      "Core Modules Development"
    ],
    nextMilestone: "Integration Testing"
  },
  {
    id: 6,
    title: "Ngân sách dự án cập nhật",
    message: "Ngân sách dự án 'IoT Platform' đã được điều chỉnh",
    time: "1 ngày trước",
    isRead: true,
    type: "budget-updated",
    priority: "medium",
    projectName: "IoT Platform",
    projectCode: "IOT-PLT-2024",
    oldBudget: "800,000,000 VND",
    newBudget: "950,000,000 VND",
    budgetIncrease: "150,000,000 VND",
    updatedBy: {
      name: "Đặng Văn G",
      role: "Finance Manager"
    },
    reason: "Thêm tính năng real-time analytics"
  },
  {
    id: 7,
    title: "Dự án bị tạm dừng",
    message: "Dự án 'Blockchain Wallet' đã bị tạm dừng do thay đổi chiến lược",
    time: "2 ngày trước",
    isRead: true,
    type: "project-paused",
    priority: "high",
    projectName: "Blockchain Wallet",
    projectCode: "BC-WALLET-2024",
    pausedBy: {
      name: "Bùi Thị H",
      role: "CEO"
    },
    pauseReason: "Thay đổi ưu tiên công ty",
    expectedResumeDate: "2025-04-01",
    currentProgress: "45%"
  }
];

// Hàm lọc thông báo dự án
export const filterProjectNotifications = (notifications, filter) => {
  switch (filter) {
    case 'unread':
      return notifications.filter(notif => !notif.isRead);
    case 'read':
      return notifications.filter(notif => notif.isRead);
    case 'all':
    default:
      return notifications;
  }
};

// Hàm đánh dấu đã đọc
export const markProjectNotificationAsRead = (notificationId) => {
  const notification = projectNotificationData.find(notif => notif.id === notificationId);
  if (notification) {
    notification.isRead = true;
  }
};

// Hàm đánh dấu tất cả đã đọc
export const markAllProjectNotificationsAsRead = () => {
  projectNotificationData.forEach(notification => {
    notification.isRead = true;
  });
};

// Hàm đánh dấu tất cả chưa đọc
export const markAllProjectNotificationsAsUnread = () => {
  projectNotificationData.forEach(notification => {
    notification.isRead = false;
  });
};
