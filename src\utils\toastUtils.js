import { toast } from 'react-toastify';

// Hàm tiện ích để gọi toast từng loại cụ thể, tránh spam bằng cách kiểm tra toast đang hiển thị
const toastOptions = {
  toastId: 'global-toast',
  autoClose: 3000, // Tự động đóng sau 3 giây
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true
};

export const showSuccess = (message) => toast.success(message, toastOptions);
export const showError = (message) => toast.error(message, toastOptions);
export const showWarning = (message) => toast.warn(message, toastOptions);
export const showInfo = (message) => toast.info(message, toastOptions);

// Hàm tiện ích để gọi toast theo từng loại
export const showToast = (message, type = 'info') => {
  switch (type) {
    case 'success':
      showSuccess(message);
      break;
    case 'error':
      showError(message);
      break;
    case 'warning':
      showWarning(message);
      break;
    default:
      showInfo(message);
  }
};
