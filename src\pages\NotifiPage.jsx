import { useState } from 'react';
import JobNoti from '../features/pages/Notifical/JobNoti';
import SysNoti from '../features/pages/Notifical/SysNoti';
import CmtNoti from '../features/pages/Notifical/CmtNoti';
import ProjectNoti from '../features/pages/Notifical/ProjectNoti';
import '../styles/NotifiPage.css';

const NotifiPage = () => {
  const [activeTab, setActiveTab] = useState('job');
  const [filter, setFilter] = useState('all');

  const tabs = [
    { id: 'job', label: 'Công việc', component: JobNoti },
    { id: 'system', label: '<PERSON>ệ thống', component: SysNoti },
    { id: 'comment', label: 'Bình luận', component: CmtNoti },
    { id: 'project', label: 'Dự án', component: ProjectNoti }
  ];

  const renderActiveComponent = () => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    if (activeTabData) {
      const Component = activeTabData.component;
      return <Component globalFilter={filter} />;
    }
    return null;
  };

  return (
    <div className="notification-page">
      <div className="notification-header">
        <div className="notification-title">
          <h1>Hộp thư đến</h1>
          <div className="notification-actions">
            <button className={`filter-btn ${filter === 'all' ? 'active' : ''}`} onClick={() => setFilter('all')}>
              Tất cả
            </button>
            <button className={`filter-btn ${filter === 'unread' ? 'active' : ''}`} onClick={() => setFilter('unread')}>
              Chưa đọc
            </button>
            <button className={`filter-btn ${filter === 'read' ? 'active' : ''}`} onClick={() => setFilter('read')}>
              Đã đọc
            </button>
            <button className="more-actions">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="1"></circle>
                <circle cx="19" cy="12" r="1"></circle>
                <circle cx="5" cy="12" r="1"></circle>
              </svg>
            </button>
          </div>
        </div>

        <div className="notification-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="notification-content">
        {renderActiveComponent()}
      </div>
    </div>
  );
};

export default NotifiPage;