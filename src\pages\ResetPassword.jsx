// Import các thư viện và component cần thiết
import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useParams } from 'react-router-dom';
import '../styles/ResetPassword.css';
import bgLogin from '../assets/login/bgLogin.jpg'; // Ảnh nền
import logo from '../assets/login/dau.svg'; // Logo
import eyeIcon from '../assets/eye.svg'; // Icon hiển thị mật khẩu
import eyeOffIcon from '../assets/eye-off.svg'; // Icon ẩn mật khẩu
import { resetPassword, forgotPassword, login } from '../api/auth.js'; // Hàm gọi API đặt lại mật khẩu
import { showSuccess, showError } from '../components/Toastify'; // Thông báo toast
import { validateResetPasswordForm } from '../utils/validation'; // Hàm validate form

const ResetPassword = () => {
  // State lưu giá trị mật khẩu mới
  const [password, setPassword] = useState('');
  // State lưu giá trị xác nhận mật khẩu
  const [confirmPassword, setConfirmPassword] = useState('');
  // State loading khi gửi form
  const [loading, setLoading] = useState(false);
  // State loading khi kiểm tra token
  const [tokenLoading, setTokenLoading] = useState(true);
  // State lưu lỗi validate
  const [errors, setErrors] = useState({});
  // State hiển thị mật khẩu
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  // State để lưu email khi token hết hạn
  const [userEmail, setUserEmail] = useState('');
  // State để lưu thông tin độ mạnh mật khẩu
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, feedback: [], level: 'weak' });
  // State để theo dõi việc submit form
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Hook điều hướng
  const navigate = useNavigate();
  // Hook lấy URL parameters và query parameters
  const { token: urlToken } = useParams();
  const [searchParams] = useSearchParams();
  const queryToken = searchParams.get('token');

  // Lấy token từ URL params hoặc query params
  const token = urlToken || queryToken;

  // Kiểm tra token khi component mount
  useEffect(() => {
    const validateToken = async () => {
      console.log('=== TOKEN VALIDATION ===');
      console.log('Raw token:', token);
      console.log('Token length:', token?.length);
      console.log('URL params token:', urlToken);
      console.log('Query params token:', queryToken);

      if (!token) {
        console.error('No token found in URL');
        showError('Không tìm thấy token đặt lại mật khẩu. Vui lòng kiểm tra link từ email.');
        setTimeout(() => {
          navigate('/login');
        }, 3000);
        return;
      }

      // Decode URL nếu cần
      let decodedToken = token;
      try {
        decodedToken = decodeURIComponent(token);
        console.log('Decoded token:', decodedToken);
      } catch (e) {
        console.log('Token không cần decode:', e.message);
      }

      // Kiểm tra format token - cho phép nhiều format khác nhau
      if (decodedToken.length < 10) {
        console.error('Token quá ngắn:', decodedToken.length);
        showError('Token không đúng định dạng. Vui lòng kiểm tra lại link từ email.');
        setTimeout(() => {
          navigate('/login');
        }, 3000);
        return;
      }

      console.log('Token validation passed');
      // Token hợp lệ, cho phép hiển thị form
      setTokenLoading(false);
    };

    validateToken();
  }, [token, navigate, urlToken, queryToken]);

  // Hàm xử lý thay đổi mật khẩu, kiểm tra hợp lệ realtime
  const handlePasswordChange = (e) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    // Kiểm tra validation
    const validationErrors = validateResetPasswordForm({
      password: newPassword,
      confirmPassword
    });
    setErrors((prev) => ({ ...prev, password: validationErrors.password }));

    // Kiểm tra độ mạnh mật khẩu
    if (newPassword) {
      const strength = checkPasswordStrength(newPassword);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ score: 0, feedback: [], level: 'weak' });
    }
  };

  // Hàm xử lý thay đổi xác nhận mật khẩu, kiểm tra hợp lệ realtime
  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
    const validationErrors = validateResetPasswordForm({
      password,
      confirmPassword: e.target.value
    });
    setErrors((prev) => ({ ...prev, confirmPassword: validationErrors.confirmPassword }));
  };

  // Hàm kiểm tra độ mạnh của mật khẩu
  const checkPasswordStrength = (password) => {
    const strength = {
      score: 0,
      feedback: [],
      level: 'weak'
    };

    if (password.length >= 8) {
      strength.score += 1;
    } else {
      strength.feedback.push('Mật khẩu nên có ít nhất 8 ký tự');
    }

    if (/[A-Z]/.test(password)) {
      strength.score += 1;
    } else {
      strength.feedback.push('Nên có ít nhất 1 chữ hoa');
    }

    if (/[a-z]/.test(password)) {
      strength.score += 1;
    } else {
      strength.feedback.push('Nên có ít nhất 1 chữ thường');
    }

    if (/[0-9]/.test(password)) {
      strength.score += 1;
    } else {
      strength.feedback.push('Nên có ít nhất 1 số');
    }

    if (/[!@#$%&*_.+=-]/.test(password)) {
      strength.score += 1;
    } else {
      strength.feedback.push('Nên có ít nhất 1 ký tự đặc biệt (!@#$%&*_.+=-)');
    }

    // Xác định level
    if (strength.score >= 4) {
      strength.level = 'strong';
    } else if (strength.score >= 2) {
      strength.level = 'medium';
    } else {
      strength.level = 'weak';
    }

    return strength;
  };

  // Hàm xử lý submit form đặt lại mật khẩu
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Kiểm tra nếu đang submit để tránh double submit
    if (isSubmitting || loading) {
      return;
    }

    // Validate form
    const validationErrors = validateResetPasswordForm({ password, confirmPassword });
    setErrors(validationErrors);

    if (validationErrors.password || validationErrors.confirmPassword) {
      showError('Vui lòng kiểm tra lại thông tin nhập vào.');
      return;
    }

    // Kiểm tra độ mạnh mật khẩu (khuyến nghị)
    const strength = checkPasswordStrength(password);
    if (strength.level === 'weak') {
      const confirmWeak = window.confirm(
        'Mật khẩu của bạn còn yếu. Bạn có muốn tiếp tục không?\n\n' +
        'Khuyến nghị: ' + strength.feedback.join(', ')
      );
      if (!confirmWeak) {
        return;
      }
    }

    setLoading(true);
    setIsSubmitting(true);

    try {
      console.log('Đang đặt lại mật khẩu với token:', token.substring(0, 10) + '...');



      // Gọi API đặt lại mật khẩu
      const resetResult = await resetPassword(token, password);
      console.log('Kết quả đặt lại mật khẩu:', resetResult);

      // Hiển thị thông báo thành công
      showSuccess('Đặt lại mật khẩu thành công!');

      // Lấy email từ response
      let emailForLogin = resetResult?.email || resetResult?.user?.email || resetResult?.data?.email;

      if (!emailForLogin) {
        // Nếu không có email trong response, chuyển về trang đăng nhập
        showSuccess('Đặt lại mật khẩu thành công! Vui lòng đăng nhập với mật khẩu mới.');
        setTimeout(() => {
          navigate('/login');
        }, 2500);
        return;
      }

      // Thử đăng nhập tự động
      await attemptAutoLogin(emailForLogin, password);

    } catch (err) {
      console.error('Lỗi đặt lại mật khẩu:', err);
      handleResetPasswordError(err);
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  // Hàm thử đăng nhập tự động
  const attemptAutoLogin = async (email, password) => {
    try {
      showSuccess('Đang đăng nhập tự động...');

      const loginResult = await login(email, password);
      console.log('Kết quả đăng nhập tự động:', loginResult);

      // Lưu thông tin đăng nhập
      if (loginResult?.token) {
        localStorage.setItem('authToken', loginResult.token);
      }
      if (loginResult?.user) {
        localStorage.setItem('user', JSON.stringify(loginResult.user));
      }

      showSuccess('Đăng nhập thành công! Đang chuyển về trang chủ...');
      setTimeout(() => {
        navigate('/');
      }, 1500);

    } catch (loginErr) {
      console.error('Đăng nhập tự động thất bại:', loginErr);
      showSuccess('Đặt lại mật khẩu thành công! Vui lòng đăng nhập với mật khẩu mới.');
      setTimeout(() => {
        navigate('/login');
      }, 2500);
    }
  };

  // Hàm xử lý lỗi đặt lại mật khẩu
  const handleResetPasswordError = (err) => {
    let errorMessage = 'Đặt lại mật khẩu thất bại.';

    if (err.message.toLowerCase().includes('token')) {
      errorMessage = 'Token không hợp lệ hoặc đã hết hạn. Vui lòng yêu cầu đặt lại mật khẩu mới từ trang đăng nhập.';
    } else if (err.message.toLowerCase().includes('password')) {
      errorMessage = 'Mật khẩu không đáp ứng yêu cầu bảo mật của hệ thống.';
    } else if (err.message.toLowerCase().includes('network') ||
               err.message.toLowerCase().includes('fetch') ||
               err.message.toLowerCase().includes('connection')) {
      errorMessage = 'Không thể kết nối tới máy chủ. Vui lòng kiểm tra kết nối mạng và thử lại.';
    } else if (err.message) {
      errorMessage = err.message;
    }

    showError(errorMessage);
  };

  // Hiển thị loading khi đang kiểm tra token
  if (tokenLoading) {
    return (
      <div className="reset-container">
        <div className="reset-right">
          <div className="reset-form-wrapper">
            <img src={logo} alt="Logo" className="reset-logo" style={{width:60, height:60, marginBottom:28}} />
            <h2 className="reset-title">Đang kiểm tra...</h2>
            <p className="reset-desc">Vui lòng đợi trong giây lát</p>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '50px'
            }}>
              <div style={{
                width: '20px',
                height: '20px',
                border: '2px solid #f3f3f3',
                borderTop: '2px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            </div>
          </div>
        </div>
        {/* Toast được hiển thị thông qua ToastContainer trong App.jsx */}
      </div>
    );
  }

  return (
    <div className="reset-container">
      {/* Bên trái: Ảnh minh hoạ */}
      <div className="reset-left">
        <img
          src={bgLogin}
          alt="Reset Password Illustration"
          className="reset-illustration"
        />
      </div>
      {/* Bên phải: Form đặt lại mật khẩu */}
      <div className="reset-right">
        <div className="reset-form-wrapper">
          <div className="reset-back-row">
            {/* Nút quay lại trang đăng nhập */}
            <a href="#" className="reset-back-link"
            onClick={e => { e.preventDefault();
            navigate('/login'); }}>
            &larr; Quay lại
            </a>
          </div>
          {/* Logo */}
          <img src={logo} alt="Logo" className="reset-logo" style={{width:60, height:60, marginBottom:28}} />
          <h2 className="reset-title">Đặt lại mật khẩu</h2>
          <p className="reset-desc">Vui lòng nhập mật khẩu mới cho tài khoản của bạn</p>

          <form className="reset-form" onSubmit={handleSubmit}>
            {/* Trường mật khẩu mới */}
            <label className="reset-label">Mật khẩu mới</label>
            <div className="reset-input-validation-wrapper">
              <div className="reset-password-wrapper">
                <input
                  type={showPassword ? "text" : "password"}
                  className={`reset-input${errors.password ? ' reset-input-error' : ''}`}
                  placeholder="Nhập mật khẩu mới"
                  value={password}
                  onChange={handlePasswordChange}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  className="reset-password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                  title={showPassword ? "Ẩn mật khẩu" : "Hiển thị mật khẩu"}
                >
                  <img
                    src={showPassword ? eyeOffIcon : eyeIcon}
                    alt={showPassword ? "Ẩn mật khẩu" : "Hiển thị mật khẩu"}
                    className="reset-eye-icon"
                  />
                </button>
              </div>
              {/* Hiển thị lỗi validate mật khẩu nếu có */}
              {errors.password && <div className="reset-validation-error-message">{errors.password}</div>}

              {/* Hiển thị độ mạnh mật khẩu */}
              {password && (
                <div style={{ marginTop: '8px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginBottom: '4px'
                  }}>
                    <span style={{ fontSize: '12px', color: '#6b7280' }}>Độ mạnh:</span>
                    <div style={{
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '11px',
                      fontWeight: '500',
                      backgroundColor: passwordStrength.level === 'strong' ? '#dcfce7' :
                                     passwordStrength.level === 'medium' ? '#fef3c7' : '#fee2e2',
                      color: passwordStrength.level === 'strong' ? '#166534' :
                             passwordStrength.level === 'medium' ? '#92400e' : '#dc2626'
                    }}>
                      {passwordStrength.level === 'strong' ? 'Mạnh' :
                       passwordStrength.level === 'medium' ? 'Trung bình' : 'Yếu'}
                    </div>
                  </div>
                  {passwordStrength.feedback.length > 0 && (
                    <div style={{ fontSize: '11px', color: '#6b7280', lineHeight: '1.4' }}>
                      {passwordStrength.feedback.join(', ')}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Trường xác nhận mật khẩu */}
            <label className="reset-label">Xác nhận mật khẩu</label>
            <div className="reset-input-validation-wrapper">
              <div className="reset-password-wrapper">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  className={`reset-input${errors.confirmPassword ? ' reset-input-error' : ''}`}
                  placeholder="Nhập lại mật khẩu mới"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  className="reset-password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  title={showConfirmPassword ? "Ẩn mật khẩu" : "Hiển thị mật khẩu"}
                >
                  <img
                    src={showConfirmPassword ? eyeOffIcon : eyeIcon}
                    alt={showConfirmPassword ? "Ẩn mật khẩu" : "Hiển thị mật khẩu"}
                    className="reset-eye-icon"
                  />
                </button>
              </div>
              {/* Hiển thị lỗi validate xác nhận mật khẩu nếu có */}
              {errors.confirmPassword && <div className="reset-validation-error-message">{errors.confirmPassword}</div>}
            </div>

            <button
              type="submit"
              className="reset-btn"
              disabled={loading || isSubmitting || !password || !confirmPassword}
              style={{
                opacity: (loading || isSubmitting || !password || !confirmPassword) ? 0.6 : 1,
                cursor: (loading || isSubmitting || !password || !confirmPassword) ? 'not-allowed' : 'pointer'
              }}
            >
              {loading || isSubmitting ? (
                <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid transparent',
                    borderTop: '2px solid currentColor',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Đang xử lý...
                </span>
              ) : (
                'Đặt lại mật khẩu →'
              )}
            </button>
          </form>
        </div>
      </div>
      {/* Toast được hiển thị thông qua ToastContainer trong App.jsx */}
    </div>
  );
};

export default ResetPassword; // Export component để sử dụng ở nơi khác
