import React, { useState, useEffect, useRef } from 'react';
import '../../styles/UserCreate.css';
import { departments } from '../../storage/hrOptions';
import usersIcon from '../../assets/users.svg';
import documentIcon from '../../assets/document.svg';
import lockIcon from '../../assets/lock.svg';
import eyeIcon from '../../assets/eye.svg';
import eyeOffIcon from '../../assets/eye-off.svg';
import dropdownIcon from '../../assets/icon-sidebar/dropdown.svg';
import { getDepartmentsList } from '../../api/departmentManagement';
import { createUser, checkDepartmentHead } from '../../api/userManagement';
import { validateUserCreateForm, validatePassword, validateConfirmPassword } from '../../utils/validation';
import { showSuccess, showError } from '../../components/Toastify';

// Hàm fetchRoles giả lập, sau này thay bằng API thật nếu có
const roleHierarchy = ['staff', 'leader', 'departmentHead', 'hr', 'admin'];

const fetchRoles = async (currentUserRole) => {
  const allRoles = [
    { value: 'staff', label: 'Nhân viên' },
    { value: 'leader', label: 'Trưởng nhóm' },
    { value: 'departmentHead', label: 'Trưởng phòng' },
    { value: 'hr', label: 'Nhân sự' },
  ];
  if (!currentUserRole) return allRoles;
  const currentIndex = roleHierarchy.indexOf(currentUserRole);
  // Chỉ lấy các role thấp hơn role hiện tại, không bao giờ có admin
  return allRoles.filter(role => roleHierarchy.indexOf(role.value) < currentIndex);
};

const UserCreate = ({ onClose, onSuccess }) => {
  const [form, setForm] = useState({
    name: '',
    email: '',
    department: '',
    position: '',
    role: '',
    password: '',
    confirmPassword: '',
  });
  const [departments, setDepartments] = useState([]);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [roleOptions, setRoleOptions] = useState([]);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null); // 'department' | 'role' | null
  const dropdownRef = useRef(null);
  const modalRef = useRef(null);
  const [validationErrors, setValidationErrors] = useState({});
  const timeoutRef = useRef(null);

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const currentUserRole = user.role?.toLowerCase();
    fetchRoles(currentUserRole).then(setRoleOptions);
    getDepartmentsList().then(res => {
      if (res && res.data) setDepartments(res.data);
    });
  }, []);

  // Handle click outside dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdown(null);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle click outside modal to close
  useEffect(() => {
    function handleModalClickOutside(event) {
      if (modalRef.current && event.target === modalRef.current) {
        if (!isCreating) {
          onClose();
        }
      }
    }
    document.addEventListener("mousedown", handleModalClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleModalClickOutside);
    };
  }, [isCreating, onClose]);

  // Cleanup timeout khi component unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const generateTemporaryPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);
    setValidationErrors({});
    if (isCreating) return;

    // Validate form
    const errors = validateUserCreateForm({
      name: form.name,
      email: form.email,
      department: form.department,
      position: form.position,
      role: form.role,
    });

    // Validate mật khẩu nếu user nhập
    if (form.password) {
      const passwordError = validatePassword(form.password);
      if (passwordError) {
        errors.password = passwordError;
      }
      
      const confirmPasswordError = validateConfirmPassword(form.password, form.confirmPassword);
      if (confirmPasswordError) {
        errors.confirmPassword = confirmPasswordError;
      }
    }

    // Check if there are any errors
    const hasErrors = Object.values(errors).some(error => error !== '');
    if (hasErrors) {
      setValidationErrors(errors);
      return;
    }

    // Kiểm tra nếu role là departmentHead, phòng ban đó không được có trưởng phòng khác
    if (form.role === 'departmentHead' && form.department) {
      try {
        const checkResult = await checkDepartmentHead(form.department);
        if (!checkResult.success && checkResult.data?.hasHead) {
          const errorMsg = `${checkResult.message}. Vui lòng chọn phòng ban khác hoặc role khác.`;
          setError(errorMsg);
          showError(errorMsg);
          return;
        }
      } catch (err) {
        const errorMsg = 'Không thể kiểm tra trạng thái trưởng phòng. Vui lòng thử lại.';
        setError(errorMsg);
        showError(errorMsg);
        return;
      }
    }

    setIsCreating(true);
    // Map lại tên trường cho đúng API
    let submitData = {
      fullName: form.name,
      email: form.email,
      role: form.role,
      position: form.position,
      departmentCode: form.department,
      password: form.password,
      confirmPassword: form.confirmPassword,
    };
    if (!submitData.password) {
      submitData.password = generateTemporaryPassword();
      submitData.confirmPassword = submitData.password;
    }
    try {
      const result = await createUser(submitData);
      setSuccess(true);
      showSuccess('Tạo tài khoản thành công!');
      setForm({ name: '', email: '', department: '', position: '', role: '', password: '', confirmPassword: '' });
      
      // Gọi callback để thông báo thành công cho component cha
      if (onSuccess) {
        onSuccess(result.data || result.user || submitData);
      }
      
      // Đóng modal sau 1.5 giây để user thấy thông báo thành công
      timeoutRef.current = setTimeout(() => {
        onClose();
      }, 1500);
      
    } catch (err) {
      const errorMessage = err.message || 'Có lỗi xảy ra';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      // Clear timeout nếu có
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      onClose();
    }
  };

  // Password validation logic
  const passwordChecks = [
    { label: 'Ít nhất 8 ký tự', valid: form.password.length >= 8 },
    { label: 'Chữ hoa', valid: /[A-Z]/.test(form.password) },
    { label: 'Ký tự đặc biệt', valid: /[^A-Za-z0-9]/.test(form.password) },
    { label: 'Chữ', valid: /[A-Za-z]/.test(form.password) },
    { label: 'Số', valid: /[0-9]/.test(form.password) },
    { label: 'Không chứa khoảng trắng', valid: !/\s/.test(form.password) },
  ];

  // Determine status for each check: pending (gray), invalid (red), valid (green)
  const getCheckStatus = (check) => {
    if (!form.password) return 'pending';
    return check.valid ? 'valid' : 'invalid';
  };

  return (
    <div className="user-create-modal" ref={modalRef}>
      <div className="user-create-dialog">
        <div className="user-create-header">
          <div>
            <div className="user-create-title">Thêm nhân sự mới</div>
            <div className="user-create-desc">Vui lòng điền đầy đủ thông tin để tạo tài khoản</div>
          </div>
          <button className="user-create-close" onClick={handleClose} disabled={isCreating}>×</button>
        </div>
        <form className="user-create-form" onSubmit={handleSubmit} ref={dropdownRef} noValidate>
          {/* Thông tin cá nhân */}
          <div className="user-create-section">
            <div className="user-create-section-title">
              <img class="icon-blue" src={usersIcon} alt="user" /> Thông tin cá nhân
            </div>
            <div className="user-create-row">
              <div className="user-create-group">
                <label>Họ và tên<span className="required">*</span></label>
                <input
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  placeholder="Nhập họ và tên đầy đủ"
                  className={validationErrors.name ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.name || ''}</span>
              </div>
              <div className="user-create-group">
                <label>Email<span className="required">*</span></label>
                <input
                  name="email"
                  value={form.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  className={validationErrors.email ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.email || ''}</span>
              </div>
            </div>
          </div>
          {/* Thông tin công việc */}
          <div className="user-create-section">
            <div className="user-create-section-title">
              <img class="icon-blue" src={documentIcon} alt="work" /> Thông tin công việc
            </div>
            <div className="user-create-row">
              <div className="user-create-group">
                <label>Phòng ban<span className="required">*</span></label>
                <div className={`user-create-dropdown ${openDropdown === 'department' ? 'open' : ''}`}>
                  <button
                    type="button"
                    className={`user-create-dropdown-btn ${validationErrors.department ? 'error' : ''}`}
                    onClick={() => setOpenDropdown(openDropdown === 'department' ? null : 'department')}
                  >
                    <span>
                      {form.department
                        ? departments.find(dep => dep.code === form.department)?.name || 'Chọn phòng ban'
                        : 'Chọn phòng ban'
                      }
                    </span>
                    <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
                  </button>
                  {openDropdown === 'department' && (
                    <div className="user-create-dropdown-menu">
                      {departments.map((dep) => (
                        <div
                          key={dep.code}
                          className="user-create-dropdown-item"
                          onClick={() => {
                            setForm(prev => ({ ...prev, department: dep.code }));
                            setOpenDropdown(null);
                            // Clear validation error when user selects
                            if (validationErrors.department) {
                              setValidationErrors(prev => ({ ...prev, department: '' }));
                            }
                          }}
                        >
                          {dep.name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <span className="field-error">{validationErrors.department || ''}</span>
              </div>
              <div className="user-create-group">
                <label>Vị trí<span className="required">*</span></label>
                <input
                  name="position"
                  value={form.position}
                  onChange={handleChange}
                  placeholder="Vị trí"
                  className={validationErrors.position ? 'error' : ''}
                />
                <span className="field-error">{validationErrors.position || ''}</span>
              </div>
              <div className="user-create-group">
                <label>Chức vụ và phân quyền<span className="required">*</span></label>
                <div className={`user-create-dropdown ${openDropdown === 'role' ? 'open' : ''}`}>
                  <button
                    type="button"
                    className={`user-create-dropdown-btn ${validationErrors.role ? 'error' : ''}`}
                    onClick={() => setOpenDropdown(openDropdown === 'role' ? null : 'role')}
                  >
                    <span>
                      {form.role
                        ? roleOptions.find(role => role.value === form.role)?.label || 'Chọn chức vụ'
                        : 'Chọn chức vụ'
                      }
                    </span>
                    <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
                  </button>
                  {openDropdown === 'role' && (
                    <div className="user-create-dropdown-menu">
                      {roleOptions.map((role) => (
                        <div
                          key={role.value}
                          className="user-create-dropdown-item"
                          onClick={() => {
                            setForm(prev => ({ ...prev, role: role.value }));
                            setOpenDropdown(null);
                            // Clear validation error when user selects
                            if (validationErrors.role) {
                              setValidationErrors(prev => ({ ...prev, role: '' }));
                            }
                          }}
                        >
                          {role.label}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <span className="field-error">{validationErrors.role || ''}</span>
              </div>
            </div>
          </div>
          {/* Thông tin bảo mật */}
          <div className="user-create-section">
            <div className="user-create-section-title">
              <img class="icon-blue" src={lockIcon} alt="lock" /> Thông tin bảo mật
            </div>
            <div className="user-create-row">
              <div className="user-create-group">
                <label>Mật khẩu (để trống để tạo mật khẩu tạm)</label>
                <div className="user-create-password-wrap">
                  <input
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={form.password}
                    onChange={handleChange}
                    placeholder="Nhập mật khẩu"
                  className={validationErrors.password ? 'error' : ''}
                  />
                  <span className="user-create-eye" onClick={() => setShowPassword(v => !v)}>
                    <img src={showPassword ? eyeOffIcon : eyeIcon} alt="eye" style={{width:18,height:18}} />
                  </span>
                </div>
                <span className="field-error">{validationErrors.password || ''}</span>
                {/* {form.password && (
                  <div className="user-create-password-checks">
                    {passwordChecks.map((c, i) => (
                      <div
                        key={i}
                        className={`password-check ${getCheckStatus(c)}`}
                      >
                        {getCheckStatus(c) === 'valid'
                          ? '✔'
                          : getCheckStatus(c) === 'invalid'
                          ? '✖'
                          : '•'}{' '}
                        {c.label}
                      </div>
                    ))}
                  </div>
                )} */}
              </div>
              <div className="user-create-group">
                <label>Xác nhận lại mật khẩu</label>
                <div className="user-create-password-wrap">
                  <input
                    name="confirmPassword"
                    type={showConfirm ? 'text' : 'password'}
                    value={form.confirmPassword}
                    onChange={handleChange}
                    placeholder="Nhập lại mật khẩu"
                  className={validationErrors.confirmPassword ? 'error' : ''}
                  />
                  <span className="user-create-eye" onClick={() => setShowConfirm(v => !v)}>
                    <img src={showConfirm ? eyeOffIcon : eyeIcon} alt="eye" style={{width:18,height:18}} />
                  </span>
                </div>
                <span className="field-error">{validationErrors.confirmPassword || ''}</span>
              </div>
            </div>
          </div>
          <button className="user-create-submit" type="submit" disabled={isCreating}>
            {isCreating ? 'Đang tạo tài khoản...' : 'Đăng ký tài khoản'}
          </button>
          {error && (
            <div className="user-create-error">
              {error}
            </div>
          )}
          {success && (
            <div className="user-create-success">
              Tài khoản đã được tạo thành công!
            </div>
          )}
          <div className="user-create-note">*Các trường bắt buộc</div>
        </form>
      </div>
    </div>
  );
};

export default UserCreate;