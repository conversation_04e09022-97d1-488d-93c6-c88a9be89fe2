import React, { useEffect, useState } from 'react';
// Removed Sidebar and Topbar imports as they're now handled by DashboardLayout
import StickyBackground from '../../../assets/StickyBackground.svg';
import NoteIcon from '../../../assets/icon-sidebar/ghichu.svg';
import AttachmentIcon from '../../../assets/attachment.svg';
import DocsIcon from '../../../assets/docs.svg';
import PdfIcon from '../../../assets/pdf.svg';
import ImageIcon from '../../../assets/image.svg';
import UpdateNote from '../../components/NoteUpdate';
import CreateNote from '../../components/NoteCreate';
import searchIcon from '../../../assets/search.svg';
import { PERSONAL_NOTES_ENDPOINTS } from '../../../api/endpoints';
import '../../../styles/PersonalNotes.css';

const getToken = () => localStorage.getItem('token');

const PersonalNotes = () => {
  const [notes, setNotes] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateNote, setShowCreateNote] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);
  const [showUpdateNote, setShowUpdateNote] = useState(false);

  // Hàm để chọn icon dựa trên loại file
  const getFileIcon = (fileName) => {
    const ext = (fileName || '').split('.').pop().toLowerCase();
    if (ext === 'pdf') return PdfIcon;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return ImageIcon;
    if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'].includes(ext)) return DocsIcon;
    return DocsIcon; // Mặc định cho các loại khác
  };

  // Hàm để lấy icon chính cho ghi chú (dựa trên file đầu tiên)
  const getNoteMainIcon = (attachments) => {
    if (!attachments || attachments.length === 0) return null;
    const firstFile = attachments[0];
    return getFileIcon(firstFile.name);
  };

  // Fetch notes from API when component mounts
  useEffect(() => {
    fetchNotes();
  }, []);

  // Disable page scroll for this component
  useEffect(() => {
    const dashboardMain = document.querySelector('.dashboard-main');
    if (dashboardMain) {
      dashboardMain.classList.add('no-page-scroll');
    }

    return () => {
      if (dashboardMain) {
        dashboardMain.classList.remove('no-page-scroll');
      }
    };
  }, []);

  const fetchNotes = async () => {
    try {
      const res = await fetch(PERSONAL_NOTES_ENDPOINTS.GET_NOTES, {
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });
      const data = await res.json();
      let notesArr = [];
      if (data && data.data && Array.isArray(data.data.tasks)) notesArr = data.data.tasks;
      else if (data && Array.isArray(data.tasks)) notesArr = data.tasks;
      else if (Array.isArray(data)) notesArr = data;
      // Nếu không có gì, trả về mảng rỗng
      const mapped = notesArr.map(note => ({
        id: note._id || note.id,
        title: note.title,
        content: note.description,
        date: note.date || note.createdAt || '',
        attachments: note.files || note.attachments || [],
      }));
      setNotes(mapped);
    } catch (error) {
      setNotes([]);
    }
  };

  // Lọc ghi chú theo từ khóa tìm kiếm
  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Hàm để cập nhật danh sách khi có dữ liệu mới từ component con
  const handleNoteCreated = async (updatedNoteData) => {
    // Cập nhật danh sách ngay lập tức
    await fetchNotes();
  };

  // Khi click vào ghi chú để chỉnh sửa
  const handleNoteClick = (note) => {
    setSelectedNote(note);
    setShowUpdateNote(true);
  };

  // Cập nhật ghi chú
  const handleUpdateNote = async (updatedNote) => {
    if (!selectedNote) return;
    try {
      const res = await fetch(PERSONAL_NOTES_ENDPOINTS.UPDATE_NOTE(selectedNote.id), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: updatedNote.title,
          description: updatedNote.content,
        }),
      });
      if (res.ok) {
        await fetchNotes();
        setShowUpdateNote(false);
        setSelectedNote(null);
      }
    } catch (error) {}
  };

  // Xóa ghi chú
  const handleDeleteNote = async (noteId) => {
    try {
      const res = await fetch(PERSONAL_NOTES_ENDPOINTS.DELETE_NOTE(noteId), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
      });
      if (res.ok) {
        await fetchNotes();
        setShowUpdateNote(false);
        setSelectedNote(null);
      }
    } catch (error) {}
  };

  // Xử lý khi người dùng nhấn Enter trong ô tìm kiếm
  const handleSearchKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className="personal-notes-container">
            <div className="personal-notes-header">
              <div className="header-left">
                <img src={NoteIcon} alt="Ghi chú" className="notes-icon" />
                <h1 className="notes-title">Ghi chú cá nhân</h1>
              </div>
              <div className="notes-search">
                <input
                  type="text"
                  placeholder="Tìm kiếm ghi chú, mô tả"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleSearchKeyDown}
                />
                  <img src={searchIcon} alt="Search" className="searc-icon" />
              </div>
            </div>
            
            <div className="notes-grid">
              <div className="note-card new-note-card" onClick={() => setShowCreateNote(true)}>
                <img src={StickyBackground} alt="" className="sticky-background" />
                <div className="note-content">
                  <h3>Ghi chú mới....</h3>
                </div>
              </div>

              {filteredNotes.map((note) => (
                <div className="note-card" key={note.id} onClick={() => handleNoteClick(note)}>
                  <img src={StickyBackground} alt="" className="sticky-background" />
                  <div className="note-content">
                    <h3>{note.title}</h3>
                    <p>{note.content}</p>
                    {/* Hiển thị số lượng file đính kèm */}
                    <div className="note-attachments-list" style={{ marginTop: 30, display: 'flex', alignItems: 'center', gap: 6 }}>
                      {Array.isArray(note.attachments) && note.attachments.length > 0 ? (
                        <>
                          <img src={getNoteMainIcon(note.attachments) || DocsIcon} alt="file" style={{ width: 16, height: 16, marginRight: 2 }} />
                          <span style={{ 
                            fontSize: 12, 
                            color: '#555',
                            maxWidth: '150px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            display: 'inline-block'
                          }}>
                            {note.attachments.length === 1 
                              ? note.attachments[0].name 
                              : `${note.attachments.length} tệp đính kèm`}
                          </span>
                        </>
                      ) : (
                        <span style={{ fontSize: 12, color: '#888' }}>Không có tài liệu đính kèm</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {showCreateNote && (
              <CreateNote
                onClose={() => setShowCreateNote(false)}
                onNoteCreated={handleNoteCreated}
              />
            )}

            {showUpdateNote && selectedNote && (
              <UpdateNote 
                note={selectedNote}
                onClose={() => setShowUpdateNote(false)}
                onUpdate={handleUpdateNote}
                onDelete={handleDeleteNote}
                onRefresh={fetchNotes}
              />
            )}
    </div>
  );
};

export default PersonalNotes;
